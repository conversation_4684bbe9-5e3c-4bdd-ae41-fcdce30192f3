var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};

// index.ts
import { bbox } from "@turf/bbox";
import { area } from "@turf/area";
import { booleanPointInPolygon } from "@turf/boolean-point-in-polygon";
import { explode } from "@turf/explode";
import { collectionOf as collectionOf2 } from "@turf/invariant";
import {
  polygon,
  multiPolygon,
  featureCollection,
  isObject as isObject2
} from "@turf/helpers";

// lib/grid-to-matrix.js
import { getCoords, collectionOf } from "@turf/invariant";
import { featureEach } from "@turf/meta";
import { isObject } from "@turf/helpers";
function gridToMatrix(grid, options) {
  options = options || {};
  if (!isObject(options)) throw new Error("options is invalid");
  var zProperty = options.zProperty || "elevation";
  var flip = options.flip;
  var flags = options.flags;
  collectionOf(grid, "Point", "input must contain Points");
  var pointsMatrix = sortPointsByLatLng(grid, flip);
  var matrix = [];
  for (var r = 0; r < pointsMatrix.length; r++) {
    var pointRow = pointsMatrix[r];
    var row = [];
    for (var c = 0; c < pointRow.length; c++) {
      var point = pointRow[c];
      if (point.properties[zProperty]) row.push(point.properties[zProperty]);
      else row.push(0);
      if (flags === true) point.properties.matrixPosition = [r, c];
    }
    matrix.push(row);
  }
  return matrix;
}
function sortPointsByLatLng(points, flip) {
  var pointsByLatitude = {};
  featureEach(points, function(point) {
    var lat = getCoords(point)[1];
    if (!pointsByLatitude[lat]) pointsByLatitude[lat] = [];
    pointsByLatitude[lat].push(point);
  });
  var orderedRowsByLatitude = Object.keys(pointsByLatitude).map(function(lat) {
    var row = pointsByLatitude[lat];
    var rowOrderedByLongitude = row.sort(function(a, b) {
      return getCoords(a)[0] - getCoords(b)[0];
    });
    return rowOrderedByLongitude;
  });
  var pointMatrix = orderedRowsByLatitude.sort(function(a, b) {
    if (flip) return getCoords(a[0])[1] - getCoords(b[0])[1];
    else return getCoords(b[0])[1] - getCoords(a[0])[1];
  });
  return pointMatrix;
}

// index.ts
import { isoBands } from "marchingsquares";
function isobands(pointGrid, breaks, options) {
  options = options || {};
  if (!isObject2(options)) throw new Error("options is invalid");
  const zProperty = options.zProperty || "elevation";
  const commonProperties = options.commonProperties || {};
  const breaksProperties = options.breaksProperties || [];
  collectionOf2(pointGrid, "Point", "Input must contain Points");
  if (!breaks) throw new Error("breaks is required");
  if (!Array.isArray(breaks)) throw new Error("breaks is not an Array");
  if (!isObject2(commonProperties))
    throw new Error("commonProperties is not an Object");
  if (!Array.isArray(breaksProperties))
    throw new Error("breaksProperties is not an Array");
  const matrix = gridToMatrix(pointGrid, { zProperty, flip: true });
  let contours = createContourLines(matrix, breaks, zProperty);
  contours = rescaleContours(contours, matrix, pointGrid);
  const multipolygons = contours.map((contour, index) => {
    if (breaksProperties[index] && !isObject2(breaksProperties[index])) {
      throw new Error("Each mappedProperty is required to be an Object");
    }
    const contourProperties = __spreadValues(__spreadValues({}, commonProperties), breaksProperties[index]);
    contourProperties[zProperty] = contour[zProperty];
    const multiP = multiPolygon(
      contour.groupedRings,
      contourProperties
    );
    return multiP;
  });
  return featureCollection(multipolygons);
}
function createContourLines(matrix, breaks, property) {
  const contours = [];
  for (let i = 1; i < breaks.length; i++) {
    const lowerBand = +breaks[i - 1];
    const upperBand = +breaks[i];
    const isobandsCoords = isoBands(matrix, lowerBand, upperBand - lowerBand);
    const nestedRings = orderByArea(isobandsCoords);
    const groupedRings = groupNestedRings(nestedRings);
    contours.push({
      groupedRings,
      [property]: lowerBand + "-" + upperBand
    });
  }
  return contours;
}
function rescaleContours(contours, matrix, points) {
  const gridBbox = bbox(points);
  const originalWidth = gridBbox[2] - gridBbox[0];
  const originalHeigth = gridBbox[3] - gridBbox[1];
  const x0 = gridBbox[0];
  const y0 = gridBbox[1];
  const matrixWidth = matrix[0].length - 1;
  const matrixHeight = matrix.length - 1;
  const scaleX = originalWidth / matrixWidth;
  const scaleY = originalHeigth / matrixHeight;
  return contours.map(function(contour) {
    contour.groupedRings = contour.groupedRings.map(
      function(lineRingSet) {
        return lineRingSet.map(function(lineRing) {
          return lineRing.map((point) => [
            point[0] * scaleX + x0,
            point[1] * scaleY + y0
          ]);
        });
      }
    );
    return contour;
  });
}
function orderByArea(ringsCoords) {
  const ringsWithArea = ringsCoords.map(function(coords) {
    return { ring: coords, area: area(polygon([coords])) };
  });
  ringsWithArea.sort(function(a, b) {
    return b.area - a.area;
  });
  return ringsWithArea.map(function(x) {
    return x.ring;
  });
}
function groupNestedRings(orderedLinearRings) {
  const lrList = orderedLinearRings.map((lr) => {
    return { lrCoordinates: lr, grouped: false };
  });
  const groupedLinearRingsCoords = [];
  while (!allGrouped(lrList)) {
    for (let i = 0; i < lrList.length; i++) {
      if (!lrList[i].grouped) {
        const group = [];
        group.push(lrList[i].lrCoordinates);
        lrList[i].grouped = true;
        const outerMostPoly = polygon([lrList[i].lrCoordinates]);
        for (let j = i + 1; j < lrList.length; j++) {
          if (!lrList[j].grouped) {
            const lrPoly = polygon([lrList[j].lrCoordinates]);
            if (isInside(lrPoly, outerMostPoly)) {
              group.push(lrList[j].lrCoordinates);
              lrList[j].grouped = true;
            }
          }
        }
        groupedLinearRingsCoords.push(group);
      }
    }
  }
  return groupedLinearRingsCoords;
}
function isInside(testPolygon, targetPolygon) {
  const points = explode(testPolygon);
  for (let i = 0; i < points.features.length; i++) {
    if (!booleanPointInPolygon(points.features[i], targetPolygon)) {
      return false;
    }
  }
  return true;
}
function allGrouped(list) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].grouped === false) {
      return false;
    }
  }
  return true;
}
var turf_isobands_default = isobands;
export {
  turf_isobands_default as default,
  isobands
};
//# sourceMappingURL=index.js.map