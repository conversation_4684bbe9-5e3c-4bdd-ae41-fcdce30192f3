{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-intersects/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,yDAAgC;AAChC,kCAA4B;AA2B5B,SAAS,iBAAA,CACP,QAAA,EACA,QAAA,EACA;AAAA,EACE,wBAAA,EAA0B;AAC5B,EAAA,EAEI,CAAC,CAAA,EACL;AACA,EAAA,IAAI,KAAA,EAAO,KAAA;AACX,EAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,IAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,MAAA,GAAA,CAAI,KAAA,IAAS,IAAA,EAAM;AACjB,QAAA,OAAO,IAAA;AAAA,MACT;AACA,MAAA,KAAA,EAAO,CAAC,8CAAA,QAAgB,CAAS,QAAA,EAAU,QAAA,CAAS,QAAA,EAAU;AAAA,QAC5D;AAAA,MACF,CAAC,CAAA;AAAA,IACH,CAAC,CAAA;AAAA,EACH,CAAC,CAAA;AACD,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,gCAAA,EAAQ,iBAAA;ADjCf;AACE;AACA;AACF,iGAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-intersects/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Geometry } from \"geojson\";\nimport { booleanDisjoint } from \"@turf/boolean-disjoint\";\nimport { flattenEach } from \"@turf/meta\";\n\n/**\n * Boolean-intersects returns (TRUE) if the intersection of the two geometries is NOT an empty set.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.ignoreSelfIntersections=true] ignore self-intersections on input features\n * @returns {boolean} true if geometries intersect, false otherwise\n * @example\n * var point1 = turf.point([2, 2]);\n * var point2 = turf.point([1, 2]);\n * var line = turf.lineString([[1, 1], [1, 3], [1, 4]]);\n *\n * turf.booleanIntersects(line, point1);\n * //=false\n *\n * turf.booleanIntersects(line, point2);\n * //=true\n *\n * //addToMap\n * var addToMap = [point1, point2, line];\n * point1.properties['marker-color'] = '#f00'\n * point2.properties['marker-color'] = '#0f0'\n */\nfunction booleanIntersects(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry,\n  {\n    ignoreSelfIntersections = true,\n  }: {\n    ignoreSelfIntersections?: boolean;\n  } = {}\n) {\n  let bool = false;\n  flattenEach(feature1, (flatten1) => {\n    flattenEach(feature2, (flatten2) => {\n      if (bool === true) {\n        return true;\n      }\n      bool = !booleanDisjoint(flatten1.geometry, flatten2.geometry, {\n        ignoreSelfIntersections,\n      });\n    });\n  });\n  return bool;\n}\n\nexport { booleanIntersects };\nexport default booleanIntersects;\n"]}