{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { flattenEach } from \"@turf/meta\";\nimport { featureCollection } from \"@turf/helpers\";\nimport type { AllGeoJSON } from \"@turf/helpers\";\nimport type {\n  Feature,\n  Point,\n  MultiPoint,\n  LineString,\n  MultiLineString,\n  FeatureCollection,\n  Polygon,\n  MultiPolygon,\n} from \"geojson\";\n\n/**\n * Flattens any {@link GeoJSON} to a {@link FeatureCollection} inspired by [geojson-flatten](https://github.com/tmcw/geojson-flatten).\n *\n * @function\n * @param {GeoJSON} geojson any valid GeoJSON Object\n * @returns {FeatureCollection<any>} all Multi-Geometries are flattened into single Features\n * @example\n * var multiGeometry = turf.multiPolygon([\n *   [[[102.0, 2.0], [103.0, 2.0], [103.0, 3.0], [102.0, 3.0], [102.0, 2.0]]],\n *   [[[100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0]],\n *   [[100.2, 0.2], [100.8, 0.2], [100.8, 0.8], [100.2, 0.8], [100.2, 0.2]]]\n * ]);\n *\n * var flatten = turf.flatten(multiGeometry);\n *\n * //addToMap\n * var addToMap = [flatten]\n */\nfunction flatten<T extends Point | MultiPoint>(\n  geojson: Feature<T> | FeatureCollection<T> | T\n): FeatureCollection<Point>;\n\nfunction flatten<T extends LineString | MultiLineString>(\n  geojson: Feature<T> | FeatureCollection<T> | T\n): FeatureCollection<LineString>;\n\nfunction flatten<T extends Polygon | MultiPolygon>(\n  geojson: Feature<T> | FeatureCollection<T> | T\n): FeatureCollection<Polygon>;\n\nfunction flatten(geojson: AllGeoJSON): FeatureCollection<any>;\n\nfunction flatten(geojson: AllGeoJSON): FeatureCollection {\n  if (!geojson) throw new Error(\"geojson is required\");\n\n  var results: Feature[] = [];\n  flattenEach(geojson, function (feature) {\n    results.push(feature);\n  });\n  return featureCollection(results);\n}\n\nexport { flatten };\nexport default flatten;\n"], "mappings": ";AAAA,SAAS,mBAAmB;AAC5B,SAAS,yBAAyB;AA6ClC,SAAS,QAAQ,SAAwC;AACvD,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AAEnD,MAAI,UAAqB,CAAC;AAC1B,cAAY,SAAS,SAAU,SAAS;AACtC,YAAQ,KAAK,OAAO;AAAA,EACtB,CAAC;AACD,SAAO,kBAAkB,OAAO;AAClC;AAGA,IAAO,uBAAQ;", "names": []}