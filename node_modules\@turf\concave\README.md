# @turf/concave

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## concave

Takes a set of [points][1] and returns a concave hull Polygon or MultiPolygon.
Internally, this uses [turf-tin][2] to generate geometries.

### Parameters

*   `points` **[FeatureCollection][3]<[Point][1]>** input points
*   `options` **[Object][4]** Optional parameters (optional, default `{}`)

    *   `options.maxEdge` **[number][5]** the length (in 'units') of an edge necessary for part of the
        hull to become concave. (optional, default `Infinity`)
    *   `options.units` **[string][6]** can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)

### Examples

```javascript
var points = turf.featureCollection([
  turf.point([-63.601226, 44.642643]),
  turf.point([-63.591442, 44.651436]),
  turf.point([-63.580799, 44.648749]),
  turf.point([-63.573589, 44.641788]),
  turf.point([-63.587665, 44.64533]),
  turf.point([-63.595218, 44.64765])
]);
var options = {units: 'miles', maxEdge: 1};

var hull = turf.concave(points, options);

//addToMap
var addToMap = [points, hull]
```

Returns **([Feature][7]<([Polygon][8] | [MultiPolygon][9])> | null)** a concave hull (null value is returned if unable to compute hull)

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://github.com/Turfjs/turf-tin

[3]: https://tools.ietf.org/html/rfc7946#section-3.3

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[7]: https://tools.ietf.org/html/rfc7946#section-3.2

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.7

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/concave
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
