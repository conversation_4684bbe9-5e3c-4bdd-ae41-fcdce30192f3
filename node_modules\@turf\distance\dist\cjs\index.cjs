"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _invariant = require('@turf/invariant');
var _helpers = require('@turf/helpers');
function distance(from, to, options = {}) {
  var coordinates1 = _invariant.getCoord.call(void 0, from);
  var coordinates2 = _invariant.getCoord.call(void 0, to);
  var dLat = _helpers.degreesToRadians.call(void 0, coordinates2[1] - coordinates1[1]);
  var dLon = _helpers.degreesToRadians.call(void 0, coordinates2[0] - coordinates1[0]);
  var lat1 = _helpers.degreesToRadians.call(void 0, coordinates1[1]);
  var lat2 = _helpers.degreesToRadians.call(void 0, coordinates2[1]);
  var a = Math.pow(Math.sin(dLat / 2), 2) + Math.pow(Math.sin(dLon / 2), 2) * Math.cos(lat1) * Math.cos(lat2);
  return _helpers.radiansToLength.call(void 0, 
    2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)),
    options.units
  );
}
var turf_distance_default = distance;



exports.default = turf_distance_default; exports.distance = distance;
//# sourceMappingURL=index.cjs.map