import { Polygon, MultiPolygon, GeoJsonProperties, Feature } from 'geo<PERSON>son';
import { Coord } from '@turf/helpers';

/**
 * Takes a {@link Point} and a {@link Polygon} or {@link MultiPolygon} and determines if the point
 * resides inside the polygon. The polygon can be convex or concave. The function accounts for holes.
 *
 * @function
 * @param {Coord} point input point
 * @param {Feature<Polygon|MultiPolygon>} polygon input polygon or multipolygon
 * @param {Object} [options={}] Optional parameters
 * @param {boolean} [options.ignoreBoundary=false] True if polygon boundary should be ignored when determining if
 * the point is inside the polygon otherwise false.
 * @returns {boolean} `true` if the Point is inside the Polygon; `false` if the Point is not inside the Polygon
 * @example
 * var pt = turf.point([-77, 44]);
 * var poly = turf.polygon([[
 *   [-81, 41],
 *   [-81, 47],
 *   [-72, 47],
 *   [-72, 41],
 *   [-81, 41]
 * ]]);
 *
 * turf.booleanPointInPolygon(pt, poly);
 * //= true
 */
declare function booleanPointInPolygon<G extends Polygon | MultiPolygon, P extends GeoJsonProperties = GeoJsonProperties>(point: Coord, polygon: Feature<G, P> | G, options?: {
    ignoreBoundary?: boolean;
}): boolean;

export { booleanPointInPolygon, booleanPointInPolygon as default };
