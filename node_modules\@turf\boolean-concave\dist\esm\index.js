// index.ts
import { getGeom } from "@turf/invariant";
function booleanConcave(polygon) {
  const coords = getGeom(polygon).coordinates;
  if (coords[0].length <= 4) {
    return false;
  }
  let sign = false;
  const n = coords[0].length - 1;
  for (let i = 0; i < n; i++) {
    const dx1 = coords[0][(i + 2) % n][0] - coords[0][(i + 1) % n][0];
    const dy1 = coords[0][(i + 2) % n][1] - coords[0][(i + 1) % n][1];
    const dx2 = coords[0][i][0] - coords[0][(i + 1) % n][0];
    const dy2 = coords[0][i][1] - coords[0][(i + 1) % n][1];
    const zcrossproduct = dx1 * dy2 - dy1 * dx2;
    if (i === 0) {
      sign = zcrossproduct > 0;
    } else if (sign !== zcrossproduct > 0) {
      return true;
    }
  }
  return false;
}
var turf_boolean_concave_default = booleanConcave;
export {
  booleanConcave,
  turf_boolean_concave_default as default
};
//# sourceMappingURL=index.js.map