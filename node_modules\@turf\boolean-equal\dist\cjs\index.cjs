"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _geojsonequalityts = require('geojson-equality-ts');
var _cleancoords = require('@turf/clean-coords');
var _invariant = require('@turf/invariant');
function booleanEqual(feature1, feature2, options = {}) {
  let precision = options.precision;
  precision = precision === void 0 || precision === null || isNaN(precision) ? 6 : precision;
  if (typeof precision !== "number" || !(precision >= 0)) {
    throw new Error("precision must be a positive number");
  }
  const type1 = _invariant.getGeom.call(void 0, feature1).type;
  const type2 = _invariant.getGeom.call(void 0, feature2).type;
  if (type1 !== type2) return false;
  return _geojsonequalityts.geojsonEquality.call(void 0, _cleancoords.cleanCoords.call(void 0, feature1), _cleancoords.cleanCoords.call(void 0, feature2), {
    precision
  });
}
var turf_boolean_equal_default = booleanEqual;



exports.booleanEqual = booleanEqual; exports.default = turf_boolean_equal_default;
//# sourceMappingURL=index.cjs.map