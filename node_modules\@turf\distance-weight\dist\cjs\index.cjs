"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _centroid = require('@turf/centroid');
var _invariant = require('@turf/invariant');
var _meta = require('@turf/meta');
function pNormDistance(feature1, feature2, p = 2) {
  const coordinate1 = _invariant.getCoord.call(void 0, feature1);
  const coordinate2 = _invariant.getCoord.call(void 0, feature2);
  const xDiff = coordinate1[0] - coordinate2[0];
  const yDiff = coordinate1[1] - coordinate2[1];
  if (p === 1) {
    return Math.abs(xDiff) + Math.abs(yDiff);
  }
  return Math.pow(Math.pow(xDiff, p) + Math.pow(yDiff, p), 1 / p);
}
function distanceWeight(fc, options) {
  var _a, _b;
  options = options || {};
  const threshold = options.threshold || 1e4;
  const p = options.p || 2;
  const binary = (_a = options.binary) != null ? _a : false;
  const alpha = options.alpha || -1;
  const rowTransform = (_b = options.standardization) != null ? _b : false;
  const features = [];
  _meta.featureEach.call(void 0, fc, (feature) => {
    features.push(_centroid.centroid.call(void 0, feature));
  });
  const weights = [];
  for (let i = 0; i < features.length; i++) {
    weights[i] = [];
  }
  for (let i = 0; i < features.length; i++) {
    for (let j = i; j < features.length; j++) {
      if (i === j) {
        weights[i][j] = 0;
      }
      const dis = pNormDistance(features[i], features[j], p);
      weights[i][j] = dis;
      weights[j][i] = dis;
    }
  }
  for (let i = 0; i < features.length; i++) {
    for (let j = 0; j < features.length; j++) {
      const dis = weights[i][j];
      if (dis === 0) {
        continue;
      }
      if (binary) {
        if (dis <= threshold) {
          weights[i][j] = 1;
        } else {
          weights[i][j] = 0;
        }
      } else {
        if (dis <= threshold) {
          weights[i][j] = Math.pow(dis, alpha);
        } else {
          weights[i][j] = 0;
        }
      }
    }
  }
  if (rowTransform) {
    for (let i = 0; i < features.length; i++) {
      const rowSum = weights[i].reduce((sum, currentVal) => {
        return sum + currentVal;
      }, 0);
      for (let j = 0; j < features.length; j++) {
        weights[i][j] = weights[i][j] / rowSum;
      }
    }
  }
  return weights;
}
var turf_distance_weight_default = distanceWeight;




exports.default = turf_distance_weight_default; exports.distanceWeight = distanceWeight; exports.pNormDistance = pNormDistance;
//# sourceMappingURL=index.cjs.map