// index.ts
import {
  degreesToRadians,
  polygon,
  isObject,
  isNumber
} from "@turf/helpers";
import { rhumbDestination } from "@turf/rhumb-destination";
import { transformRotate } from "@turf/transform-rotate";
import { getCoord } from "@turf/invariant";
function ellipse(center, xSemiAxis, ySemiAxis, options) {
  options = options || {};
  const steps = options.steps || 64;
  const units = options.units || "kilometers";
  const angle = options.angle || 0;
  const pivot = options.pivot || center;
  const properties = options.properties || {};
  if (!center) throw new Error("center is required");
  if (!xSemiAxis) throw new Error("xSemiAxis is required");
  if (!ySemiAxis) throw new Error("ySemiAxis is required");
  if (!isObject(options)) throw new Error("options must be an object");
  if (!isNumber(steps)) throw new Error("steps must be a number");
  if (!isNumber(angle)) throw new Error("angle must be a number");
  const centerCoords = getCoord(center);
  if (units !== "degrees") {
    const xDest = rhumbDestination(center, xSemiAxis, 90, { units });
    const yDest = rhumbDestination(center, ySemiAxis, 0, { units });
    xSemiAxis = getCoord(xDest)[0] - centerCoords[0];
    ySemiAxis = getCoord(yDest)[1] - centerCoords[1];
  }
  const coordinates = [];
  for (let i = 0; i < steps; i += 1) {
    const stepAngle = i * -360 / steps;
    let x = xSemiAxis * ySemiAxis / Math.sqrt(
      Math.pow(ySemiAxis, 2) + Math.pow(xSemiAxis, 2) * Math.pow(getTanDeg(stepAngle), 2)
    );
    let y = xSemiAxis * ySemiAxis / Math.sqrt(
      Math.pow(xSemiAxis, 2) + Math.pow(ySemiAxis, 2) / Math.pow(getTanDeg(stepAngle), 2)
    );
    if (stepAngle < -90 && stepAngle >= -270) x = -x;
    if (stepAngle < -180 && stepAngle >= -360) y = -y;
    if (units === "degrees") {
      const angleRad = degreesToRadians(angle);
      const newx = x * Math.cos(angleRad) + y * Math.sin(angleRad);
      const newy = y * Math.cos(angleRad) - x * Math.sin(angleRad);
      x = newx;
      y = newy;
    }
    coordinates.push([x + centerCoords[0], y + centerCoords[1]]);
  }
  coordinates.push(coordinates[0]);
  if (units === "degrees") {
    return polygon([coordinates], properties);
  } else {
    return transformRotate(polygon([coordinates], properties), angle, {
      pivot
    });
  }
}
function getTanDeg(deg) {
  const rad = deg * Math.PI / 180;
  return Math.tan(rad);
}
var turf_ellipse_default = ellipse;
export {
  turf_ellipse_default as default,
  ellipse
};
//# sourceMappingURL=index.js.map