# @turf/distance-weight

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## pNormDistance

calcualte the Minkowski p-norm distance between two features.

### Parameters

*   `feature1` **[Feature][1]<[Point][2]>** point feature
*   `feature2` **[Feature][1]<[Point][2]>** point feature
*   `p`  p-norm 1=\<p<=infinity 1: Manhattan distance 2: Euclidean distance (optional, default `2`)

Returns **[number][3]**&#x20;

## distanceWeight

### Parameters

*   `fc` **[FeatureCollection][4]\<any>** FeatureCollection.
*   `options` **[Object][5]?** option object.

    *   `options.threshold` **[number][3]** If the distance between neighbor and
        target features is greater than threshold, the weight of that neighbor is 0. (optional, default `10000`)
    *   `options.p` **[number][3]** Minkowski p-norm distance parameter.
        1: Manhattan distance. 2: Euclidean distance. 1=\<p<=infinity. (optional, default `2`)
    *   `options.binary` **[boolean][6]** If true, weight=1 if d <= threshold otherwise weight=0.
        If false, weight=Math.pow(d, alpha). (optional, default `false`)
    *   `options.alpha` **[number][3]** distance decay parameter.
        A big value means the weight decay quickly as distance increases. (optional, default `-1`)
    *   `options.standardization` **[boolean][6]** row standardization. (optional, default `false`)

### Examples

```javascript
var bbox = [-65, 40, -63, 42];
var dataset = turf.randomPoint(100, { bbox: bbox });
var result = turf.distanceWeight(dataset);
```

Returns **[Array][7]<[Array][7]<[number][3]>>** distance weight matrix.

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[4]: https://tools.ietf.org/html/rfc7946#section-3.3

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/distance-weight
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
