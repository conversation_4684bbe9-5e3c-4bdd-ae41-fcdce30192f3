{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-explode/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,kCAAuC;AACvC,wCAAyC;AAmBzC,SAAS,OAAA,CAAQ,OAAA,EAA+C;AAC9D,EAAA,MAAM,OAAA,EAA2B,CAAC,CAAA;AAClC,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,mBAAA,EAAqB;AACxC,IAAA,+BAAA,OAAY,EAAS,QAAA,CAAU,OAAA,EAAS;AACtC,MAAA,6BAAA,OAAU,EAAS,QAAA,CAAU,KAAA,EAAO;AAClC,QAAA,MAAA,CAAO,IAAA,CAAK,4BAAA,KAAM,EAAO,OAAA,CAAQ,UAAU,CAAC,CAAA;AAAA,MAC9C,CAAC,CAAA;AAAA,IACH,CAAC,CAAA;AAAA,EACH,EAAA,KAAA,GAAA,CAAW,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW;AACrC,IAAA,6BAAA,OAAU,EAAS,QAAA,CAAU,KAAA,EAAO;AAClC,MAAA,MAAA,CAAO,IAAA,CAAK,4BAAA,KAAM,EAAO,OAAA,CAAQ,UAAU,CAAC,CAAA;AAAA,IAC9C,CAAC,CAAA;AAAA,EACH,EAAA,KAAO;AAEL,IAAA,6BAAA,OAAU,EAAS,QAAA,CAAU,KAAA,EAAO;AAClC,MAAA,MAAA,CAAO,IAAA,CAAK,4BAAA,KAAW,CAAC,CAAA;AAAA,IAC1B,CAAC,CAAA;AAAA,EACH;AAEA,EAAA,OAAO,wCAAA,MAAwB,CAAA;AACjC;AAGA,IAAO,qBAAA,EAAQ,OAAA;ADpBf;AACE;AACA;AACF,kEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-explode/dist/cjs/index.cjs", "sourcesContent": [null, "import { coordEach, featureEach } from \"@turf/meta\";\nimport { point, featureCollection } from \"@turf/helpers\";\nimport type { AllGeoJSON } from \"@turf/helpers\";\nimport type { Feature, FeatureCollection, Point } from \"geojson\";\n\n/**\n * Takes a feature or set of features and returns all positions as {@link Point|points}.\n *\n * @function\n * @param {GeoJSON} geojson input features\n * @returns {FeatureCollection<point>} points representing the exploded input features\n * @throws {Error} if it encounters an unknown geometry type\n * @example\n * var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);\n *\n * var explode = turf.explode(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, explode]\n */\nfunction explode(geojson: AllGeoJSON): FeatureCollection<Point> {\n  const points: Feature<Point>[] = [];\n  if (geojson.type === \"FeatureCollection\") {\n    featureEach(geojson, function (feature) {\n      coordEach(feature, function (coord) {\n        points.push(point(coord, feature.properties));\n      });\n    });\n  } else if (geojson.type === \"Feature\") {\n    coordEach(geojson, function (coord) {\n      points.push(point(coord, geojson.properties));\n    });\n  } else {\n    // No properties to copy.\n    coordEach(geojson, function (coord) {\n      points.push(point(coord));\n    });\n  }\n\n  return featureCollection(points);\n}\n\nexport { explode };\nexport default explode;\n"]}