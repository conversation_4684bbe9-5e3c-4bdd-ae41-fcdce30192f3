{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, FeatureCollection, Point } from \"geojson\";\nimport { centroid } from \"@turf/centroid\";\nimport { getCoord } from \"@turf/invariant\";\nimport { featureEach } from \"@turf/meta\";\n\n/**\n * calcualte the Minkowski p-norm distance between two features.\n *\n * @function\n * @param feature1 point feature\n * @param feature2 point feature\n * @param p p-norm 1=<p<=infinity 1: Manhattan distance 2: Euclidean distance\n */\nfunction pNormDistance(\n  feature1: Feature<Point>,\n  feature2: Feature<Point>,\n  p = 2\n): number {\n  const coordinate1 = getCoord(feature1);\n  const coordinate2 = getCoord(feature2);\n  const xDiff = coordinate1[0] - coordinate2[0];\n  const yDiff = coordinate1[1] - coordinate2[1];\n  if (p === 1) {\n    return Math.abs(xDiff) + Math.abs(yDiff);\n  }\n  return Math.pow(Math.pow(xDiff, p) + Math.pow(yDiff, p), 1 / p);\n}\n\n/**\n *\n *\n * @function\n * @param {FeatureCollection<any>} fc FeatureCollection.\n * @param {Object} [options] option object.\n * @param {number} [options.threshold=10000] If the distance between neighbor and\n * target features is greater than threshold, the weight of that neighbor is 0.\n * @param {number} [options.p=2] Minkowski p-norm distance parameter.\n * 1: Manhattan distance. 2: Euclidean distance. 1=<p<=infinity.\n * @param {boolean} [options.binary=false] If true, weight=1 if d <= threshold otherwise weight=0.\n *  If false, weight=Math.pow(d, alpha).\n * @param {number} [options.alpha=-1] distance decay parameter.\n * A big value means the weight decay quickly as distance increases.\n * @param {boolean} [options.standardization=false] row standardization.\n * @returns {Array<Array<number>>} distance weight matrix.\n * @example\n *\n * var bbox = [-65, 40, -63, 42];\n * var dataset = turf.randomPoint(100, { bbox: bbox });\n * var result = turf.distanceWeight(dataset);\n */\nfunction distanceWeight(\n  fc: FeatureCollection<any>,\n  options?: {\n    threshold?: number;\n    p?: number;\n    binary?: boolean;\n    alpha?: number;\n    standardization?: boolean;\n  }\n): number[][] {\n  options = options || {};\n  const threshold = options.threshold || 10000;\n  const p = options.p || 2;\n  const binary = options.binary ?? false;\n  const alpha = options.alpha || -1;\n  const rowTransform = options.standardization ?? false;\n\n  const features: Array<Feature<Point>> = [];\n  featureEach(fc, (feature) => {\n    features.push(centroid(feature));\n  });\n\n  // computing the distance between the features\n  const weights: number[][] = [];\n  for (let i = 0; i < features.length; i++) {\n    weights[i] = [];\n  }\n\n  for (let i = 0; i < features.length; i++) {\n    for (let j = i; j < features.length; j++) {\n      if (i === j) {\n        weights[i][j] = 0;\n      }\n      const dis = pNormDistance(features[i], features[j], p);\n      weights[i][j] = dis;\n      weights[j][i] = dis;\n    }\n  }\n\n  // binary or distance decay\n  for (let i = 0; i < features.length; i++) {\n    for (let j = 0; j < features.length; j++) {\n      const dis: number = weights[i][j];\n      if (dis === 0) {\n        continue;\n      }\n      if (binary) {\n        if (dis <= threshold) {\n          weights[i][j] = 1.0;\n        } else {\n          weights[i][j] = 0.0;\n        }\n      } else {\n        if (dis <= threshold) {\n          weights[i][j] = Math.pow(dis, alpha);\n        } else {\n          weights[i][j] = 0.0;\n        }\n      }\n    }\n  }\n\n  if (rowTransform) {\n    for (let i = 0; i < features.length; i++) {\n      const rowSum = weights[i].reduce((sum: number, currentVal: number) => {\n        return sum + currentVal;\n      }, 0);\n      for (let j = 0; j < features.length; j++) {\n        weights[i][j] = weights[i][j] / rowSum;\n      }\n    }\n  }\n\n  return weights;\n}\n\nexport { pNormDistance, distanceWeight };\nexport default distanceWeight;\n"], "mappings": ";AACA,SAAS,gBAAgB;AACzB,SAAS,gBAAgB;AACzB,SAAS,mBAAmB;AAU5B,SAAS,cACP,UACA,UACA,IAAI,GACI;AACR,QAAM,cAAc,SAAS,QAAQ;AACrC,QAAM,cAAc,SAAS,QAAQ;AACrC,QAAM,QAAQ,YAAY,CAAC,IAAI,YAAY,CAAC;AAC5C,QAAM,QAAQ,YAAY,CAAC,IAAI,YAAY,CAAC;AAC5C,MAAI,MAAM,GAAG;AACX,WAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,EACzC;AACA,SAAO,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC;AAChE;AAwBA,SAAS,eACP,IACA,SAOY;AA3Dd;AA4DE,YAAU,WAAW,CAAC;AACtB,QAAM,YAAY,QAAQ,aAAa;AACvC,QAAM,IAAI,QAAQ,KAAK;AACvB,QAAM,UAAS,aAAQ,WAAR,YAAkB;AACjC,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,gBAAe,aAAQ,oBAAR,YAA2B;AAEhD,QAAM,WAAkC,CAAC;AACzC,cAAY,IAAI,CAAC,YAAY;AAC3B,aAAS,KAAK,SAAS,OAAO,CAAC;AAAA,EACjC,CAAC;AAGD,QAAM,UAAsB,CAAC;AAC7B,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAQ,CAAC,IAAI,CAAC;AAAA,EAChB;AAEA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAI,MAAM,GAAG;AACX,gBAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,MAClB;AACA,YAAM,MAAM,cAAc,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;AACrD,cAAQ,CAAC,EAAE,CAAC,IAAI;AAChB,cAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,IAClB;AAAA,EACF;AAGA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,MAAc,QAAQ,CAAC,EAAE,CAAC;AAChC,UAAI,QAAQ,GAAG;AACb;AAAA,MACF;AACA,UAAI,QAAQ;AACV,YAAI,OAAO,WAAW;AACpB,kBAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,QAClB,OAAO;AACL,kBAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,QAClB;AAAA,MACF,OAAO;AACL,YAAI,OAAO,WAAW;AACpB,kBAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,QACrC,OAAO;AACL,kBAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,cAAc;AAChB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,SAAS,QAAQ,CAAC,EAAE,OAAO,CAAC,KAAa,eAAuB;AACpE,eAAO,MAAM;AAAA,MACf,GAAG,CAAC;AACJ,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAQ,CAAC,EAAE,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAGA,IAAO,+BAAQ;", "names": []}