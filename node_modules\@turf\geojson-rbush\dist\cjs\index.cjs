"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// index.js
var _rbush = require('rbush'); var _rbush2 = _interopRequireDefault(_rbush);
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
var _bbox = require('@turf/bbox');
function geojsonRbush(maxEntries) {
  var tree = new (0, _rbush2.default)(maxEntries);
  tree.insert = function(feature) {
    if (feature.type !== "Feature") throw new Error("invalid feature");
    feature.bbox = feature.bbox ? feature.bbox : _bbox.bbox.call(void 0, feature);
    return _rbush2.default.prototype.insert.call(this, feature);
  };
  tree.load = function(features) {
    var load = [];
    if (Array.isArray(features)) {
      features.forEach(function(feature) {
        if (feature.type !== "Feature") throw new Error("invalid features");
        feature.bbox = feature.bbox ? feature.bbox : _bbox.bbox.call(void 0, feature);
        load.push(feature);
      });
    } else {
      _meta.featureEach.call(void 0, features, function(feature) {
        if (feature.type !== "Feature") throw new Error("invalid features");
        feature.bbox = feature.bbox ? feature.bbox : _bbox.bbox.call(void 0, feature);
        load.push(feature);
      });
    }
    return _rbush2.default.prototype.load.call(this, load);
  };
  tree.remove = function(feature, equals) {
    if (feature.type !== "Feature") throw new Error("invalid feature");
    feature.bbox = feature.bbox ? feature.bbox : _bbox.bbox.call(void 0, feature);
    return _rbush2.default.prototype.remove.call(this, feature, equals);
  };
  tree.clear = function() {
    return _rbush2.default.prototype.clear.call(this);
  };
  tree.search = function(geojson) {
    var features = _rbush2.default.prototype.search.call(this, this.toBBox(geojson));
    return _helpers.featureCollection.call(void 0, features);
  };
  tree.collides = function(geojson) {
    return _rbush2.default.prototype.collides.call(this, this.toBBox(geojson));
  };
  tree.all = function() {
    var features = _rbush2.default.prototype.all.call(this);
    return _helpers.featureCollection.call(void 0, features);
  };
  tree.toJSON = function() {
    return _rbush2.default.prototype.toJSON.call(this);
  };
  tree.fromJSON = function(json) {
    return _rbush2.default.prototype.fromJSON.call(this, json);
  };
  tree.toBBox = function(geojson) {
    var bbox;
    if (geojson.bbox) bbox = geojson.bbox;
    else if (Array.isArray(geojson) && geojson.length === 4) bbox = geojson;
    else if (Array.isArray(geojson) && geojson.length === 6)
      bbox = [geojson[0], geojson[1], geojson[3], geojson[4]];
    else if (geojson.type === "Feature") bbox = _bbox.bbox.call(void 0, geojson);
    else if (geojson.type === "FeatureCollection") bbox = _bbox.bbox.call(void 0, geojson);
    else throw new Error("invalid geojson");
    return {
      minX: bbox[0],
      minY: bbox[1],
      maxX: bbox[2],
      maxY: bbox[3]
    };
  };
  return tree;
}
var turf_geojson_rbush_default = geojsonRbush;



exports.default = turf_geojson_rbush_default; exports.geojsonRbush = geojsonRbush;
//# sourceMappingURL=index.cjs.map