{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, Geometry, Polygon, LineString, MultiPoint } from \"geojson\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { polygonToLine } from \"@turf/polygon-to-line\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { getGeom } from \"@turf/invariant\";\nimport { point } from \"@turf/helpers\";\n\n/**\n * Boolean-Crosses returns True if the intersection results in a geometry whose dimension is one less than\n * the maximum dimension of the two source geometries and the intersection set is interior to\n * both source geometries.\n *\n * Boolean-Crosses returns t (TRUE) for only multipoint/polygon, multipoint/linestring, linestring/linestring, linestring/polygon, and linestring/multipolygon comparisons.\n * Other comparisons are not supported as they are outside the OpenGIS Simple Features spec and may give unexpected results.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line1 = turf.lineString([[-2, 2], [4, 2]]);\n * var line2 = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n *\n * var cross = turf.booleanCrosses(line1, line2);\n * //=true\n */\nfunction booleanCrosses(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  var geom1 = getGeom(feature1);\n  var geom2 = getGeom(feature2);\n  var type1 = geom1.type;\n  var type2 = geom2.type;\n\n  switch (type1) {\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"LineString\":\n          return doMultiPointAndLineStringCross(geom1, geom2);\n        case \"Polygon\":\n          return doesMultiPointCrossPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"MultiPoint\": // An inverse operation\n          return doMultiPointAndLineStringCross(geom2, geom1);\n        case \"LineString\":\n          return doLineStringsCross(geom1, geom2);\n        case \"Polygon\":\n          return doLineStringAndPolygonCross(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"MultiPoint\": // An inverse operation\n          return doesMultiPointCrossPoly(geom2, geom1);\n        case \"LineString\": // An inverse operation\n          return doLineStringAndPolygonCross(geom2, geom1);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction doMultiPointAndLineStringCross(\n  multiPoint: MultiPoint,\n  lineString: LineString\n) {\n  var foundIntPoint = false;\n  var foundExtPoint = false;\n  var pointLength = multiPoint.coordinates.length;\n  var i = 0;\n  while (i < pointLength && !foundIntPoint && !foundExtPoint) {\n    for (var i2 = 0; i2 < lineString.coordinates.length - 1; i2++) {\n      var incEndVertices = true;\n      if (i2 === 0 || i2 === lineString.coordinates.length - 2) {\n        incEndVertices = false;\n      }\n      if (\n        isPointOnLineSegment(\n          lineString.coordinates[i2],\n          lineString.coordinates[i2 + 1],\n          multiPoint.coordinates[i],\n          incEndVertices\n        )\n      ) {\n        foundIntPoint = true;\n      } else {\n        foundExtPoint = true;\n      }\n    }\n    i++;\n  }\n  return foundIntPoint && foundExtPoint;\n}\n\nfunction doLineStringsCross(lineString1: LineString, lineString2: LineString) {\n  var doLinesIntersect = lineIntersect(lineString1, lineString2);\n  if (doLinesIntersect.features.length > 0) {\n    for (var i = 0; i < lineString1.coordinates.length - 1; i++) {\n      for (var i2 = 0; i2 < lineString2.coordinates.length - 1; i2++) {\n        var incEndVertices = true;\n        if (i2 === 0 || i2 === lineString2.coordinates.length - 2) {\n          incEndVertices = false;\n        }\n        if (\n          isPointOnLineSegment(\n            lineString1.coordinates[i],\n            lineString1.coordinates[i + 1],\n            lineString2.coordinates[i2],\n            incEndVertices\n          )\n        ) {\n          return true;\n        }\n      }\n    }\n  }\n  return false;\n}\n\nfunction doLineStringAndPolygonCross(lineString: LineString, polygon: Polygon) {\n  const line: any = polygonToLine(polygon);\n  const doLinesIntersect = lineIntersect(lineString, line);\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\nfunction doesMultiPointCrossPoly(multiPoint: MultiPoint, polygon: Polygon) {\n  var foundIntPoint = false;\n  var foundExtPoint = false;\n  var pointLength = multiPoint.coordinates.length;\n  for (let i = 0; i < pointLength && (!foundIntPoint || !foundExtPoint); i++) {\n    if (booleanPointInPolygon(point(multiPoint.coordinates[i]), polygon)) {\n      foundIntPoint = true;\n    } else {\n      foundExtPoint = true;\n    }\n  }\n\n  return foundExtPoint && foundIntPoint;\n}\n\n/**\n * Is a point on a line segment\n * Only takes into account outer rings\n * See http://stackoverflow.com/a/4833823/1979085\n *\n * @private\n * @param {number[]} lineSegmentStart coord pair of start of line\n * @param {number[]} lineSegmentEnd coord pair of end of line\n * @param {number[]} pt coord pair of point to check\n * @param {boolean} incEnd whether the point is allowed to fall on the line ends\n * @returns {boolean} true/false\n */\nfunction isPointOnLineSegment(\n  lineSegmentStart: number[],\n  lineSegmentEnd: number[],\n  pt: number[],\n  incEnd: boolean\n) {\n  var dxc = pt[0] - lineSegmentStart[0];\n  var dyc = pt[1] - lineSegmentStart[1];\n  var dxl = lineSegmentEnd[0] - lineSegmentStart[0];\n  var dyl = lineSegmentEnd[1] - lineSegmentStart[1];\n  var cross = dxc * dyl - dyc * dxl;\n  if (cross !== 0) {\n    return false;\n  }\n  if (incEnd) {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0\n        ? lineSegmentStart[0] <= pt[0] && pt[0] <= lineSegmentEnd[0]\n        : lineSegmentEnd[0] <= pt[0] && pt[0] <= lineSegmentStart[0];\n    }\n    return dyl > 0\n      ? lineSegmentStart[1] <= pt[1] && pt[1] <= lineSegmentEnd[1]\n      : lineSegmentEnd[1] <= pt[1] && pt[1] <= lineSegmentStart[1];\n  } else {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0\n        ? lineSegmentStart[0] < pt[0] && pt[0] < lineSegmentEnd[0]\n        : lineSegmentEnd[0] < pt[0] && pt[0] < lineSegmentStart[0];\n    }\n    return dyl > 0\n      ? lineSegmentStart[1] < pt[1] && pt[1] < lineSegmentEnd[1]\n      : lineSegmentEnd[1] < pt[1] && pt[1] < lineSegmentStart[1];\n  }\n}\n\nexport { booleanCrosses };\nexport default booleanCrosses;\n"], "mappings": ";AACA,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,6BAA6B;AACtC,SAAS,eAAe;AACxB,SAAS,aAAa;AAqBtB,SAAS,eACP,UACA,UACS;AACT,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,QAAQ,MAAM;AAClB,MAAI,QAAQ,MAAM;AAElB,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,+BAA+B,OAAO,KAAK;AAAA,QACpD,KAAK;AACH,iBAAO,wBAAwB,OAAO,KAAK;AAAA,QAC7C;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,+BAA+B,OAAO,KAAK;AAAA,QACpD,KAAK;AACH,iBAAO,mBAAmB,OAAO,KAAK;AAAA,QACxC,KAAK;AACH,iBAAO,4BAA4B,OAAO,KAAK;AAAA,QACjD;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,wBAAwB,OAAO,KAAK;AAAA,QAC7C,KAAK;AACH,iBAAO,4BAA4B,OAAO,KAAK;AAAA,QACjD;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF;AACE,YAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,EACnE;AACF;AAEA,SAAS,+BACP,YACA,YACA;AACA,MAAI,gBAAgB;AACpB,MAAI,gBAAgB;AACpB,MAAI,cAAc,WAAW,YAAY;AACzC,MAAI,IAAI;AACR,SAAO,IAAI,eAAe,CAAC,iBAAiB,CAAC,eAAe;AAC1D,aAAS,KAAK,GAAG,KAAK,WAAW,YAAY,SAAS,GAAG,MAAM;AAC7D,UAAI,iBAAiB;AACrB,UAAI,OAAO,KAAK,OAAO,WAAW,YAAY,SAAS,GAAG;AACxD,yBAAiB;AAAA,MACnB;AACA,UACE;AAAA,QACE,WAAW,YAAY,EAAE;AAAA,QACzB,WAAW,YAAY,KAAK,CAAC;AAAA,QAC7B,WAAW,YAAY,CAAC;AAAA,QACxB;AAAA,MACF,GACA;AACA,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AACA;AAAA,EACF;AACA,SAAO,iBAAiB;AAC1B;AAEA,SAAS,mBAAmB,aAAyB,aAAyB;AAC5E,MAAI,mBAAmB,cAAc,aAAa,WAAW;AAC7D,MAAI,iBAAiB,SAAS,SAAS,GAAG;AACxC,aAAS,IAAI,GAAG,IAAI,YAAY,YAAY,SAAS,GAAG,KAAK;AAC3D,eAAS,KAAK,GAAG,KAAK,YAAY,YAAY,SAAS,GAAG,MAAM;AAC9D,YAAI,iBAAiB;AACrB,YAAI,OAAO,KAAK,OAAO,YAAY,YAAY,SAAS,GAAG;AACzD,2BAAiB;AAAA,QACnB;AACA,YACE;AAAA,UACE,YAAY,YAAY,CAAC;AAAA,UACzB,YAAY,YAAY,IAAI,CAAC;AAAA,UAC7B,YAAY,YAAY,EAAE;AAAA,UAC1B;AAAA,QACF,GACA;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,4BAA4B,YAAwB,SAAkB;AAC7E,QAAM,OAAY,cAAc,OAAO;AACvC,QAAM,mBAAmB,cAAc,YAAY,IAAI;AACvD,MAAI,iBAAiB,SAAS,SAAS,GAAG;AACxC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,wBAAwB,YAAwB,SAAkB;AACzE,MAAI,gBAAgB;AACpB,MAAI,gBAAgB;AACpB,MAAI,cAAc,WAAW,YAAY;AACzC,WAAS,IAAI,GAAG,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,gBAAgB,KAAK;AAC1E,QAAI,sBAAsB,MAAM,WAAW,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG;AACpE,sBAAgB;AAAA,IAClB,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AAEA,SAAO,iBAAiB;AAC1B;AAcA,SAAS,qBACP,kBACA,gBACA,IACA,QACA;AACA,MAAI,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC;AACpC,MAAI,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC;AACpC,MAAI,MAAM,eAAe,CAAC,IAAI,iBAAiB,CAAC;AAChD,MAAI,MAAM,eAAe,CAAC,IAAI,iBAAiB,CAAC;AAChD,MAAI,QAAQ,MAAM,MAAM,MAAM;AAC9B,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,MAAI,QAAQ;AACV,QAAI,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAClC,aAAO,MAAM,IACT,iBAAiB,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,eAAe,CAAC,IACzD,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,iBAAiB,CAAC;AAAA,IAC/D;AACA,WAAO,MAAM,IACT,iBAAiB,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,eAAe,CAAC,IACzD,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,iBAAiB,CAAC;AAAA,EAC/D,OAAO;AACL,QAAI,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAClC,aAAO,MAAM,IACT,iBAAiB,CAAC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,eAAe,CAAC,IACvD,eAAe,CAAC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,iBAAiB,CAAC;AAAA,IAC7D;AACA,WAAO,MAAM,IACT,iBAAiB,CAAC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,eAAe,CAAC,IACvD,eAAe,CAAC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,iBAAiB,CAAC;AAAA,EAC7D;AACF;AAGA,IAAO,+BAAQ;", "names": []}