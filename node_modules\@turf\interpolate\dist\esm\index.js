// index.js
import { bbox } from "@turf/bbox";
import { hexGrid } from "@turf/hex-grid";
import { pointGrid } from "@turf/point-grid";
import { distance } from "@turf/distance";
import { centroid } from "@turf/centroid";
import { squareGrid } from "@turf/square-grid";
import { triangleGrid } from "@turf/triangle-grid";
import { clone } from "@turf/clone";
import { featureCollection, validateBBox } from "@turf/helpers";
import { featureEach } from "@turf/meta";
import { collectionOf } from "@turf/invariant";
function interpolate(points, cellSize, options) {
  options = options || {};
  if (typeof options !== "object") throw new Error("options is invalid");
  var gridType = options.gridType;
  var property = options.property;
  var weight = options.weight;
  var box = options.bbox;
  if (!points) throw new Error("points is required");
  collectionOf(points, "Point", "input must contain Points");
  if (!cellSize) throw new Error("cellSize is required");
  if (weight !== void 0 && typeof weight !== "number")
    throw new Error("weight must be a number");
  property = property || "elevation";
  gridType = gridType || "square";
  weight = weight || 1;
  box = box != null ? box : bbox(points);
  validateBBox(box);
  var grid;
  switch (gridType) {
    case "point":
    case "points":
      grid = pointGrid(box, cellSize, options);
      break;
    case "square":
    case "squares":
      grid = squareGrid(box, cellSize, options);
      break;
    case "hex":
    case "hexes":
      grid = hexGrid(box, cellSize, options);
      break;
    case "triangle":
    case "triangles":
      grid = triangleGrid(box, cellSize, options);
      break;
    default:
      throw new Error("invalid gridType");
  }
  var results = [];
  featureEach(grid, function(gridFeature) {
    var zw = 0;
    var sw = 0;
    featureEach(points, function(point) {
      var gridPoint = gridType === "point" ? gridFeature : centroid(gridFeature);
      var d = distance(gridPoint, point, options);
      var zValue;
      if (property !== void 0) zValue = point.properties[property];
      if (zValue === void 0) zValue = point.geometry.coordinates[2];
      if (zValue === void 0) throw new Error("zValue is missing");
      if (d === 0) zw = zValue;
      var w = 1 / Math.pow(d, weight);
      sw += w;
      zw += w * zValue;
    });
    var newFeature = clone(gridFeature);
    newFeature.properties[property] = zw / sw;
    results.push(newFeature);
  });
  return featureCollection(results);
}
var turf_interpolate_default = interpolate;
export {
  turf_interpolate_default as default,
  interpolate
};
//# sourceMappingURL=index.js.map