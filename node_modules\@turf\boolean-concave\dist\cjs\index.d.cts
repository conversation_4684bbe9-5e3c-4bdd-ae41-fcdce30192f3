import { Feature, Polygon } from 'geo<PERSON><PERSON>';

/**
 * Takes a polygon and return true or false as to whether it is concave or not.
 *
 * @function
 * @param {Feature<Polygon>} polygon to be evaluated
 * @returns {boolean} true/false
 * @example
 * var convexPolygon = turf.polygon([[[0,0],[0,1],[1,1],[1,0],[0,0]]]);
 *
 * turf.booleanConcave(convexPolygon)
 * //=false
 */
declare function booleanConcave(polygon: Feature<Polygon> | Polygon): boolean;

export { booleanConcave, booleanConcave as default };
