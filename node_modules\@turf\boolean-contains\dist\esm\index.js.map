{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  BBox,\n  Feature,\n  Geometry,\n  LineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { bbox as calcBbox } from \"@turf/bbox\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { booleanPointOnLine as isPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Boolean-contains returns True if the second geometry is completely contained by the first geometry.\n * The interiors of both geometries must intersect and, the interior and boundary of the secondary (geometry b)\n * must not intersect the exterior of the primary (geometry a).\n * Boolean-contains returns the exact opposite result of the `@turf/boolean-within`.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n * var point = turf.point([1, 2]);\n *\n * turf.booleanContains(line, point);\n * //=true\n */\nfunction booleanContains(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n) {\n  const geom1 = getGeom(feature1);\n  const geom2 = getGeom(feature2);\n  const type1 = geom1.type;\n  const type2 = geom2.type;\n  const coords1 = geom1.coordinates;\n  const coords2 = geom2.coordinates;\n\n  switch (type1) {\n    case \"Point\":\n      switch (type2) {\n        case \"Point\":\n          return compareCoords(coords1, coords2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"Point\":\n          return isPointInMultiPoint(geom1, geom2);\n        case \"MultiPoint\":\n          return isMultiPointInMultiPoint(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"Point\":\n          return isPointOnLine(geom2, geom1, { ignoreEndVertices: true });\n        case \"LineString\":\n          return isLineOnLine(geom1, geom2);\n        case \"MultiPoint\":\n          return isMultiPointOnLine(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"Point\":\n          return booleanPointInPolygon(geom2, geom1, { ignoreBoundary: true });\n        case \"LineString\":\n          return isLineInPoly(geom1, geom2);\n        case \"Polygon\":\n          return isPolyInPoly(geom1, geom2);\n        case \"MultiPoint\":\n          return isMultiPointInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPolygon\":\n      switch (type2) {\n        case \"Polygon\":\n          return isPolygonInMultiPolygon(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction isPolygonInMultiPolygon(multiPolygon: MultiPolygon, polygon: Polygon) {\n  return multiPolygon.coordinates.some((coords) =>\n    isPolyInPoly({ type: \"Polygon\", coordinates: coords }, polygon)\n  );\n}\n\nfunction isPointInMultiPoint(multiPoint: MultiPoint, pt: Point) {\n  let i;\n  let output = false;\n  for (i = 0; i < multiPoint.coordinates.length; i++) {\n    if (compareCoords(multiPoint.coordinates[i], pt.coordinates)) {\n      output = true;\n      break;\n    }\n  }\n  return output;\n}\n\nfunction isMultiPointInMultiPoint(\n  multiPoint1: MultiPoint,\n  multiPoint2: MultiPoint\n) {\n  for (const coord2 of multiPoint2.coordinates) {\n    let matchFound = false;\n    for (const coord1 of multiPoint1.coordinates) {\n      if (compareCoords(coord2, coord1)) {\n        matchFound = true;\n        break;\n      }\n    }\n    if (!matchFound) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isMultiPointOnLine(lineString: LineString, multiPoint: MultiPoint) {\n  let haveFoundInteriorPoint = false;\n  for (const coord of multiPoint.coordinates) {\n    if (isPointOnLine(coord, lineString, { ignoreEndVertices: true })) {\n      haveFoundInteriorPoint = true;\n    }\n    if (!isPointOnLine(coord, lineString)) {\n      return false;\n    }\n  }\n  if (haveFoundInteriorPoint) {\n    return true;\n  }\n  return false;\n}\n\nfunction isMultiPointInPoly(polygon: Polygon, multiPoint: MultiPoint) {\n  for (const coord of multiPoint.coordinates) {\n    if (!booleanPointInPolygon(coord, polygon, { ignoreBoundary: true })) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isLineOnLine(lineString1: LineString, lineString2: LineString) {\n  let haveFoundInteriorPoint = false;\n  for (const coords of lineString2.coordinates) {\n    if (\n      isPointOnLine({ type: \"Point\", coordinates: coords }, lineString1, {\n        ignoreEndVertices: true,\n      })\n    ) {\n      haveFoundInteriorPoint = true;\n    }\n    if (\n      !isPointOnLine({ type: \"Point\", coordinates: coords }, lineString1, {\n        ignoreEndVertices: false,\n      })\n    ) {\n      return false;\n    }\n  }\n  return haveFoundInteriorPoint;\n}\n\nfunction isLineInPoly(polygon: Polygon, linestring: LineString) {\n  let output = false;\n  let i = 0;\n\n  const polyBbox = calcBbox(polygon);\n  const lineBbox = calcBbox(linestring);\n  if (!doBBoxOverlap(polyBbox, lineBbox)) {\n    return false;\n  }\n  for (i; i < linestring.coordinates.length - 1; i++) {\n    const midPoint = getMidpoint(\n      linestring.coordinates[i],\n      linestring.coordinates[i + 1]\n    );\n    if (\n      booleanPointInPolygon({ type: \"Point\", coordinates: midPoint }, polygon, {\n        ignoreBoundary: true,\n      })\n    ) {\n      output = true;\n      break;\n    }\n  }\n  return output;\n}\n\n/**\n * Is Polygon2 in Polygon1\n * Only takes into account outer rings\n *\n * @private\n * @param {Geometry|Feature<Polygon>} feature1 Polygon1\n * @param {Geometry|Feature<Polygon>} feature2 Polygon2\n * @returns {boolean} true/false\n */\nfunction isPolyInPoly(\n  feature1: Feature<Polygon> | Polygon,\n  feature2: Feature<Polygon> | Polygon\n) {\n  // Handle Nulls\n  if (feature1.type === \"Feature\" && feature1.geometry === null) {\n    return false;\n  }\n  if (feature2.type === \"Feature\" && feature2.geometry === null) {\n    return false;\n  }\n\n  const poly1Bbox = calcBbox(feature1);\n  const poly2Bbox = calcBbox(feature2);\n  if (!doBBoxOverlap(poly1Bbox, poly2Bbox)) {\n    return false;\n  }\n\n  const coords = getGeom(feature2).coordinates;\n  for (const ring of coords) {\n    for (const coord of ring) {\n      if (!booleanPointInPolygon(coord, feature1)) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nfunction doBBoxOverlap(bbox1: BBox, bbox2: BBox) {\n  if (bbox1[0] > bbox2[0]) {\n    return false;\n  }\n  if (bbox1[2] < bbox2[2]) {\n    return false;\n  }\n  if (bbox1[1] > bbox2[1]) {\n    return false;\n  }\n  if (bbox1[3] < bbox2[3]) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true/false if coord pairs match\n */\nfunction compareCoords(pair1: number[], pair2: number[]) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\nfunction getMidpoint(pair1: number[], pair2: number[]) {\n  return [(pair1[0] + pair2[0]) / 2, (pair1[1] + pair2[1]) / 2];\n}\n\nexport {\n  booleanContains,\n  isPolygonInMultiPolygon,\n  isPointInMultiPoint,\n  isMultiPointInMultiPoint,\n  isMultiPointOnLine,\n  isMultiPointInPoly,\n  isLineOnLine,\n  isLineInPoly,\n  isPolyInPoly,\n  doBBoxOverlap,\n  compareCoords,\n  getMidpoint,\n};\n\nexport default booleanContains;\n"], "mappings": ";AAUA,SAAS,QAAQ,gBAAgB;AACjC,SAAS,6BAA6B;AACtC,SAAS,sBAAsB,qBAAqB;AACpD,SAAS,eAAe;AAmBxB,SAAS,gBACP,UACA,UACA;AACA,QAAM,QAAQ,QAAQ,QAAQ;AAC9B,QAAM,QAAQ,QAAQ,QAAQ;AAC9B,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,MAAM;AACpB,QAAM,UAAU,MAAM;AACtB,QAAM,UAAU,MAAM;AAEtB,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,cAAc,SAAS,OAAO;AAAA,QACvC;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,oBAAoB,OAAO,KAAK;AAAA,QACzC,KAAK;AACH,iBAAO,yBAAyB,OAAO,KAAK;AAAA,QAC9C;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,cAAc,OAAO,OAAO,EAAE,mBAAmB,KAAK,CAAC;AAAA,QAChE,KAAK;AACH,iBAAO,aAAa,OAAO,KAAK;AAAA,QAClC,KAAK;AACH,iBAAO,mBAAmB,OAAO,KAAK;AAAA,QACxC;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,sBAAsB,OAAO,OAAO,EAAE,gBAAgB,KAAK,CAAC;AAAA,QACrE,KAAK;AACH,iBAAO,aAAa,OAAO,KAAK;AAAA,QAClC,KAAK;AACH,iBAAO,aAAa,OAAO,KAAK;AAAA,QAClC,KAAK;AACH,iBAAO,mBAAmB,OAAO,KAAK;AAAA,QACxC;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,wBAAwB,OAAO,KAAK;AAAA,QAC7C;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF;AACE,YAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,EACnE;AACF;AAEA,SAAS,wBAAwB,cAA4B,SAAkB;AAC7E,SAAO,aAAa,YAAY;AAAA,IAAK,CAAC,WACpC,aAAa,EAAE,MAAM,WAAW,aAAa,OAAO,GAAG,OAAO;AAAA,EAChE;AACF;AAEA,SAAS,oBAAoB,YAAwB,IAAW;AAC9D,MAAI;AACJ,MAAI,SAAS;AACb,OAAK,IAAI,GAAG,IAAI,WAAW,YAAY,QAAQ,KAAK;AAClD,QAAI,cAAc,WAAW,YAAY,CAAC,GAAG,GAAG,WAAW,GAAG;AAC5D,eAAS;AACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,yBACP,aACA,aACA;AACA,aAAW,UAAU,YAAY,aAAa;AAC5C,QAAI,aAAa;AACjB,eAAW,UAAU,YAAY,aAAa;AAC5C,UAAI,cAAc,QAAQ,MAAM,GAAG;AACjC,qBAAa;AACb;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,YAAwB,YAAwB;AAC1E,MAAI,yBAAyB;AAC7B,aAAW,SAAS,WAAW,aAAa;AAC1C,QAAI,cAAc,OAAO,YAAY,EAAE,mBAAmB,KAAK,CAAC,GAAG;AACjE,+BAAyB;AAAA,IAC3B;AACA,QAAI,CAAC,cAAc,OAAO,UAAU,GAAG;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,wBAAwB;AAC1B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,SAAkB,YAAwB;AACpE,aAAW,SAAS,WAAW,aAAa;AAC1C,QAAI,CAAC,sBAAsB,OAAO,SAAS,EAAE,gBAAgB,KAAK,CAAC,GAAG;AACpE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,aAAyB,aAAyB;AACtE,MAAI,yBAAyB;AAC7B,aAAW,UAAU,YAAY,aAAa;AAC5C,QACE,cAAc,EAAE,MAAM,SAAS,aAAa,OAAO,GAAG,aAAa;AAAA,MACjE,mBAAmB;AAAA,IACrB,CAAC,GACD;AACA,+BAAyB;AAAA,IAC3B;AACA,QACE,CAAC,cAAc,EAAE,MAAM,SAAS,aAAa,OAAO,GAAG,aAAa;AAAA,MAClE,mBAAmB;AAAA,IACrB,CAAC,GACD;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,SAAkB,YAAwB;AAC9D,MAAI,SAAS;AACb,MAAI,IAAI;AAER,QAAM,WAAW,SAAS,OAAO;AACjC,QAAM,WAAW,SAAS,UAAU;AACpC,MAAI,CAAC,cAAc,UAAU,QAAQ,GAAG;AACtC,WAAO;AAAA,EACT;AACA,OAAK,GAAG,IAAI,WAAW,YAAY,SAAS,GAAG,KAAK;AAClD,UAAM,WAAW;AAAA,MACf,WAAW,YAAY,CAAC;AAAA,MACxB,WAAW,YAAY,IAAI,CAAC;AAAA,IAC9B;AACA,QACE,sBAAsB,EAAE,MAAM,SAAS,aAAa,SAAS,GAAG,SAAS;AAAA,MACvE,gBAAgB;AAAA,IAClB,CAAC,GACD;AACA,eAAS;AACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAWA,SAAS,aACP,UACA,UACA;AAEA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,MAAM;AAC7D,WAAO;AAAA,EACT;AACA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,MAAM;AAC7D,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,SAAS,QAAQ;AACnC,QAAM,YAAY,SAAS,QAAQ;AACnC,MAAI,CAAC,cAAc,WAAW,SAAS,GAAG;AACxC,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,QAAQ,QAAQ,EAAE;AACjC,aAAW,QAAQ,QAAQ;AACzB,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,sBAAsB,OAAO,QAAQ,GAAG;AAC3C,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,OAAa,OAAa;AAC/C,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG;AACvB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAUA,SAAS,cAAc,OAAiB,OAAiB;AACvD,SAAO,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC;AACtD;AAEA,SAAS,YAAY,OAAiB,OAAiB;AACrD,SAAO,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC;AAC9D;AAiBA,IAAO,gCAAQ;", "names": []}