{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-center-mean/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,kCAAoC;AACpC,wCAAoC;AA4BpC,SAAS,UAAA,CACP,OAAA,EACA,QAAA,EAAqE,CAAC,CAAA,EACnD;AACnB,EAAA,IAAI,MAAA,EAAQ,CAAA;AACZ,EAAA,IAAI,MAAA,EAAQ,CAAA;AACZ,EAAA,IAAI,MAAA,EAAQ,CAAA;AACZ,EAAA,4BAAA,OAAS,EAAS,QAAA,CAAU,IAAA,EAAM,YAAA,EAAc,UAAA,EAAY;AAC1D,IAAA,IAAI,OAAA,EAAS,OAAA,CAAQ,OAAA,EAAS,WAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA,UAAA,CAAa,OAAA,CAAQ,MAAA,EAAA,EAAU,KAAA,CAAA;AAC7D,IAAA,OAAA,EAAS,OAAA,IAAW,KAAA,EAAA,GAAa,OAAA,IAAW,KAAA,EAAO,EAAA,EAAI,MAAA;AACvD,IAAA,GAAA,CAAI,CAAC,+BAAA,MAAe,CAAA;AAClB,MAAA,MAAM,IAAI,KAAA;AAAA,QACR,mDAAA,EAAqD;AAAA,MACvD,CAAA;AACF,IAAA,OAAA,EAAS,MAAA,CAAO,MAAM,CAAA;AACtB,IAAA,GAAA,CAAI,OAAA,EAAS,CAAA,EAAG;AACd,MAAA,6BAAA,IAAU,EAAM,QAAA,CAAU,KAAA,EAAO;AAC/B,QAAA,MAAA,GAAS,KAAA,CAAM,CAAC,EAAA,EAAI,MAAA;AACpB,QAAA,MAAA,GAAS,KAAA,CAAM,CAAC,EAAA,EAAI,MAAA;AACpB,QAAA,MAAA,GAAS,MAAA;AAAA,MACX,CAAC,CAAA;AAAA,IACH;AAAA,EACF,CAAC,CAAA;AACD,EAAA,OAAO,4BAAA,CAAO,MAAA,EAAQ,KAAA,EAAO,MAAA,EAAQ,KAAK,CAAA,EAAG,OAAA,CAAQ,UAAA,EAAY,OAAO,CAAA;AAC1E;AAGA,IAAO,yBAAA,EAAQ,UAAA;AD/Bf;AACE;AACA;AACF,4EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-center-mean/dist/cjs/index.cjs", "sourcesContent": [null, "import { BBox, Feature, Point, GeoJsonProperties } from \"geojson\";\nimport { geomEach, coordEach } from \"@turf/meta\";\nimport { isNumber, point, Id } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Feature} or {@link FeatureCollection} and returns the mean center. Can be weighted.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] Translate GeoJSON Properties to Point\n * @param {Object} [options.bbox={}] Translate GeoJSON BBox to Point\n * @param {Object} [options.id={}] Translate GeoJSON Id to Point\n * @param {string} [options.weight] the property name used to weight the center\n * @returns {Feature<Point>} a Point feature at the mean center point of all input features\n * @example\n * var features = turf.featureCollection([\n *   turf.point([-97.522259, 35.4691], {value: 10}),\n *   turf.point([-97.502754, 35.463455], {value: 3}),\n *   turf.point([-97.508269, 35.463245], {value: 5})\n * ]);\n *\n * var options = {weight: \"value\"}\n * var mean = turf.centerMean(features, options);\n *\n * //addToMap\n * var addToMap = [features, mean]\n * mean.properties['marker-size'] = 'large';\n * mean.properties['marker-color'] = '#000';\n */\nfunction centerMean<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: any, // To-Do include Typescript AllGeoJSON\n  options: { properties?: P; bbox?: BBox; id?: Id; weight?: string } = {}\n): Feature<Point, P> {\n  let sumXs = 0;\n  let sumYs = 0;\n  let sumNs = 0;\n  geomEach(geojson, function (geom, featureIndex, properties) {\n    let weight = options.weight ? properties?.[options.weight] : undefined;\n    weight = weight === undefined || weight === null ? 1 : weight;\n    if (!isNumber(weight))\n      throw new Error(\n        \"weight value must be a number for feature index \" + featureIndex\n      );\n    weight = Number(weight);\n    if (weight > 0) {\n      coordEach(geom, function (coord) {\n        sumXs += coord[0] * weight;\n        sumYs += coord[1] * weight;\n        sumNs += weight;\n      });\n    }\n  });\n  return point([sumXs / sumNs, sumYs / sumNs], options.properties, options);\n}\n\nexport { centerMean };\nexport default centerMean;\n"]}