{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { distance } from \"@turf/distance\";\nimport { intersect } from \"@turf/intersect\";\nimport {\n  Feature,\n  FeatureCollection,\n  GeoJsonProperties,\n  Polygon,\n  BBox,\n} from \"geojson\";\nimport { polygon, featureCollection, Units } from \"@turf/helpers\";\n\n/**\n * Takes a bounding box and the diameter of the cell and returns a {@link FeatureCollection} of flat-topped\n * hexagons or triangles ({@link Polygon} features) aligned in an \"odd-q\" vertical grid as\n * described in [Hexagonal Grids](http://www.redblobgames.com/grids/hexagons/).\n *\n * @function\n * @param {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @param {number} cellSide length of the side of the the hexagons or triangles, in units. It will also coincide with the\n * radius of the circumcircle of the hexagons.\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] used in calculating cell size, can be degrees, radians, miles, or kilometers\n * @param {Object} [options.properties={}] passed to each hexagon or triangle of the grid\n * @param {Feature<Polygon>} [options.mask] if passed a Polygon or MultiPolygon, the grid Points will be created only inside it\n * @param {boolean} [options.triangles=false] whether to return as triangles instead of hexagons\n * @returns {FeatureCollection<Polygon>} a hexagonal grid\n * @example\n * var bbox = [-96,31,-84,40];\n * var cellSide = 50;\n * var options = {units: 'miles'};\n *\n * var hexgrid = turf.hexGrid(bbox, cellSide, options);\n *\n * //addToMap\n * var addToMap = [hexgrid];\n */\nfunction hexGrid<P extends GeoJsonProperties = GeoJsonProperties>(\n  bbox: BBox,\n  cellSide: number,\n  options: {\n    units?: Units;\n    triangles?: boolean;\n    properties?: P;\n    mask?: Feature<Polygon>;\n  } = {}\n): FeatureCollection<Polygon, P> {\n  // Issue => https://github.com/Turfjs/turf/issues/1284\n  const clonedProperties = JSON.stringify(options.properties || {});\n\n  const [west, south, east, north] = bbox;\n  const centerY = (south + north) / 2;\n  const centerX = (west + east) / 2;\n\n  // https://github.com/Turfjs/turf/issues/758\n  const xFraction =\n    (cellSide * 2) / distance([west, centerY], [east, centerY], options);\n  const cellWidth = xFraction * (east - west);\n  const yFraction =\n    (cellSide * 2) / distance([centerX, south], [centerX, north], options);\n  const cellHeight = yFraction * (north - south);\n  const radius = cellWidth / 2;\n\n  const hex_width = radius * 2;\n  const hex_height = (Math.sqrt(3) / 2) * cellHeight;\n\n  const box_width = east - west;\n  const box_height = north - south;\n\n  const x_interval = (3 / 4) * hex_width;\n  const y_interval = hex_height;\n\n  // adjust box_width so all hexagons will be inside the bbox\n  const x_span = (box_width - hex_width) / (hex_width - radius / 2);\n  const x_count = Math.floor(x_span);\n\n  const x_adjust =\n    (x_count * x_interval - radius / 2 - box_width) / 2 -\n    radius / 2 +\n    x_interval / 2;\n\n  // adjust box_height so all hexagons will be inside the bbox\n  const y_count = Math.floor((box_height - hex_height) / hex_height);\n\n  let y_adjust = (box_height - y_count * hex_height) / 2;\n\n  const hasOffsetY = y_count * hex_height - box_height > hex_height / 2;\n  if (hasOffsetY) {\n    y_adjust -= hex_height / 4;\n  }\n\n  // Precompute cosines and sines of angles used in hexagon creation for performance gain\n  const cosines = [];\n  const sines = [];\n  for (let i = 0; i < 6; i++) {\n    const angle = ((2 * Math.PI) / 6) * i;\n    cosines.push(Math.cos(angle));\n    sines.push(Math.sin(angle));\n  }\n\n  const results = [];\n  for (let x = 0; x <= x_count; x++) {\n    for (let y = 0; y <= y_count; y++) {\n      const isOdd = x % 2 === 1;\n      if (y === 0 && isOdd) continue;\n      if (y === 0 && hasOffsetY) continue;\n\n      const center_x = x * x_interval + west - x_adjust;\n      let center_y = y * y_interval + south + y_adjust;\n\n      if (isOdd) {\n        center_y -= hex_height / 2;\n      }\n\n      if (options.triangles === true) {\n        hexTriangles(\n          [center_x, center_y],\n          cellWidth / 2,\n          cellHeight / 2,\n          JSON.parse(clonedProperties),\n          cosines,\n          sines\n        ).forEach(function (triangle) {\n          if (options.mask) {\n            if (intersect(featureCollection([options.mask, triangle])))\n              results.push(triangle);\n          } else {\n            results.push(triangle);\n          }\n        });\n      } else {\n        const hex = hexagon(\n          [center_x, center_y],\n          cellWidth / 2,\n          cellHeight / 2,\n          JSON.parse(clonedProperties),\n          cosines,\n          sines\n        );\n        if (options.mask) {\n          if (intersect(featureCollection([options.mask, hex])))\n            results.push(hex);\n        } else {\n          results.push(hex);\n        }\n      }\n    }\n  }\n\n  return featureCollection(results) as FeatureCollection<Polygon, P>;\n}\n\n/**\n * Creates hexagon\n *\n * @private\n * @param {Array<number>} center of the hexagon\n * @param {number} rx half hexagon width\n * @param {number} ry half hexagon height\n * @param {Object} properties passed to each hexagon\n * @param {Array<number>} cosines precomputed\n * @param {Array<number>} sines precomputed\n * @returns {Feature<Polygon>} hexagon\n */\nfunction hexagon(\n  center: number[],\n  rx: number,\n  ry: number,\n  properties: GeoJsonProperties,\n  cosines: number[],\n  sines: number[]\n) {\n  const vertices = [];\n  for (let i = 0; i < 6; i++) {\n    const x = center[0] + rx * cosines[i];\n    const y = center[1] + ry * sines[i];\n    vertices.push([x, y]);\n  }\n  //first and last vertex must be the same\n  vertices.push(vertices[0].slice());\n  return polygon([vertices], properties);\n}\n\n/**\n * Creates triangles composing an hexagon\n *\n * @private\n * @param {Array<number>} center of the hexagon\n * @param {number} rx half triangle width\n * @param {number} ry half triangle height\n * @param {Object} properties passed to each triangle\n * @param {Array<number>} cosines precomputed\n * @param {Array<number>} sines precomputed\n * @returns {Array<Feature<Polygon>>} triangles\n */\nfunction hexTriangles(\n  center: number[],\n  rx: number,\n  ry: number,\n  properties: GeoJsonProperties,\n  cosines: number[],\n  sines: number[]\n) {\n  const triangles = [];\n  for (let i = 0; i < 6; i++) {\n    const vertices = [];\n    vertices.push(center);\n    vertices.push([center[0] + rx * cosines[i], center[1] + ry * sines[i]]);\n    vertices.push([\n      center[0] + rx * cosines[(i + 1) % 6],\n      center[1] + ry * sines[(i + 1) % 6],\n    ]);\n    vertices.push(center);\n    triangles.push(polygon([vertices], properties));\n  }\n  return triangles;\n}\n\nexport { hexGrid };\nexport default hexGrid;\n"], "mappings": ";AAAA,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAQ1B,SAAS,SAAS,yBAAgC;AA2BlD,SAAS,QACP,MACA,UACA,UAKI,CAAC,GAC0B;AAE/B,QAAM,mBAAmB,KAAK,UAAU,QAAQ,cAAc,CAAC,CAAC;AAEhE,QAAM,CAAC,MAAM,OAAO,MAAM,KAAK,IAAI;AACnC,QAAM,WAAW,QAAQ,SAAS;AAClC,QAAM,WAAW,OAAO,QAAQ;AAGhC,QAAM,YACH,WAAW,IAAK,SAAS,CAAC,MAAM,OAAO,GAAG,CAAC,MAAM,OAAO,GAAG,OAAO;AACrE,QAAM,YAAY,aAAa,OAAO;AACtC,QAAM,YACH,WAAW,IAAK,SAAS,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,OAAO;AACvE,QAAM,aAAa,aAAa,QAAQ;AACxC,QAAM,SAAS,YAAY;AAE3B,QAAM,YAAY,SAAS;AAC3B,QAAM,aAAc,KAAK,KAAK,CAAC,IAAI,IAAK;AAExC,QAAM,YAAY,OAAO;AACzB,QAAM,aAAa,QAAQ;AAE3B,QAAM,aAAc,IAAI,IAAK;AAC7B,QAAM,aAAa;AAGnB,QAAM,UAAU,YAAY,cAAc,YAAY,SAAS;AAC/D,QAAM,UAAU,KAAK,MAAM,MAAM;AAEjC,QAAM,YACH,UAAU,aAAa,SAAS,IAAI,aAAa,IAClD,SAAS,IACT,aAAa;AAGf,QAAM,UAAU,KAAK,OAAO,aAAa,cAAc,UAAU;AAEjE,MAAI,YAAY,aAAa,UAAU,cAAc;AAErD,QAAM,aAAa,UAAU,aAAa,aAAa,aAAa;AACpE,MAAI,YAAY;AACd,gBAAY,aAAa;AAAA,EAC3B;AAGA,QAAM,UAAU,CAAC;AACjB,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,QAAU,IAAI,KAAK,KAAM,IAAK;AACpC,YAAQ,KAAK,KAAK,IAAI,KAAK,CAAC;AAC5B,UAAM,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EAC5B;AAEA,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,KAAK,SAAS,KAAK;AACjC,aAAS,IAAI,GAAG,KAAK,SAAS,KAAK;AACjC,YAAM,QAAQ,IAAI,MAAM;AACxB,UAAI,MAAM,KAAK,MAAO;AACtB,UAAI,MAAM,KAAK,WAAY;AAE3B,YAAM,WAAW,IAAI,aAAa,OAAO;AACzC,UAAI,WAAW,IAAI,aAAa,QAAQ;AAExC,UAAI,OAAO;AACT,oBAAY,aAAa;AAAA,MAC3B;AAEA,UAAI,QAAQ,cAAc,MAAM;AAC9B;AAAA,UACE,CAAC,UAAU,QAAQ;AAAA,UACnB,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,KAAK,MAAM,gBAAgB;AAAA,UAC3B;AAAA,UACA;AAAA,QACF,EAAE,QAAQ,SAAU,UAAU;AAC5B,cAAI,QAAQ,MAAM;AAChB,gBAAI,UAAU,kBAAkB,CAAC,QAAQ,MAAM,QAAQ,CAAC,CAAC;AACvD,sBAAQ,KAAK,QAAQ;AAAA,UACzB,OAAO;AACL,oBAAQ,KAAK,QAAQ;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,MAAM;AAAA,UACV,CAAC,UAAU,QAAQ;AAAA,UACnB,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,KAAK,MAAM,gBAAgB;AAAA,UAC3B;AAAA,UACA;AAAA,QACF;AACA,YAAI,QAAQ,MAAM;AAChB,cAAI,UAAU,kBAAkB,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC;AAClD,oBAAQ,KAAK,GAAG;AAAA,QACpB,OAAO;AACL,kBAAQ,KAAK,GAAG;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,kBAAkB,OAAO;AAClC;AAcA,SAAS,QACP,QACA,IACA,IACA,YACA,SACA,OACA;AACA,QAAM,WAAW,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC;AACpC,UAAM,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;AAClC,aAAS,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,EACtB;AAEA,WAAS,KAAK,SAAS,CAAC,EAAE,MAAM,CAAC;AACjC,SAAO,QAAQ,CAAC,QAAQ,GAAG,UAAU;AACvC;AAcA,SAAS,aACP,QACA,IACA,IACA,YACA,SACA,OACA;AACA,QAAM,YAAY,CAAC;AACnB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,WAAW,CAAC;AAClB,aAAS,KAAK,MAAM;AACpB,aAAS,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;AACtE,aAAS,KAAK;AAAA,MACZ,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC;AAAA,MACpC,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC;AAAA,IACpC,CAAC;AACD,aAAS,KAAK,MAAM;AACpB,cAAU,KAAK,QAAQ,CAAC,QAAQ,GAAG,UAAU,CAAC;AAAA,EAChD;AACA,SAAO;AACT;AAGA,IAAO,wBAAQ;", "names": []}