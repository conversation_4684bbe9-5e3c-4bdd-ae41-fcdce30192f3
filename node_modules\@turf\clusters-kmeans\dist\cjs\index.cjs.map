{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-clusters-kmeans/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,oCAAsB;AACtB,kCAAsC;AACtC,oFAAoB;AA4BpB,SAAS,cAAA,CACP,MAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACkC;AAEvC,EAAA,IAAI,MAAA,EAAQ,MAAA,CAAO,QAAA,CAAS,MAAA;AAC5B,EAAA,OAAA,CAAQ,iBAAA,EACN,OAAA,CAAQ,iBAAA,GAAoB,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,MAAA,EAAQ,CAAC,CAAC,CAAA;AAI7D,EAAA,GAAA,CAAI,OAAA,CAAQ,iBAAA,EAAmB,KAAA,EAAO,OAAA,CAAQ,iBAAA,EAAmB,KAAA;AAGjE,EAAA,GAAA,CAAI,OAAA,CAAQ,OAAA,IAAW,IAAA,EAAM,OAAA,EAAS,0BAAA,MAAY,CAAA;AAGlD,EAAA,IAAI,KAAA,EAAO,4BAAA,MAAe,CAAA;AAG1B,EAAA,IAAI,iBAAA,EAAmB,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,OAAA,CAAQ,gBAAgB,CAAA;AAG7D,EAAA,IAAI,cAAA,EAAgB,+BAAA,IAAQ,EAAM,OAAA,CAAQ,gBAAA,EAAkB,gBAAgB,CAAA;AAG5E,EAAA,IAAI,UAAA,EAAsC,CAAC,CAAA;AAC3C,EAAC,aAAA,CAAc,SAAA,CAAyB,OAAA,CAAQ,QAAA,CAC9C,KAAA,EACA,GAAA,EACA;AACA,IAAA,SAAA,CAAU,GAAG,EAAA,EAAI,KAAA;AAAA,EACnB,CAAC,CAAA;AAGD,EAAA,+BAAA,MAAY,EAAQ,QAAA,CAAU,KAAA,EAAO,KAAA,EAAO;AAC1C,IAAA,IAAI,UAAA,EAAY,aAAA,CAAc,IAAA,CAAK,KAAK,CAAA;AACxC,IAAA,KAAA,CAAM,UAAA,CAAY,QAAA,EAAU,SAAA;AAC5B,IAAA,KAAA,CAAM,UAAA,CAAY,SAAA,EAAW,SAAA,CAAU,SAAS,CAAA;AAAA,EAClD,CAAC,CAAA;AAED,EAAA,OAAO,MAAA;AACT;AAGA,IAAO,6BAAA,EAAQ,cAAA;ADvDf;AACE;AACA;AACF,wFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-clusters-kmeans/dist/cjs/index.cjs", "sourcesContent": [null, "import { FeatureCollection, Point, GeoJsonProperties } from \"geojson\";\nimport { clone } from \"@turf/clone\";\nimport { coordAll, featureEach } from \"@turf/meta\";\nimport skmeans from \"skmeans\";\n\ntype KmeansProps = GeoJsonProperties & {\n  cluster?: number;\n  centroid?: [number, number];\n};\n\n/**\n * Takes a set of {@link Point|points} and partition them into clusters using the k-mean .\n * It uses the [k-means algorithm](https://en.wikipedia.org/wiki/K-means_clustering)\n *\n * @function\n * @param {FeatureCollection<Point>} points to be clustered\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.numberOfClusters=Math.sqrt(numberOfPoints/2)] numberOfClusters that will be generated\n * @param {boolean} [options.mutate=false] allows GeoJSON input to be mutated (significant performance increase if true)\n * @returns {FeatureCollection<Point>} Clustered Points with an additional two properties associated to each Feature:\n * - {number} cluster - the associated clusterId\n * - {[number, number]} centroid - Centroid of the cluster [Longitude, Latitude]\n * @example\n * // create random points with random z-values in their properties\n * var points = turf.randomPoint(100, {bbox: [0, 30, 20, 50]});\n * var options = {numberOfClusters: 7};\n * var clustered = turf.clustersKmeans(points, options);\n *\n * //addToMap\n * var addToMap = [clustered];\n */\nfunction clustersKmeans(\n  points: FeatureCollection<Point>,\n  options: {\n    numberOfClusters?: number;\n    mutate?: boolean;\n  } = {}\n): FeatureCollection<Point, KmeansProps> {\n  // Default Params\n  var count = points.features.length;\n  options.numberOfClusters =\n    options.numberOfClusters || Math.round(Math.sqrt(count / 2));\n\n  // numberOfClusters can't be greater than the number of points\n  // fallbacks to count\n  if (options.numberOfClusters > count) options.numberOfClusters = count;\n\n  // Clone points to prevent any mutations (enabled by default)\n  if (options.mutate !== true) points = clone(points);\n\n  // collect points coordinates\n  var data = coordAll(points);\n\n  // create seed to avoid skmeans to drift\n  var initialCentroids = data.slice(0, options.numberOfClusters);\n\n  // create skmeans clusters\n  var skmeansResult = skmeans(data, options.numberOfClusters, initialCentroids);\n\n  // store centroids {clusterId: [number, number]}\n  var centroids: Record<string, number[]> = {};\n  (skmeansResult.centroids as number[][]).forEach(function (\n    coord: number[],\n    idx: number\n  ) {\n    centroids[idx] = coord;\n  });\n\n  // add associated cluster number\n  featureEach(points, function (point, index) {\n    var clusterId = skmeansResult.idxs[index];\n    point.properties!.cluster = clusterId;\n    point.properties!.centroid = centroids[clusterId];\n  });\n\n  return points as FeatureCollection<Point, KmeansProps>;\n}\n\nexport { clustersKmeans, KmeansProps };\nexport default clustersKmeans;\n"]}