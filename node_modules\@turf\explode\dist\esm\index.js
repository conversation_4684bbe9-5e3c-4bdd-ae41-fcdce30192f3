// index.ts
import { coordEach, featureEach } from "@turf/meta";
import { point, featureCollection } from "@turf/helpers";
function explode(geojson) {
  const points = [];
  if (geojson.type === "FeatureCollection") {
    featureEach(geojson, function(feature) {
      coordEach(feature, function(coord) {
        points.push(point(coord, feature.properties));
      });
    });
  } else if (geojson.type === "Feature") {
    coordEach(geojson, function(coord) {
      points.push(point(coord, geojson.properties));
    });
  } else {
    coordEach(geojson, function(coord) {
      points.push(point(coord));
    });
  }
  return featureCollection(points);
}
var turf_explode_default = explode;
export {
  turf_explode_default as default,
  explode
};
//# sourceMappingURL=index.js.map