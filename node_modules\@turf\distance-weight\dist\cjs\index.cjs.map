{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-distance-weight/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,0CAAyB;AACzB,4CAAyB;AACzB,kCAA4B;AAU5B,SAAS,aAAA,CACP,QAAA,EACA,QAAA,EACA,EAAA,EAAI,CAAA,EACI;AACR,EAAA,MAAM,YAAA,EAAc,iCAAA,QAAiB,CAAA;AACrC,EAAA,MAAM,YAAA,EAAc,iCAAA,QAAiB,CAAA;AACrC,EAAA,MAAM,MAAA,EAAQ,WAAA,CAAY,CAAC,EAAA,EAAI,WAAA,CAAY,CAAC,CAAA;AAC5C,EAAA,MAAM,MAAA,EAAQ,WAAA,CAAY,CAAC,EAAA,EAAI,WAAA,CAAY,CAAC,CAAA;AAC5C,EAAA,GAAA,CAAI,EAAA,IAAM,CAAA,EAAG;AACX,IAAA,OAAO,IAAA,CAAK,GAAA,CAAI,KAAK,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAK,CAAA;AAAA,EACzC;AACA,EAAA,OAAO,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,KAAA,EAAO,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAA,EAAO,CAAC,CAAA,EAAG,EAAA,EAAI,CAAC,CAAA;AAChE;AAwBA,SAAS,cAAA,CACP,EAAA,EACA,OAAA,EAOY;AA3Dd,EAAA,IAAA,EAAA,EAAA,EAAA;AA4DE,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,MAAM,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,GAAA;AACvC,EAAA,MAAM,EAAA,EAAI,OAAA,CAAQ,EAAA,GAAK,CAAA;AACvB,EAAA,MAAM,OAAA,EAAA,CAAS,GAAA,EAAA,OAAA,CAAQ,MAAA,EAAA,GAAR,KAAA,EAAA,GAAA,EAAkB,KAAA;AACjC,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,CAAA,CAAA;AAC/B,EAAA,MAAM,aAAA,EAAA,CAAe,GAAA,EAAA,OAAA,CAAQ,eAAA,EAAA,GAAR,KAAA,EAAA,GAAA,EAA2B,KAAA;AAEhD,EAAA,MAAM,SAAA,EAAkC,CAAC,CAAA;AACzC,EAAA,+BAAA,EAAY,EAAI,CAAC,OAAA,EAAA,GAAY;AAC3B,IAAA,QAAA,CAAS,IAAA,CAAK,gCAAA,OAAgB,CAAC,CAAA;AAAA,EACjC,CAAC,CAAA;AAGD,EAAA,MAAM,QAAA,EAAsB,CAAC,CAAA;AAC7B,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,IAAA,OAAA,CAAQ,CAAC,EAAA,EAAI,CAAC,CAAA;AAAA,EAChB;AAEA,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,MAAA,GAAA,CAAI,EAAA,IAAM,CAAA,EAAG;AACX,QAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA;AAAA,MAClB;AACA,MAAA,MAAM,IAAA,EAAM,aAAA,CAAc,QAAA,CAAS,CAAC,CAAA,EAAG,QAAA,CAAS,CAAC,CAAA,EAAG,CAAC,CAAA;AACrD,MAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,GAAA;AAChB,MAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,GAAA;AAAA,IAClB;AAAA,EACF;AAGA,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,MAAA,MAAM,IAAA,EAAc,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA;AAChC,MAAA,GAAA,CAAI,IAAA,IAAQ,CAAA,EAAG;AACb,QAAA,QAAA;AAAA,MACF;AACA,MAAA,GAAA,CAAI,MAAA,EAAQ;AACV,QAAA,GAAA,CAAI,IAAA,GAAO,SAAA,EAAW;AACpB,UAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA;AAAA,QAClB,EAAA,KAAO;AACL,UAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA;AAAA,QAClB;AAAA,MACF,EAAA,KAAO;AACL,QAAA,GAAA,CAAI,IAAA,GAAO,SAAA,EAAW;AACpB,UAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,GAAA,EAAK,KAAK,CAAA;AAAA,QACrC,EAAA,KAAO;AACL,UAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,EAAA,GAAA,CAAI,YAAA,EAAc;AAChB,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,MAAA,MAAM,OAAA,EAAS,OAAA,CAAQ,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC,GAAA,EAAa,UAAA,EAAA,GAAuB;AACpE,QAAA,OAAO,IAAA,EAAM,UAAA;AAAA,MACf,CAAA,EAAG,CAAC,CAAA;AACJ,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,QAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,MAAA;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAEA,EAAA,OAAO,OAAA;AACT;AAGA,IAAO,6BAAA,EAAQ,cAAA;ADrDf;AACE;AACA;AACA;AACF,+HAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-distance-weight/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, FeatureCollection, Point } from \"geojson\";\nimport { centroid } from \"@turf/centroid\";\nimport { getCoord } from \"@turf/invariant\";\nimport { featureEach } from \"@turf/meta\";\n\n/**\n * calcualte the Minkowski p-norm distance between two features.\n *\n * @function\n * @param feature1 point feature\n * @param feature2 point feature\n * @param p p-norm 1=<p<=infinity 1: Manhattan distance 2: Euclidean distance\n */\nfunction pNormDistance(\n  feature1: Feature<Point>,\n  feature2: Feature<Point>,\n  p = 2\n): number {\n  const coordinate1 = getCoord(feature1);\n  const coordinate2 = getCoord(feature2);\n  const xDiff = coordinate1[0] - coordinate2[0];\n  const yDiff = coordinate1[1] - coordinate2[1];\n  if (p === 1) {\n    return Math.abs(xDiff) + Math.abs(yDiff);\n  }\n  return Math.pow(Math.pow(xDiff, p) + Math.pow(yDiff, p), 1 / p);\n}\n\n/**\n *\n *\n * @function\n * @param {FeatureCollection<any>} fc FeatureCollection.\n * @param {Object} [options] option object.\n * @param {number} [options.threshold=10000] If the distance between neighbor and\n * target features is greater than threshold, the weight of that neighbor is 0.\n * @param {number} [options.p=2] Minkowski p-norm distance parameter.\n * 1: Manhattan distance. 2: Euclidean distance. 1=<p<=infinity.\n * @param {boolean} [options.binary=false] If true, weight=1 if d <= threshold otherwise weight=0.\n *  If false, weight=Math.pow(d, alpha).\n * @param {number} [options.alpha=-1] distance decay parameter.\n * A big value means the weight decay quickly as distance increases.\n * @param {boolean} [options.standardization=false] row standardization.\n * @returns {Array<Array<number>>} distance weight matrix.\n * @example\n *\n * var bbox = [-65, 40, -63, 42];\n * var dataset = turf.randomPoint(100, { bbox: bbox });\n * var result = turf.distanceWeight(dataset);\n */\nfunction distanceWeight(\n  fc: FeatureCollection<any>,\n  options?: {\n    threshold?: number;\n    p?: number;\n    binary?: boolean;\n    alpha?: number;\n    standardization?: boolean;\n  }\n): number[][] {\n  options = options || {};\n  const threshold = options.threshold || 10000;\n  const p = options.p || 2;\n  const binary = options.binary ?? false;\n  const alpha = options.alpha || -1;\n  const rowTransform = options.standardization ?? false;\n\n  const features: Array<Feature<Point>> = [];\n  featureEach(fc, (feature) => {\n    features.push(centroid(feature));\n  });\n\n  // computing the distance between the features\n  const weights: number[][] = [];\n  for (let i = 0; i < features.length; i++) {\n    weights[i] = [];\n  }\n\n  for (let i = 0; i < features.length; i++) {\n    for (let j = i; j < features.length; j++) {\n      if (i === j) {\n        weights[i][j] = 0;\n      }\n      const dis = pNormDistance(features[i], features[j], p);\n      weights[i][j] = dis;\n      weights[j][i] = dis;\n    }\n  }\n\n  // binary or distance decay\n  for (let i = 0; i < features.length; i++) {\n    for (let j = 0; j < features.length; j++) {\n      const dis: number = weights[i][j];\n      if (dis === 0) {\n        continue;\n      }\n      if (binary) {\n        if (dis <= threshold) {\n          weights[i][j] = 1.0;\n        } else {\n          weights[i][j] = 0.0;\n        }\n      } else {\n        if (dis <= threshold) {\n          weights[i][j] = Math.pow(dis, alpha);\n        } else {\n          weights[i][j] = 0.0;\n        }\n      }\n    }\n  }\n\n  if (rowTransform) {\n    for (let i = 0; i < features.length; i++) {\n      const rowSum = weights[i].reduce((sum: number, currentVal: number) => {\n        return sum + currentVal;\n      }, 0);\n      for (let j = 0; j < features.length; j++) {\n        weights[i][j] = weights[i][j] / rowSum;\n      }\n    }\n  }\n\n  return weights;\n}\n\nexport { pNormDistance, distanceWeight };\nexport default distanceWeight;\n"]}