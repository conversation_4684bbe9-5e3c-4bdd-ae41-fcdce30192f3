{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-offset/dist/cjs/index.cjs", "../../index.js", "../../lib/intersection.js"], "names": ["intersection"], "mappings": "AAAA;ACAA,kCAA4B;AAC5B,4CAAmC;AACnC;AACE;AACA;AACA;AACA;AAAA,wCACK;ADEP;AACA;AEGA,SAAS,EAAA,CAAG,OAAA,EAAS;AACnB,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,CAAC,CAAA;AACrB,EAAA,IAAI,IAAA,EAAM,OAAA,CAAQ,CAAC,CAAA;AACnB,EAAA,OAAO,CAAC,GAAA,CAAI,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG,GAAA,CAAI,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAC,CAAA;AAC9C;AAUA,SAAS,YAAA,CAAa,EAAA,EAAI,EAAA,EAAI;AAC5B,EAAA,OAAO,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA;AACrC;AAUA,SAAS,GAAA,CAAI,EAAA,EAAI,EAAA,EAAI;AACnB,EAAA,OAAO,CAAC,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA,EAAG,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,CAAC,CAAA;AACtC;AAUA,SAAS,GAAA,CAAI,EAAA,EAAI,EAAA,EAAI;AACnB,EAAA,OAAO,CAAC,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA,EAAG,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,CAAC,CAAA;AACtC;AAUA,SAAS,UAAA,CAAW,CAAA,EAAG,CAAA,EAAG;AACxB,EAAA,OAAO,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAG,EAAA,EAAI,CAAA,CAAE,CAAC,CAAC,CAAA;AAC5B;AAUA,SAAS,iBAAA,CAAkB,CAAA,EAAG,CAAA,EAAG;AAC/B,EAAA,IAAI,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA;AACX,EAAA,IAAI,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA;AACZ,EAAA,IAAI,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA;AACX,EAAA,IAAI,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA;AAEZ,EAAA,IAAI,MAAA,EAAQ,YAAA,CAAa,CAAA,EAAG,CAAC,CAAA;AAC7B,EAAA,IAAI,IAAA,EAAM,GAAA,CAAI,CAAA,EAAG,CAAC,CAAA;AAClB,EAAA,IAAI,UAAA,EAAY,YAAA,CAAa,GAAA,EAAK,CAAC,CAAA;AACnC,EAAA,IAAI,EAAA,EAAI,UAAA,EAAY,KAAA;AACpB,EAAA,IAAIA,cAAAA,EAAe,GAAA,CAAI,CAAA,EAAG,UAAA,CAAW,CAAA,EAAG,CAAC,CAAC,CAAA;AAC1C,EAAA,OAAOA,aAAAA;AACT;AAUA,SAAS,UAAA,CAAW,CAAA,EAAG,CAAA,EAAG;AACxB,EAAA,IAAI,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA;AACZ,EAAA,IAAI,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA;AACZ,EAAA,OAAO,YAAA,CAAa,CAAA,EAAG,CAAC,EAAA,IAAM,CAAA;AAChC;AAUA,SAAS,YAAA,CAAa,CAAA,EAAG,CAAA,EAAG;AAC1B,EAAA,GAAA,CAAI,UAAA,CAAW,CAAA,EAAG,CAAC,CAAA,EAAG,OAAO,KAAA;AAC7B,EAAA,OAAO,iBAAA,CAAkB,CAAA,EAAG,CAAC,CAAA;AAC/B;AFjEA;AACA;ACtBA,SAAS,UAAA,CAAW,OAAA,EAAS,QAAA,EAAU,OAAA,EAAS;AAE9C,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,KAAA;AAGpB,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AACnD,EAAA,GAAA,CAAI,SAAA,IAAa,KAAA,EAAA,GAAa,SAAA,IAAa,KAAA,GAAQ,KAAA,CAAM,QAAQ,CAAA;AAC/D,IAAA,MAAM,IAAI,KAAA,CAAM,sBAAsB,CAAA;AAExC,EAAA,IAAI,KAAA,EAAO,gCAAA,OAAe,CAAA;AAC1B,EAAA,IAAI,WAAA,EAAa,OAAA,CAAQ,UAAA;AAEzB,EAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,IACZ,KAAK,YAAA;AACH,MAAA,OAAO,iBAAA,CAAkB,OAAA,EAAS,QAAA,EAAU,KAAK,CAAA;AAAA,IACnD,KAAK,iBAAA;AACH,MAAA,IAAI,OAAA,EAAS,CAAC,CAAA;AACd,MAAA,+BAAA,OAAY,EAAS,QAAA,CAAU,OAAA,EAAS;AACtC,QAAA,MAAA,CAAO,IAAA;AAAA,UACL,iBAAA,CAAkB,OAAA,EAAS,QAAA,EAAU,KAAK,CAAA,CAAE,QAAA,CAAS;AAAA,QACvD,CAAA;AAAA,MACF,CAAC,CAAA;AACD,MAAA,OAAO,sCAAA,MAAgB,EAAQ,UAAU,CAAA;AAAA,IAC3C,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,KAAA,EAAO,mBAAmB,CAAA;AAAA,EAC5D;AACF;AAWA,SAAS,iBAAA,CAAkB,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO;AAChD,EAAA,IAAI,SAAA,EAAW,CAAC,CAAA;AAChB,EAAA,IAAI,cAAA,EAAgB,sCAAA,QAAgB,EAAU,KAAK,CAAA;AACnD,EAAA,IAAI,OAAA,EAAS,kCAAA,IAAc,CAAA;AAC3B,EAAA,IAAI,YAAA,EAAc,CAAC,CAAA;AACnB,EAAA,MAAA,CAAO,OAAA,CAAQ,QAAA,CAAU,aAAA,EAAe,KAAA,EAAO;AAC7C,IAAA,GAAA,CAAI,MAAA,IAAU,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG;AAC/B,MAAA,IAAI,QAAA,EAAU,cAAA;AAAA,QACZ,aAAA;AAAA,QACA,MAAA,CAAO,MAAA,EAAQ,CAAC,CAAA;AAAA,QAChB;AAAA,MACF,CAAA;AACA,MAAA,QAAA,CAAS,IAAA,CAAK,OAAO,CAAA;AACrB,MAAA,GAAA,CAAI,MAAA,EAAQ,CAAA,EAAG;AACb,QAAA,IAAI,WAAA,EAAa,QAAA,CAAS,MAAA,EAAQ,CAAC,CAAA;AACnC,QAAA,IAAI,WAAA,EAAa,YAAA,CAAa,OAAA,EAAS,UAAU,CAAA;AAGjD,QAAA,GAAA,CAAI,WAAA,IAAe,KAAA,EAAO;AACxB,UAAA,UAAA,CAAW,CAAC,EAAA,EAAI,UAAA;AAChB,UAAA,OAAA,CAAQ,CAAC,EAAA,EAAI,UAAA;AAAA,QACf;AAEA,QAAA,WAAA,CAAY,IAAA,CAAK,UAAA,CAAW,CAAC,CAAC,CAAA;AAC9B,QAAA,GAAA,CAAI,MAAA,IAAU,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG;AAC/B,UAAA,WAAA,CAAY,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAC,CAAA;AAC3B,UAAA,WAAA,CAAY,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAC,CAAA;AAAA,QAC7B;AAAA,MACF;AAEA,MAAA,GAAA,CAAI,MAAA,CAAO,OAAA,IAAW,CAAA,EAAG;AACvB,QAAA,WAAA,CAAY,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAC,CAAA;AAC3B,QAAA,WAAA,CAAY,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAC,CAAA;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,CAAC,CAAA;AACD,EAAA,OAAO,iCAAA,WAAW,EAAa,IAAA,CAAK,UAAU,CAAA;AAChD;AAYA,SAAS,cAAA,CAAe,MAAA,EAAQ,MAAA,EAAQ,MAAA,EAAQ;AAC9C,EAAA,IAAI,EAAA,EAAI,IAAA,CAAK,IAAA;AAAA,IAAA,CACV,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,EAAA,EAAA,CAAM,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,EAAA,EAAA,CAC5C,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,EAAA,EAAA,CAAM,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA;AAAA,EACnD,CAAA;AAEA,EAAA,IAAI,MAAA,EAAQ,MAAA,CAAO,CAAC,EAAA,EAAK,OAAA,EAAA,CAAU,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,EAAA,EAAM,CAAA;AAC7D,EAAA,IAAI,MAAA,EAAQ,MAAA,CAAO,CAAC,EAAA,EAAK,OAAA,EAAA,CAAU,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,EAAA,EAAM,CAAA;AAC7D,EAAA,IAAI,MAAA,EAAQ,MAAA,CAAO,CAAC,EAAA,EAAK,OAAA,EAAA,CAAU,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,EAAA,EAAM,CAAA;AAC7D,EAAA,IAAI,MAAA,EAAQ,MAAA,CAAO,CAAC,EAAA,EAAK,OAAA,EAAA,CAAU,MAAA,CAAO,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,EAAA,EAAM,CAAA;AAC7D,EAAA,OAAO;AAAA,IACL,CAAC,KAAA,EAAO,KAAK,CAAA;AAAA,IACb,CAAC,KAAA,EAAO,KAAK;AAAA,EACf,CAAA;AACF;AAGA,IAAO,yBAAA,EAAQ,UAAA;ADVf;AACE;AACA;AACF,4EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-offset/dist/cjs/index.cjs", "sourcesContent": [null, "import { flattenEach } from \"@turf/meta\";\nimport { getCoords, getType } from \"@turf/invariant\";\nimport {\n  isObject,\n  lineString,\n  multiLineString,\n  lengthToDegrees,\n} from \"@turf/helpers\";\nimport { intersection } from \"./lib/intersection.js\";\n\n/**\n * Takes a {@link LineString|line} and returns a {@link LineString|line} at offset by the specified distance.\n *\n * @function\n * @param {Geometry|Feature<LineString|MultiLineString>} geojson input GeoJSON\n * @param {number} distance distance to offset the line (can be of negative value)\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] can be degrees, radians, miles, kilometers, inches, yards, meters\n * @returns {Feature<LineString|MultiLineString>} Line offset from the input line\n * @example\n * var line = turf.lineString([[-83, 30], [-84, 36], [-78, 41]], { \"stroke\": \"#F00\" });\n *\n * var offsetLine = turf.lineOffset(line, 2, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [offsetLine, line]\n * offsetLine.properties.stroke = \"#00F\"\n */\nfunction lineOffset(geojson, distance, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var units = options.units;\n\n  // Valdiation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (distance === undefined || distance === null || isNaN(distance))\n    throw new Error(\"distance is required\");\n\n  var type = getType(geojson);\n  var properties = geojson.properties;\n\n  switch (type) {\n    case \"LineString\":\n      return lineOffsetFeature(geojson, distance, units);\n    case \"MultiLineString\":\n      var coords = [];\n      flattenEach(geojson, function (feature) {\n        coords.push(\n          lineOffsetFeature(feature, distance, units).geometry.coordinates\n        );\n      });\n      return multiLineString(coords, properties);\n    default:\n      throw new Error(\"geometry \" + type + \" is not supported\");\n  }\n}\n\n/**\n * Line Offset\n *\n * @private\n * @param {Geometry|Feature<LineString>} line input line\n * @param {number} distance distance to offset the line (can be of negative value)\n * @param {string} [units=kilometers] units\n * @returns {Feature<LineString>} Line offset from the input line\n */\nfunction lineOffsetFeature(line, distance, units) {\n  var segments = [];\n  var offsetDegrees = lengthToDegrees(distance, units);\n  var coords = getCoords(line);\n  var finalCoords = [];\n  coords.forEach(function (currentCoords, index) {\n    if (index !== coords.length - 1) {\n      var segment = processSegment(\n        currentCoords,\n        coords[index + 1],\n        offsetDegrees\n      );\n      segments.push(segment);\n      if (index > 0) {\n        var seg2Coords = segments[index - 1];\n        var intersects = intersection(segment, seg2Coords);\n\n        // Handling for line segments that aren't straight\n        if (intersects !== false) {\n          seg2Coords[1] = intersects;\n          segment[0] = intersects;\n        }\n\n        finalCoords.push(seg2Coords[0]);\n        if (index === coords.length - 2) {\n          finalCoords.push(segment[0]);\n          finalCoords.push(segment[1]);\n        }\n      }\n      // Handling for lines that only have 1 segment\n      if (coords.length === 2) {\n        finalCoords.push(segment[0]);\n        finalCoords.push(segment[1]);\n      }\n    }\n  });\n  return lineString(finalCoords, line.properties);\n}\n\n/**\n * Process Segment\n * Inspiration taken from http://stackoverflow.com/questions/2825412/draw-a-parallel-line\n *\n * @private\n * @param {Array<number>} point1 Point coordinates\n * @param {Array<number>} point2 Point coordinates\n * @param {number} offset Offset\n * @returns {Array<Array<number>>} offset points\n */\nfunction processSegment(point1, point2, offset) {\n  var L = Math.sqrt(\n    (point1[0] - point2[0]) * (point1[0] - point2[0]) +\n      (point1[1] - point2[1]) * (point1[1] - point2[1])\n  );\n\n  var out1x = point1[0] + (offset * (point2[1] - point1[1])) / L;\n  var out2x = point2[0] + (offset * (point2[1] - point1[1])) / L;\n  var out1y = point1[1] + (offset * (point1[0] - point2[0])) / L;\n  var out2y = point2[1] + (offset * (point1[0] - point2[0])) / L;\n  return [\n    [out1x, out1y],\n    [out2x, out2y],\n  ];\n}\n\nexport { lineOffset };\nexport default lineOffset;\n", "/**\n * https://github.com/rook2pawn/node-intersection\n *\n * Author @rook2pawn\n */\n\n/**\n * AB\n *\n * @private\n * @param {Array<Array<number>>} segment - 2 vertex line segment\n * @returns {Array<number>} coordinates [x, y]\n */\nfunction ab(segment) {\n  var start = segment[0];\n  var end = segment[1];\n  return [end[0] - start[0], end[1] - start[1]];\n}\n\n/**\n * Cross Product\n *\n * @private\n * @param {Array<number>} v1 coordinates [x, y]\n * @param {Array<number>} v2 coordinates [x, y]\n * @returns {Array<number>} Cross Product\n */\nfunction crossProduct(v1, v2) {\n  return v1[0] * v2[1] - v2[0] * v1[1];\n}\n\n/**\n * Add\n *\n * @private\n * @param {Array<number>} v1 coordinates [x, y]\n * @param {Array<number>} v2 coordinates [x, y]\n * @returns {Array<number>} Add\n */\nfunction add(v1, v2) {\n  return [v1[0] + v2[0], v1[1] + v2[1]];\n}\n\n/**\n * Sub\n *\n * @private\n * @param {Array<number>} v1 coordinates [x, y]\n * @param {Array<number>} v2 coordinates [x, y]\n * @returns {Array<number>} Sub\n */\nfunction sub(v1, v2) {\n  return [v1[0] - v2[0], v1[1] - v2[1]];\n}\n\n/**\n * scalarMult\n *\n * @private\n * @param {number} s scalar\n * @param {Array<number>} v coordinates [x, y]\n * @returns {Array<number>} scalarMult\n */\nfunction scalarMult(s, v) {\n  return [s * v[0], s * v[1]];\n}\n\n/**\n * Intersect Segments\n *\n * @private\n * @param {Array<number>} a coordinates [x, y]\n * @param {Array<number>} b coordinates [x, y]\n * @returns {Array<number>} intersection\n */\nfunction intersectSegments(a, b) {\n  var p = a[0];\n  var r = ab(a);\n  var q = b[0];\n  var s = ab(b);\n\n  var cross = crossProduct(r, s);\n  var qmp = sub(q, p);\n  var numerator = crossProduct(qmp, s);\n  var t = numerator / cross;\n  var intersection = add(p, scalarMult(t, r));\n  return intersection;\n}\n\n/**\n * Is Parallel\n *\n * @private\n * @param {Array<number>} a coordinates [x, y]\n * @param {Array<number>} b coordinates [x, y]\n * @returns {boolean} true if a and b are parallel (or co-linear)\n */\nfunction isParallel(a, b) {\n  var r = ab(a);\n  var s = ab(b);\n  return crossProduct(r, s) === 0;\n}\n\n/**\n * Intersection\n *\n * @private\n * @param {Array<number>} a coordinates [x, y]\n * @param {Array<number>} b coordinates [x, y]\n * @returns {Array<number>|boolean} true if a and b are parallel (or co-linear)\n */\nfunction intersection(a, b) {\n  if (isParallel(a, b)) return false;\n  return intersectSegments(a, b);\n}\n\nexport { intersection };\nexport default intersection;\n"]}