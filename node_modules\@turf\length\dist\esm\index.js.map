{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, FeatureCollection, GeometryCollection } from \"geojson\";\nimport { distance } from \"@turf/distance\";\nimport { Units } from \"@turf/helpers\";\nimport { segmentReduce } from \"@turf/meta\";\n\n/**\n * Takes a {@link GeoJSON} and measures its length in the specified units, {@link (Multi)Point}'s distance are ignored.\n *\n * @function\n * @param {Feature<LineString|MultiLineString>} geojson GeoJSON to measure\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units=kilometers] can be degrees, radians, miles, or kilometers\n * @returns {number} length of GeoJSON\n * @example\n * var line = turf.lineString([[115, -32], [131, -22], [143, -25], [150, -34]]);\n * var length = turf.length(line, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [line];\n * line.properties.distance = length;\n */\nfunction length(\n  geojson: Feature<any> | FeatureCollection<any> | GeometryCollection,\n  options: {\n    units?: Units;\n  } = {}\n): number {\n  // Calculate distance from 2-vertex line segments\n  return segmentReduce(\n    geojson,\n    (previousValue, segment) => {\n      const coords = segment!.geometry.coordinates;\n      return previousValue! + distance(coords[0], coords[1], options);\n    },\n    0\n  );\n}\n\nexport { length };\nexport default length;\n"], "mappings": ";AACA,SAAS,gBAAgB;AAEzB,SAAS,qBAAqB;AAkB9B,SAAS,OACP,SACA,UAEI,CAAC,GACG;AAER,SAAO;AAAA,IACL;AAAA,IACA,CAAC,eAAe,YAAY;AAC1B,YAAM,SAAS,QAAS,SAAS;AACjC,aAAO,gBAAiB,SAAS,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO;AAAA,IAChE;AAAA,IACA;AAAA,EACF;AACF;AAGA,IAAO,sBAAQ;", "names": []}