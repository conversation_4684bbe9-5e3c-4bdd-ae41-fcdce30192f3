{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-valid/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,4CAAwB;AACxB,wCAAoC;AACpC,yDAAgC;AAChC,uDAA+B;AAC/B,qDAA8B;AAC9B,iEAAoD;AAcpD,SAAS,YAAA,CAAa,OAAA,EAAkC;AAEtD,EAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,IAAA,EAAM,OAAO,KAAA;AAG1B,EAAA,MAAM,KAAA,EAAO,gCAAA,OAAe,CAAA;AAC5B,EAAA,MAAM,KAAA,EAAO,IAAA,CAAK,IAAA;AAClB,EAAA,MAAM,OAAA,EAAS,IAAA,CAAK,WAAA;AAEpB,EAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,IACZ,KAAK,OAAA;AACH,MAAA,OAAO,MAAA,CAAO,OAAA,EAAS,CAAA;AAAA,IACzB,KAAK,YAAA;AACH,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,QAAA,GAAA,CAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,EAAG,OAAO,KAAA;AAAA,MACnC;AACA,MAAA,OAAO,IAAA;AAAA,IACT,KAAK,YAAA;AACH,MAAA,GAAA,CAAI,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG,OAAO,KAAA;AAC9B,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,QAAA,GAAA,CAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,EAAG,OAAO,KAAA;AAAA,MACnC;AACA,MAAA,OAAO,IAAA;AAAA,IACT,KAAK,iBAAA;AACH,MAAA,GAAA,CAAI,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG,OAAO,KAAA;AAC9B,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,QAAA,GAAA,CAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,EAAG,OAAO,KAAA;AAAA,MACnC;AACA,MAAA,OAAO,IAAA;AAAA,IACT,KAAK,SAAA;AACH,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AAChD,QAAA,GAAA,CAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,EAAG,OAAO,KAAA;AACjC,QAAA,GAAA,CAAI,CAAC,eAAA,CAAgB,MAAA,CAAO,CAAC,CAAC,CAAA,EAAG,OAAO,KAAA;AACxC,QAAA,GAAA,CAAI,4BAAA,CAA6B,MAAA,CAAO,CAAC,CAAC,CAAA,EAAG,OAAO,KAAA;AACpD,QAAA,GAAA,CAAI,EAAA,EAAI,CAAA,EAAG;AACT,UAAA,GAAA,CACE,0CAAA,8BAAc,CAAS,MAAA,CAAO,CAAC,CAAC,CAAC,CAAA,EAAG,8BAAA,CAAS,MAAA,CAAO,CAAC,CAAC,CAAC,CAAC,CAAA,CAAE,QAAA,CACvD,OAAA,EAAS,CAAA;AAEZ,YAAA,OAAO,KAAA;AAAA,QACX;AAAA,MACF;AACA,MAAA,OAAO,IAAA;AAAA,IACT,KAAK,cAAA;AACH,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AAChD,QAAA,IAAI,KAAA,EAAY,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA;AAElC,QAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,IAAA,CAAK,MAAA,EAAQ,EAAA,EAAA,EAAM;AACvC,UAAA,GAAA,CAAI,IAAA,CAAK,EAAE,CAAA,CAAE,OAAA,EAAS,CAAA,EAAG,OAAO,KAAA;AAChC,UAAA,GAAA,CAAI,CAAC,eAAA,CAAgB,IAAA,CAAK,EAAE,CAAC,CAAA,EAAG,OAAO,KAAA;AACvC,UAAA,GAAA,CAAI,4BAAA,CAA6B,IAAA,CAAK,EAAE,CAAC,CAAA,EAAG,OAAO,KAAA;AACnD,UAAA,GAAA,CAAI,GAAA,IAAO,CAAA,EAAG;AACZ,YAAA,GAAA,CAAI,CAAC,yBAAA,CAA0B,IAAA,EAAM,IAAA,CAAK,WAAA,EAAa,CAAC,CAAA;AACtD,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,GAAA,CAAI,GAAA,EAAK,CAAA,EAAG;AACV,YAAA,GAAA,CACE,0CAAA,8BAAc,CAAS,IAAA,CAAK,CAAC,CAAC,CAAC,CAAA,EAAG,8BAAA,CAAS,IAAA,CAAK,EAAE,CAAC,CAAC,CAAC,CAAA,CAAE,QAAA,CACpD,OAAA,EAAS,CAAA;AAEZ,cAAA,OAAO,KAAA;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,MAAA,OAAO,IAAA;AAAA,IACT,OAAA;AACE,MAAA,OAAO,KAAA;AAAA,EACX;AACF;AAEA,SAAS,eAAA,CAAgB,IAAA,EAAkB;AACzC,EAAA,OACE,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,IAAA,CAAK,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA,CAAE,CAAC,EAAA,GACtC,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,IAAA,CAAK,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA,CAAE,CAAC,CAAA;AAE1C;AAEA,SAAS,4BAAA,CAA6B,IAAA,EAAkB;AACtD,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AACxC,IAAA,IAAI,MAAA,EAAQ,IAAA,CAAK,CAAC,CAAA;AAClB,IAAA,IAAA,CAAA,IAAS,GAAA,EAAK,EAAA,EAAI,CAAA,EAAG,GAAA,EAAK,IAAA,CAAK,OAAA,EAAS,CAAA,EAAG,EAAA,EAAA,EAAM;AAC/C,MAAA,IAAI,IAAA,EAAM,CAAC,IAAA,CAAK,EAAE,CAAA,EAAG,IAAA,CAAK,GAAA,EAAK,CAAC,CAAC,CAAA;AACjC,MAAA,GAAA,CAAI,oDAAA,KAAc,EAAO,iCAAA,GAAc,CAAC,CAAA,EAAG,OAAO,IAAA;AAAA,IACpD;AAAA,EACF;AACA,EAAA,OAAO,KAAA;AACT;AAEA,SAAS,yBAAA,CACP,IAAA,EACA,IAAA,EACA,KAAA,EACA;AACA,EAAA,IAAI,YAAA,EAAc,8BAAA,IAAY,CAAA;AAC9B,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,MAAA,EAAQ,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC5C,IAAA,GAAA,CAAI,CAAC,8CAAA,WAAgB,EAAa,8BAAA,IAAQ,CAAK,CAAC,CAAC,CAAC,CAAA,EAAG;AACnD,MAAA,GAAA,CAAI,4CAAA,WAAe,EAAa,iCAAA,IAAW,CAAK,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA,EAAG,OAAO,KAAA;AAAA,IAClE;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,2BAAA,EAAQ,YAAA;ADnCf;AACE;AACA;AACF,kFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-valid/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Geometry, Position } from \"geojson\";\nimport { getGeom } from \"@turf/invariant\";\nimport { polygon, lineString } from \"@turf/helpers\";\nimport { booleanDisjoint } from \"@turf/boolean-disjoint\";\nimport { booleanCrosses } from \"@turf/boolean-crosses\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { booleanPointOnLine as isPointOnLine } from \"@turf/boolean-point-on-line\";\n\n/**\n * booleanValid checks if the geometry is a valid according to the OGC Simple Feature Specification.\n *\n * @function\n * @param {Geometry|Feature<any>} feature GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n *\n * turf.booleanValid(line); // => true\n * turf.booleanValid({foo: \"bar\"}); // => false\n */\nfunction booleanValid(feature: Feature<any> | Geometry) {\n  // Automatic False\n  if (!feature.type) return false;\n\n  // Parse GeoJSON\n  const geom = getGeom(feature);\n  const type = geom.type;\n  const coords = geom.coordinates;\n\n  switch (type) {\n    case \"Point\":\n      return coords.length > 1;\n    case \"MultiPoint\":\n      for (var i = 0; i < coords.length; i++) {\n        if (coords[i].length < 2) return false;\n      }\n      return true;\n    case \"LineString\":\n      if (coords.length < 2) return false;\n      for (var i = 0; i < coords.length; i++) {\n        if (coords[i].length < 2) return false;\n      }\n      return true;\n    case \"MultiLineString\":\n      if (coords.length < 2) return false;\n      for (var i = 0; i < coords.length; i++) {\n        if (coords[i].length < 2) return false;\n      }\n      return true;\n    case \"Polygon\":\n      for (var i = 0; i < geom.coordinates.length; i++) {\n        if (coords[i].length < 4) return false;\n        if (!checkRingsClose(coords[i])) return false;\n        if (checkRingsForSpikesPunctures(coords[i])) return false;\n        if (i > 0) {\n          if (\n            lineIntersect(polygon([coords[0]]), polygon([coords[i]])).features\n              .length > 1\n          )\n            return false;\n        }\n      }\n      return true;\n    case \"MultiPolygon\":\n      for (var i = 0; i < geom.coordinates.length; i++) {\n        var poly: any = geom.coordinates[i];\n\n        for (var ii = 0; ii < poly.length; ii++) {\n          if (poly[ii].length < 4) return false;\n          if (!checkRingsClose(poly[ii])) return false;\n          if (checkRingsForSpikesPunctures(poly[ii])) return false;\n          if (ii === 0) {\n            if (!checkPolygonAgainstOthers(poly, geom.coordinates, i))\n              return false;\n          }\n          if (ii > 0) {\n            if (\n              lineIntersect(polygon([poly[0]]), polygon([poly[ii]])).features\n                .length > 1\n            )\n              return false;\n          }\n        }\n      }\n      return true;\n    default:\n      return false;\n  }\n}\n\nfunction checkRingsClose(geom: Position[]) {\n  return (\n    geom[0][0] === geom[geom.length - 1][0] &&\n    geom[0][1] === geom[geom.length - 1][1]\n  );\n}\n\nfunction checkRingsForSpikesPunctures(geom: Position[]) {\n  for (var i = 0; i < geom.length - 1; i++) {\n    var point = geom[i];\n    for (var ii = i + 1; ii < geom.length - 2; ii++) {\n      var seg = [geom[ii], geom[ii + 1]];\n      if (isPointOnLine(point, lineString(seg))) return true;\n    }\n  }\n  return false;\n}\n\nfunction checkPolygonAgainstOthers(\n  poly: Position[][],\n  geom: Position[][][],\n  index: number\n) {\n  var polyToCheck = polygon(poly);\n  for (var i = index + 1; i < geom.length; i++) {\n    if (!booleanDisjoint(polyToCheck, polygon(geom[i]))) {\n      if (booleanCrosses(polyToCheck, lineString(geom[i][0]))) return false;\n    }\n  }\n  return true;\n}\n\nexport { booleanValid };\nexport default booleanValid;\n"]}