{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-overlap/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,mDAAsC;AACtC,iDAA4B;AAC5B,iEAAmC;AACnC,iEAAmC;AACnC,4CAA0B;AAC1B,kCAAyC;AAUzC,wCAA4C;AAC5C,8GAAkB;AAoBlB,SAAS,WAAA,CAIP,KAAA,EACA,KAAA,EACA,QAAA,EAAkC,CAAC,CAAA,EACJ;AAE/B,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,IAAI,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,CAAA;AAGrC,EAAA,IAAI,SAAA,EAAqD,CAAC,CAAA;AAG1D,EAAA,IAAI,KAAA,EAAO,wCAAA,CAAkB;AAG7B,EAAA,MAAM,KAAA,EAAY,sCAAA,KAAiB,CAAA;AACnC,EAAA,IAAA,CAAK,IAAA,CAAK,IAAI,CAAA;AACd,EAAA,IAAI,cAAA;AACJ,EAAA,IAAI,mBAAA,EAA4C,CAAC,CAAA;AAKjD,EAAA,+BAAA,KAAY,EAAO,QAAA,CAAU,OAAA,EAAS;AACpC,IAAA,IAAI,aAAA,EAAe,KAAA;AAEnB,IAAA,GAAA,CAAI,CAAC,OAAA,EAAS;AACZ,MAAA,MAAA;AAAA,IACF;AAGA,IAAA,+BAAA,IAAY,CAAK,MAAA,CAAO,OAAO,CAAA,EAAG,QAAA,CAAU,KAAA,EAAO;AACjD,MAAA,GAAA,CAAI,aAAA,IAAiB,KAAA,EAAO;AAC1B,QAAA,IAAI,cAAA,EAAgB,kCAAA,OAAiB,CAAA,CAAE,IAAA,CAAK,CAAA;AAC5C,QAAA,IAAI,YAAA,EAAmB,kCAAA,KAAe,CAAA,CAAE,IAAA,CAAK,CAAA;AAG7C,QAAA,GAAA,CAAI,qCAAA,aAAM,EAAe,WAAW,CAAA,EAAG;AACrC,UAAA,aAAA,EAAe,IAAA;AAEf,UAAA,GAAA,CAAI,cAAA,EAAgB;AAClB,YAAA,eAAA,EACE,aAAA,CAAc,cAAA,EAAgB,OAAO,EAAA,GAAK,cAAA;AAAA,UAC9C,EAAA,KAAO,eAAA,EAAiB,OAAA;AAAA,QAE1B,EAAA,KAAA,GAAA,CACE,UAAA,IAAc,EAAA,EACV,oDAAA,aAAmB,CAAc,CAAC,CAAA,EAAG,KAAK,EAAA,GAC1C,oDAAA,aAAmB,CAAc,CAAC,CAAA,EAAG,KAAK,EAAA,EAC1C,oDAAA,KAAmB,EAAO,aAAA,CAAc,CAAC,CAAC,CAAA,CAAE,UAAA,CAAW,KAAA,GACrD,UAAA,GACF,oDAAA,KAAmB,EAAO,aAAA,CAAc,CAAC,CAAC,CAAA,CAAE,UAAA,CAAW,KAAA,GACrD,SAAA,EACN;AACA,UAAA,aAAA,EAAe,IAAA;AACf,UAAA,GAAA,CAAI,cAAA,EAAgB;AAClB,YAAA,eAAA,EACE,aAAA,CAAc,cAAA,EAAgB,OAAO,EAAA,GAAK,cAAA;AAAA,UAC9C,EAAA,KAAO,eAAA,EAAiB,OAAA;AAAA,QAC1B,EAAA,KAAA,GAAA,CACE,UAAA,IAAc,EAAA,EACV,oDAAA,WAAmB,CAAY,CAAC,CAAA,EAAG,OAAO,EAAA,GAC1C,oDAAA,WAAmB,CAAY,CAAC,CAAA,EAAG,OAAO,EAAA,EAC1C,oDAAA,OAAmB,EAAS,WAAA,CAAY,CAAC,CAAC,CAAA,CAAE,UAAA,CAAW,KAAA,GACrD,UAAA,GACF,oDAAA,OAAmB,EAAS,WAAA,CAAY,CAAC,CAAC,CAAA,CAAE,UAAA,CAAW,KAAA,GACrD,SAAA,EACN;AAGA,UAAA,GAAA,CAAI,cAAA,EAAgB;AAClB,YAAA,MAAM,gBAAA,EAAkB,aAAA,CAAc,cAAA,EAAgB,KAAK,CAAA;AAC3D,YAAA,GAAA,CAAI,eAAA,EAAiB;AACnB,cAAA,eAAA,EAAiB,eAAA;AAAA,YACnB,EAAA,KAAO;AACL,cAAA,kBAAA,CAAmB,IAAA,CAAK,KAAK,CAAA;AAAA,YAC/B;AAAA,UACF,EAAA,KAAO,eAAA,EAAiB,KAAA;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,CAAC,CAAA;AAGD,IAAA,GAAA,CAAI,aAAA,IAAiB,MAAA,GAAS,cAAA,EAAgB;AAC5C,MAAA,QAAA,CAAS,IAAA,CAAK,cAAc,CAAA;AAC5B,MAAA,GAAA,CAAI,kBAAA,CAAmB,MAAA,EAAQ;AAC7B,QAAA,SAAA,EAAW,QAAA,CAAS,MAAA,CAAO,kBAAkB,CAAA;AAC7C,QAAA,mBAAA,EAAqB,CAAC,CAAA;AAAA,MACxB;AACA,MAAA,eAAA,EAAiB,KAAA,CAAA;AAAA,IACnB;AAAA,EACF,CAAC,CAAA;AAED,EAAA,GAAA,CAAI,cAAA,EAAgB,QAAA,CAAS,IAAA,CAAK,cAAc,CAAA;AAEhD,EAAA,OAAO,wCAAA,QAA0B,CAAA;AACnC;AAUA,SAAS,aAAA,CACP,IAAA,EACA,OAAA,EACA;AACA,EAAA,IAAI,OAAA,EAAS,kCAAA,OAAiB,CAAA;AAC9B,EAAA,IAAI,WAAA,EAAa,kCAAA,IAAc,CAAA;AAC/B,EAAA,IAAI,MAAA,EAAQ,UAAA,CAAW,CAAC,CAAA;AACxB,EAAA,IAAI,IAAA,EAAM,UAAA,CAAW,UAAA,CAAW,OAAA,EAAS,CAAC,CAAA;AAC1C,EAAA,IAAI,KAAA,EAAO,IAAA,CAAK,QAAA,CAAS,WAAA;AAEzB,EAAA,GAAA,CAAI,qCAAA,MAAM,CAAO,CAAC,CAAA,EAAG,KAAK,CAAA,EAAG,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,CAAC,CAAC,CAAA;AAAA,EAAA,KAAA,GAAA,CAC1C,qCAAA,MAAM,CAAO,CAAC,CAAA,EAAG,GAAG,CAAA,EAAG,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AAAA,EAAA,KAAA,GAAA,CAC1C,qCAAA,MAAM,CAAO,CAAC,CAAA,EAAG,KAAK,CAAA,EAAG,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,CAAC,CAAC,CAAA;AAAA,EAAA,KAAA,GAAA,CAC/C,qCAAA,MAAM,CAAO,CAAC,CAAA,EAAG,GAAG,CAAA,EAAG,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AAAA,EAAA,KAC9C,MAAA;AAGL,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,0BAAA,EAAQ,WAAA;AD5Ff;AACE;AACA;AACF,+EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-overlap/dist/cjs/index.cjs", "sourcesContent": [null, "import { geojsonRbush as rbush } from \"@turf/geojson-rbush\";\nimport { lineSegment } from \"@turf/line-segment\";\nimport { nearestPointOnLine } from \"@turf/nearest-point-on-line\";\nimport { booleanPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { getCoords } from \"@turf/invariant\";\nimport { featureEach, segmentEach } from \"@turf/meta\";\nimport {\n  FeatureCollection,\n  Feature,\n  LineString,\n  MultiLineString,\n  Polygon,\n  MultiPolygon,\n  GeoJsonProperties,\n} from \"geojson\";\nimport { featureCollection, isObject } from \"@turf/helpers\";\nimport equal from \"fast-deep-equal\";\n\n/**\n * Takes any LineString or Polygon and returns the overlapping lines between both features.\n *\n * @function\n * @param {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} line1 any LineString or Polygon\n * @param {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} line2 any LineString or Polygon\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.tolerance=0] Tolerance distance to match overlapping line segments (in kilometers)\n * @returns {FeatureCollection<LineString>} lines(s) that are overlapping between both features\n * @example\n * var line1 = turf.lineString([[115, -35], [125, -30], [135, -30], [145, -35]]);\n * var line2 = turf.lineString([[115, -25], [125, -30], [135, -30], [145, -25]]);\n *\n * var overlapping = turf.lineOverlap(line1, line2);\n *\n * //addToMap\n * var addToMap = [line1, line2, overlapping]\n */\nfunction lineOverlap<\n  G1 extends LineString | MultiLineString | Polygon | MultiPolygon,\n  G2 extends LineString | MultiLineString | Polygon | MultiPolygon,\n>(\n  line1: Feature<G1> | G1,\n  line2: Feature<G2> | G2,\n  options: { tolerance?: number } = {}\n): FeatureCollection<LineString> {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var tolerance = options.tolerance || 0;\n\n  // Containers\n  var features: Feature<LineString, GeoJsonProperties>[] = [];\n\n  // Create Spatial Index\n  var tree = rbush<LineString>();\n\n  // To-Do -- HACK way to support typescript\n  const line: any = lineSegment(line1);\n  tree.load(line);\n  var overlapSegment: Feature<LineString> | undefined;\n  let additionalSegments: Feature<LineString>[] = [];\n\n  // Line Intersection\n\n  // Iterate over line segments\n  segmentEach(line2, function (segment) {\n    var doesOverlaps = false;\n\n    if (!segment) {\n      return;\n    }\n\n    // Iterate over each segments which falls within the same bounds\n    featureEach(tree.search(segment), function (match) {\n      if (doesOverlaps === false) {\n        var coordsSegment = getCoords(segment).sort();\n        var coordsMatch: any = getCoords(match).sort();\n\n        // Segment overlaps feature\n        if (equal(coordsSegment, coordsMatch)) {\n          doesOverlaps = true;\n          // Overlaps already exists - only append last coordinate of segment\n          if (overlapSegment) {\n            overlapSegment =\n              concatSegment(overlapSegment, segment) || overlapSegment;\n          } else overlapSegment = segment;\n          // Match segments which don't share nodes (Issue #901)\n        } else if (\n          tolerance === 0\n            ? booleanPointOnLine(coordsSegment[0], match) &&\n              booleanPointOnLine(coordsSegment[1], match)\n            : nearestPointOnLine(match, coordsSegment[0]).properties.dist! <=\n                tolerance &&\n              nearestPointOnLine(match, coordsSegment[1]).properties.dist! <=\n                tolerance\n        ) {\n          doesOverlaps = true;\n          if (overlapSegment) {\n            overlapSegment =\n              concatSegment(overlapSegment, segment) || overlapSegment;\n          } else overlapSegment = segment;\n        } else if (\n          tolerance === 0\n            ? booleanPointOnLine(coordsMatch[0], segment) &&\n              booleanPointOnLine(coordsMatch[1], segment)\n            : nearestPointOnLine(segment, coordsMatch[0]).properties.dist! <=\n                tolerance &&\n              nearestPointOnLine(segment, coordsMatch[1]).properties.dist! <=\n                tolerance\n        ) {\n          // Do not define (doesOverlap = true) since more matches can occur within the same segment\n          // doesOverlaps = true;\n          if (overlapSegment) {\n            const combinedSegment = concatSegment(overlapSegment, match);\n            if (combinedSegment) {\n              overlapSegment = combinedSegment;\n            } else {\n              additionalSegments.push(match);\n            }\n          } else overlapSegment = match;\n        }\n      }\n    });\n\n    // Segment doesn't overlap - add overlaps to results & reset\n    if (doesOverlaps === false && overlapSegment) {\n      features.push(overlapSegment);\n      if (additionalSegments.length) {\n        features = features.concat(additionalSegments);\n        additionalSegments = [];\n      }\n      overlapSegment = undefined;\n    }\n  });\n  // Add last segment if exists\n  if (overlapSegment) features.push(overlapSegment);\n\n  return featureCollection(features);\n}\n\n/**\n * Concat Segment\n *\n * @private\n * @param {Feature<LineString>} line LineString\n * @param {Feature<LineString>} segment 2-vertex LineString\n * @returns {Feature<LineString>} concat linestring\n */\nfunction concatSegment(\n  line: Feature<LineString>,\n  segment: Feature<LineString>\n) {\n  var coords = getCoords(segment);\n  var lineCoords = getCoords(line);\n  var start = lineCoords[0];\n  var end = lineCoords[lineCoords.length - 1];\n  var geom = line.geometry.coordinates;\n\n  if (equal(coords[0], start)) geom.unshift(coords[1]);\n  else if (equal(coords[0], end)) geom.push(coords[1]);\n  else if (equal(coords[1], start)) geom.unshift(coords[0]);\n  else if (equal(coords[1], end)) geom.push(coords[0]);\n  else return; // If the overlap leaves the segment unchanged, return undefined so that this can be identified.\n\n  // Otherwise return the mutated line.\n  return line;\n}\n\nexport { lineOverlap };\nexport default lineOverlap;\n"]}