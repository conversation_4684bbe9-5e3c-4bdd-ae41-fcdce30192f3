"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// index.ts
var _helpers = require('@turf/helpers');

// lib/sweepline-intersections-export.ts
var _sweeplineintersections = require('sweepline-intersections'); var _sweeplineintersections2 = _interopRequireDefault(_sweeplineintersections);
var sweeplineIntersections = _sweeplineintersections2.default;

// index.ts
function lineIntersect(line1, line2, options = {}) {
  const { removeDuplicates = true, ignoreSelfIntersections = true } = options;
  let features = [];
  if (line1.type === "FeatureCollection")
    features = features.concat(line1.features);
  else if (line1.type === "Feature") features.push(line1);
  else if (line1.type === "LineString" || line1.type === "Polygon" || line1.type === "MultiLineString" || line1.type === "MultiPolygon") {
    features.push(_helpers.feature.call(void 0, line1));
  }
  if (line2.type === "FeatureCollection")
    features = features.concat(line2.features);
  else if (line2.type === "Feature") features.push(line2);
  else if (line2.type === "LineString" || line2.type === "Polygon" || line2.type === "MultiLineString" || line2.type === "MultiPolygon") {
    features.push(_helpers.feature.call(void 0, line2));
  }
  const intersections = sweeplineIntersections(
    _helpers.featureCollection.call(void 0, features),
    ignoreSelfIntersections
  );
  let results = [];
  if (removeDuplicates) {
    const unique = {};
    intersections.forEach((intersection) => {
      const key = intersection.join(",");
      if (!unique[key]) {
        unique[key] = true;
        results.push(intersection);
      }
    });
  } else {
    results = intersections;
  }
  return _helpers.featureCollection.call(void 0, results.map((r) => _helpers.point.call(void 0, r)));
}
var turf_line_intersect_default = lineIntersect;



exports.default = turf_line_intersect_default; exports.lineIntersect = lineIntersect;
//# sourceMappingURL=index.cjs.map