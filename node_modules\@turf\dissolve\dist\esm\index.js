// index.ts
import { featureCollection, isObject, multiPolygon } from "@turf/helpers";
import { collectionOf } from "@turf/invariant";
import { featureEach } from "@turf/meta";
import { flatten } from "@turf/flatten";
import * as polyclip from "polyclip-ts";
function dissolve(fc, options = {}) {
  options = options || {};
  if (!isObject(options)) throw new Error("options is invalid");
  const { propertyName } = options;
  collectionOf(fc, "Polygon", "dissolve");
  const outFeatures = [];
  if (!propertyName) {
    return flatten(
      multiPolygon(
        polyclip.union.apply(
          null,
          // List of polygons expressed as Position[][][] a.k.a. Geom[]
          fc.features.map(function(f) {
            return f.geometry.coordinates;
          })
        )
      )
    );
  } else {
    const uniquePropertyVals = {};
    featureEach(fc, function(feature) {
      if (feature.properties) {
        if (!Object.prototype.hasOwnProperty.call(
          uniquePropertyVals,
          feature.properties[propertyName]
        )) {
          uniquePropertyVals[feature.properties[propertyName]] = [];
        }
        uniquePropertyVals[feature.properties[propertyName]].push(feature);
      }
    });
    const vals = Object.keys(uniquePropertyVals);
    for (let i = 0; i < vals.length; i++) {
      const mp = multiPolygon(
        polyclip.union.apply(
          null,
          // List of polygons expressed as Position[][][] a.k.a. Geom[]
          uniquePropertyVals[vals[i]].map(function(f) {
            return f.geometry.coordinates;
          })
        )
      );
      if (mp && mp.properties) {
        mp.properties[propertyName] = vals[i];
        outFeatures.push(mp);
      }
    }
  }
  return flatten(featureCollection(outFeatures));
}
var turf_dissolve_default = dissolve;
export {
  turf_dissolve_default as default,
  dissolve
};
//# sourceMappingURL=index.js.map