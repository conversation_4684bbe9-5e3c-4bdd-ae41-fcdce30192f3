"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _centermean = require('@turf/center-mean');
var _distance = require('@turf/distance');
var _centroid = require('@turf/centroid');
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
function centerMedian(features, options = {}) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  var counter = options.counter || 10;
  if (!_helpers.isNumber.call(void 0, counter)) throw new Error("counter must be a number");
  var weightTerm = options.weight;
  var meanCenter = _centermean.centerMean.call(void 0, features, { weight: options.weight });
  var centroids = _helpers.featureCollection.call(void 0, []);
  _meta.featureEach.call(void 0, features, function(feature) {
    var _a;
    centroids.features.push(
      _centroid.centroid.call(void 0, feature, {
        properties: { weight: (_a = feature.properties) == null ? void 0 : _a[weightTerm] }
      })
    );
  });
  const properties = {
    tolerance: options.tolerance,
    medianCandidates: []
  };
  return findMedian(
    meanCenter.geometry.coordinates,
    [0, 0],
    centroids,
    properties,
    counter
  );
}
function findMedian(candidateMedian, previousCandidate, centroids, properties, counter) {
  var tolerance = properties.tolerance || 1e-3;
  var candidateXsum = 0;
  var candidateYsum = 0;
  var kSum = 0;
  var centroidCount = 0;
  _meta.featureEach.call(void 0, centroids, function(theCentroid) {
    var _a;
    var weightValue = (_a = theCentroid.properties) == null ? void 0 : _a.weight;
    var weight = weightValue === void 0 || weightValue === null ? 1 : weightValue;
    weight = Number(weight);
    if (!_helpers.isNumber.call(void 0, weight)) throw new Error("weight value must be a number");
    if (weight > 0) {
      centroidCount += 1;
      var distanceFromCandidate = weight * _distance.distance.call(void 0, theCentroid, candidateMedian);
      if (distanceFromCandidate === 0) distanceFromCandidate = 1;
      var k = weight / distanceFromCandidate;
      candidateXsum += theCentroid.geometry.coordinates[0] * k;
      candidateYsum += theCentroid.geometry.coordinates[1] * k;
      kSum += k;
    }
  });
  if (centroidCount < 1) throw new Error("no features to measure");
  var candidateX = candidateXsum / kSum;
  var candidateY = candidateYsum / kSum;
  if (centroidCount === 1 || counter === 0 || Math.abs(candidateX - previousCandidate[0]) < tolerance && Math.abs(candidateY - previousCandidate[1]) < tolerance) {
    return _helpers.point.call(void 0, [candidateX, candidateY], {
      medianCandidates: properties.medianCandidates
    });
  } else {
    properties.medianCandidates.push([candidateX, candidateY]);
    return findMedian(
      [candidateX, candidateY],
      candidateMedian,
      centroids,
      properties,
      counter - 1
    );
  }
}
var turf_center_median_default = centerMedian;



exports.centerMedian = centerMedian; exports.default = turf_center_median_default;
//# sourceMappingURL=index.cjs.map