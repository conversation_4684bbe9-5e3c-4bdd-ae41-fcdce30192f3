{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { coordEach, featureEach } from \"@turf/meta\";\nimport { point, featureCollection } from \"@turf/helpers\";\nimport type { AllGeoJSON } from \"@turf/helpers\";\nimport type { Feature, FeatureCollection, Point } from \"geojson\";\n\n/**\n * Takes a feature or set of features and returns all positions as {@link Point|points}.\n *\n * @function\n * @param {GeoJSON} geojson input features\n * @returns {FeatureCollection<point>} points representing the exploded input features\n * @throws {Error} if it encounters an unknown geometry type\n * @example\n * var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);\n *\n * var explode = turf.explode(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, explode]\n */\nfunction explode(geojson: AllGeoJSON): FeatureCollection<Point> {\n  const points: Feature<Point>[] = [];\n  if (geojson.type === \"FeatureCollection\") {\n    featureEach(geojson, function (feature) {\n      coordEach(feature, function (coord) {\n        points.push(point(coord, feature.properties));\n      });\n    });\n  } else if (geojson.type === \"Feature\") {\n    coordEach(geojson, function (coord) {\n      points.push(point(coord, geojson.properties));\n    });\n  } else {\n    // No properties to copy.\n    coordEach(geojson, function (coord) {\n      points.push(point(coord));\n    });\n  }\n\n  return featureCollection(points);\n}\n\nexport { explode };\nexport default explode;\n"], "mappings": ";AAAA,SAAS,WAAW,mBAAmB;AACvC,SAAS,OAAO,yBAAyB;AAmBzC,SAAS,QAAQ,SAA+C;AAC9D,QAAM,SAA2B,CAAC;AAClC,MAAI,QAAQ,SAAS,qBAAqB;AACxC,gBAAY,SAAS,SAAU,SAAS;AACtC,gBAAU,SAAS,SAAU,OAAO;AAClC,eAAO,KAAK,MAAM,OAAO,QAAQ,UAAU,CAAC;AAAA,MAC9C,CAAC;AAAA,IACH,CAAC;AAAA,EACH,WAAW,QAAQ,SAAS,WAAW;AACrC,cAAU,SAAS,SAAU,OAAO;AAClC,aAAO,KAAK,MAAM,OAAO,QAAQ,UAAU,CAAC;AAAA,IAC9C,CAAC;AAAA,EACH,OAAO;AAEL,cAAU,SAAS,SAAU,OAAO;AAClC,aAAO,KAAK,MAAM,KAAK,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AAEA,SAAO,kBAAkB,MAAM;AACjC;AAGA,IAAO,uBAAQ;", "names": []}