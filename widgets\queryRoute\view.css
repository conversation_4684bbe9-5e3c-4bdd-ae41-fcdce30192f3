.talbe_style {
    padding: 5px;
    border: 0;
    font-size: 13px;
}

.talbe_style tr td {
    padding: 2px;
    text-align: left;
}
.infoview .line {
	border-top: 1px solid #e6e6e6;
	margin: 5px;
}
.nametd{
	width: 50px;
}

.mapDH_FX{
    background-color: rgba(32, 160, 255, 0.2);
    color: white;
    width: 135px;
    line-height: 40px;
    border: none;
    border-radius: 4px;

}

.mapDH_FX_DIV{
    text-align: center
}

input,p{
    padding: 0;
}
input{
    border: none;
}
.clearfix{
    *zoom: 1;
}
.clearfix:after{
    content: '';
    display: block;
    clear: both;
    overflow: hidden;
}
.route_box{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}
td{
    height: 40px;
    color: #ffffff;
}
.title td{
    text-align: center;
    color: #ffffff;
}
.lxh{
    text-align: center;
}
.fa input{
    padding: 5px 10px;
    background: #4db3ff;
    color: #ffffff;
    border-radius: 5px;
}

.router_btn{
    margin-left: 10px;
}
.router_table{
    width: 99%;
    height: 100%;
}

.span_sjgs{
   display: none;
    margin-left: 5px;
}
.mcxd{
	display: none;    
	margin-top:10px;
	position: relative;
}


.clearfix{
	*zoom: 1;
}
.clearfix:after{
	content: '';
	display: block;
	clear: both;
	overflow: hidden;
}
.mapDH_title button{
	width: 120px;
	height: 40px;
	border: 1px solid #4793c7;
	float: left;
	margin-right: 10px;
	cursor: pointer;
	border-radius: 5px;
	font-size: 16px;
	color: #ffffff;
	background: none;
}

.mapDH_title .active{
    background: #20a0ff;
   
}
.mapDH_title{
    margin-left: 20%;
	margin-top: 10px;
}
.zbxd{
    margin: 10px;
}

.searchbox{
	width: 400px;
	overflow: hidden;
}
.seachcon{
	width: 999999px;
	float: left;
}
.searchinfo{
	width: 400px;
	box-sizing: border-box;
	padding: 10px 20px;
	float: left;
}

.inpbox{
	margin-bottom: 10px;
}
.ltit{
	float: left;
	color: #ffffff;
	line-height: 32px;
}
.text{
	width: 180px;
	height: 32px;
	float: left;
	border-radius: 5px;
	box-sizing: border-box;
	padding-left: 10px;
}
.btn{
	width: 82px;
	height: 32px;
	background: #20a0ff;
	color: #ffffff;
	margin-left: 10px;
	border-radius: 5px;
}
.searchval{
	width: 180px;
	height: 220px;
	box-sizing: border-box;
	padding: 10px;
	overflow-y: auto;
	background: #ffffff;
	/* margin: 10px 0 10px 70px; */
	display: none;
	position: absolute;
	left: 90px;
}

.startbox .searchval{
	top: 50px;
}
.endbox .searchval{
	top: 97px;
}

.searchval li{
	color: #333333;
	line-height: 30px;
}