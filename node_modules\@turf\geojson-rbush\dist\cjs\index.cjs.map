{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-geojson-rbush/dist/cjs/index.cjs", "../../index.js"], "names": [], "mappings": "AAAA;ACAA,4EAAkB;AAClB,wCAAkC;AAClC,kCAA4B;AAC5B,kCAAiC;AAiBjC,SAAS,YAAA,CAAa,UAAA,EAAY;AAChC,EAAA,IAAI,KAAA,EAAO,IAAI,oBAAA,CAAM,UAAU,CAAA;AAY/B,EAAA,IAAA,CAAK,OAAA,EAAS,QAAA,CAAU,OAAA,EAAS;AAC/B,IAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW,MAAM,IAAI,KAAA,CAAM,iBAAiB,CAAA;AACjE,IAAA,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,wBAAA,OAAgB,CAAA;AAC7D,IAAA,OAAO,eAAA,CAAM,SAAA,CAAU,MAAA,CAAO,IAAA,CAAK,IAAA,EAAM,OAAO,CAAA;AAAA,EAClD,CAAA;AAeA,EAAA,IAAA,CAAK,KAAA,EAAO,QAAA,CAAU,QAAA,EAAU;AAC9B,IAAA,IAAI,KAAA,EAAO,CAAC,CAAA;AAEZ,IAAA,GAAA,CAAI,KAAA,CAAM,OAAA,CAAQ,QAAQ,CAAA,EAAG;AAC3B,MAAA,QAAA,CAAS,OAAA,CAAQ,QAAA,CAAU,OAAA,EAAS;AAClC,QAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW,MAAM,IAAI,KAAA,CAAM,kBAAkB,CAAA;AAClE,QAAA,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,wBAAA,OAAgB,CAAA;AAC7D,QAAA,IAAA,CAAK,IAAA,CAAK,OAAO,CAAA;AAAA,MACnB,CAAC,CAAA;AAAA,IACH,EAAA,KAAO;AAEL,MAAA,+BAAA,QAAY,EAAU,QAAA,CAAU,OAAA,EAAS;AACvC,QAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW,MAAM,IAAI,KAAA,CAAM,kBAAkB,CAAA;AAClE,QAAA,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,wBAAA,OAAgB,CAAA;AAC7D,QAAA,IAAA,CAAK,IAAA,CAAK,OAAO,CAAA;AAAA,MACnB,CAAC,CAAA;AAAA,IACH;AACA,IAAA,OAAO,eAAA,CAAM,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,IAAA,EAAM,IAAI,CAAA;AAAA,EAC7C,CAAA;AAcA,EAAA,IAAA,CAAK,OAAA,EAAS,QAAA,CAAU,OAAA,EAAS,MAAA,EAAQ;AACvC,IAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW,MAAM,IAAI,KAAA,CAAM,iBAAiB,CAAA;AACjE,IAAA,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,OAAA,CAAQ,KAAA,EAAO,wBAAA,OAAgB,CAAA;AAC7D,IAAA,OAAO,eAAA,CAAM,SAAA,CAAU,MAAA,CAAO,IAAA,CAAK,IAAA,EAAM,OAAA,EAAS,MAAM,CAAA;AAAA,EAC1D,CAAA;AAUA,EAAA,IAAA,CAAK,MAAA,EAAQ,QAAA,CAAA,EAAY;AACvB,IAAA,OAAO,eAAA,CAAM,SAAA,CAAU,KAAA,CAAM,IAAA,CAAK,IAAI,CAAA;AAAA,EACxC,CAAA;AAaA,EAAA,IAAA,CAAK,OAAA,EAAS,QAAA,CAAU,OAAA,EAAS;AAC/B,IAAA,IAAI,SAAA,EAAW,eAAA,CAAM,SAAA,CAAU,MAAA,CAAO,IAAA,CAAK,IAAA,EAAM,IAAA,CAAK,MAAA,CAAO,OAAO,CAAC,CAAA;AACrE,IAAA,OAAO,wCAAA,QAA0B,CAAA;AAAA,EACnC,CAAA;AAaA,EAAA,IAAA,CAAK,SAAA,EAAW,QAAA,CAAU,OAAA,EAAS;AACjC,IAAA,OAAO,eAAA,CAAM,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,IAAA,EAAM,IAAA,CAAK,MAAA,CAAO,OAAO,CAAC,CAAA;AAAA,EACjE,CAAA;AAUA,EAAA,IAAA,CAAK,IAAA,EAAM,QAAA,CAAA,EAAY;AACrB,IAAA,IAAI,SAAA,EAAW,eAAA,CAAM,SAAA,CAAU,GAAA,CAAI,IAAA,CAAK,IAAI,CAAA;AAC5C,IAAA,OAAO,wCAAA,QAA0B,CAAA;AAAA,EACnC,CAAA;AAUA,EAAA,IAAA,CAAK,OAAA,EAAS,QAAA,CAAA,EAAY;AACxB,IAAA,OAAO,eAAA,CAAM,SAAA,CAAU,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA;AAAA,EACzC,CAAA;AA8BA,EAAA,IAAA,CAAK,SAAA,EAAW,QAAA,CAAU,IAAA,EAAM;AAC9B,IAAA,OAAO,eAAA,CAAM,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,IAAA,EAAM,IAAI,CAAA;AAAA,EACjD,CAAA;AAUA,EAAA,IAAA,CAAK,OAAA,EAAS,QAAA,CAAU,OAAA,EAAS;AAC/B,IAAA,IAAI,IAAA;AACJ,IAAA,GAAA,CAAI,OAAA,CAAQ,IAAA,EAAM,KAAA,EAAO,OAAA,CAAQ,IAAA;AAAA,IAAA,KAAA,GAAA,CACxB,KAAA,CAAM,OAAA,CAAQ,OAAO,EAAA,GAAK,OAAA,CAAQ,OAAA,IAAW,CAAA,EAAG,KAAA,EAAO,OAAA;AAAA,IAAA,KAAA,GAAA,CACvD,KAAA,CAAM,OAAA,CAAQ,OAAO,EAAA,GAAK,OAAA,CAAQ,OAAA,IAAW,CAAA;AACpD,MAAA,KAAA,EAAO,CAAC,OAAA,CAAQ,CAAC,CAAA,EAAG,OAAA,CAAQ,CAAC,CAAA,EAAG,OAAA,CAAQ,CAAC,CAAA,EAAG,OAAA,CAAQ,CAAC,CAAC,CAAA;AAAA,IAAA,KAAA,GAAA,CAC/C,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW,KAAA,EAAO,wBAAA,OAAgB,CAAA;AAAA,IAAA,KAAA,GAAA,CACnD,OAAA,CAAQ,KAAA,IAAS,mBAAA,EAAqB,KAAA,EAAO,wBAAA,OAAgB,CAAA;AAAA,IAAA,KACjE,MAAM,IAAI,KAAA,CAAM,iBAAiB,CAAA;AAEtC,IAAA,OAAO;AAAA,MACL,IAAA,EAAM,IAAA,CAAK,CAAC,CAAA;AAAA,MACZ,IAAA,EAAM,IAAA,CAAK,CAAC,CAAA;AAAA,MACZ,IAAA,EAAM,IAAA,CAAK,CAAC,CAAA;AAAA,MACZ,IAAA,EAAM,IAAA,CAAK,CAAC;AAAA,IACd,CAAA;AAAA,EACF,CAAA;AACA,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,2BAAA,EAAQ,YAAA;ADlJf;AACE;AACA;AACF,kFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-geojson-rbush/dist/cjs/index.cjs", "sourcesContent": [null, "import rbush from \"rbush\";\nimport { featureCollection } from \"@turf/helpers\";\nimport { featureEach } from \"@turf/meta\";\nimport { bbox as turfBBox } from \"@turf/bbox\";\n\n/**\n * @module rbush\n */\n\n/**\n * GeoJSON implementation of [RBush](https://github.com/mourner/rbush#rbush) spatial index.\n *\n * @function rbush\n * @param {number} [maxEntries=9] defines the maximum number of entries in a tree node. 9 (used by default) is a\n * reasonable choice for most applications. Higher value means faster insertion and slower search, and vice versa.\n * @returns {RBush} GeoJSON RBush\n * @example\n * var geojsonRbush = require('geojson-rbush').default;\n * var tree = geojsonRbush();\n */\nfunction geojsonRbush(maxEntries) {\n  var tree = new rbush(maxEntries);\n\n  /**\n   * [insert](https://github.com/mourner/rbush#data-format)\n   *\n   * @memberof rbush\n   * @param {Feature} feature insert single GeoJSON Feature\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   * tree.insert(poly)\n   */\n  tree.insert = function (feature) {\n    if (feature.type !== \"Feature\") throw new Error(\"invalid feature\");\n    feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n    return rbush.prototype.insert.call(this, feature);\n  };\n\n  /**\n   * [load](https://github.com/mourner/rbush#bulk-inserting-data)\n   *\n   * @memberof rbush\n   * @param {FeatureCollection|Array<Feature>} features load entire GeoJSON FeatureCollection\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var polys = turf.polygons([\n   *     [[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]],\n   *     [[[-93, 32], [-83, 32], [-83, 39], [-93, 39], [-93, 32]]]\n   * ]);\n   * tree.load(polys);\n   */\n  tree.load = function (features) {\n    var load = [];\n    // Load an Array of Features\n    if (Array.isArray(features)) {\n      features.forEach(function (feature) {\n        if (feature.type !== \"Feature\") throw new Error(\"invalid features\");\n        feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n        load.push(feature);\n      });\n    } else {\n      // Load a FeatureCollection\n      featureEach(features, function (feature) {\n        if (feature.type !== \"Feature\") throw new Error(\"invalid features\");\n        feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n        load.push(feature);\n      });\n    }\n    return rbush.prototype.load.call(this, load);\n  };\n\n  /**\n   * [remove](https://github.com/mourner/rbush#removing-data)\n   *\n   * @memberof rbush\n   * @param {Feature} feature remove single GeoJSON Feature\n   * @param {Function} equals Pass a custom equals function to compare by value for removal.\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   *\n   * tree.remove(poly);\n   */\n  tree.remove = function (feature, equals) {\n    if (feature.type !== \"Feature\") throw new Error(\"invalid feature\");\n    feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n    return rbush.prototype.remove.call(this, feature, equals);\n  };\n\n  /**\n   * [clear](https://github.com/mourner/rbush#removing-data)\n   *\n   * @memberof rbush\n   * @returns {RBush} GeoJSON Rbush\n   * @example\n   * tree.clear()\n   */\n  tree.clear = function () {\n    return rbush.prototype.clear.call(this);\n  };\n\n  /**\n   * [search](https://github.com/mourner/rbush#search)\n   *\n   * @memberof rbush\n   * @param {BBox|FeatureCollection|Feature} geojson search with GeoJSON\n   * @returns {FeatureCollection} all features that intersects with the given GeoJSON.\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   *\n   * tree.search(poly);\n   */\n  tree.search = function (geojson) {\n    var features = rbush.prototype.search.call(this, this.toBBox(geojson));\n    return featureCollection(features);\n  };\n\n  /**\n   * [collides](https://github.com/mourner/rbush#collisions)\n   *\n   * @memberof rbush\n   * @param {BBox|FeatureCollection|Feature} geojson collides with GeoJSON\n   * @returns {boolean} true if there are any items intersecting the given GeoJSON, otherwise false.\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   *\n   * tree.collides(poly);\n   */\n  tree.collides = function (geojson) {\n    return rbush.prototype.collides.call(this, this.toBBox(geojson));\n  };\n\n  /**\n   * [all](https://github.com/mourner/rbush#search)\n   *\n   * @memberof rbush\n   * @returns {FeatureCollection} all the features in RBush\n   * @example\n   * tree.all()\n   */\n  tree.all = function () {\n    var features = rbush.prototype.all.call(this);\n    return featureCollection(features);\n  };\n\n  /**\n   * [toJSON](https://github.com/mourner/rbush#export-and-import)\n   *\n   * @memberof rbush\n   * @returns {any} export data as JSON object\n   * @example\n   * var exported = tree.toJSON()\n   */\n  tree.toJSON = function () {\n    return rbush.prototype.toJSON.call(this);\n  };\n\n  /**\n   * [fromJSON](https://github.com/mourner/rbush#export-and-import)\n   *\n   * @memberof rbush\n   * @param {any} json import previously exported data\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var exported = {\n   *   \"children\": [\n   *     {\n   *       \"type\": \"Feature\",\n   *       \"geometry\": {\n   *         \"type\": \"Point\",\n   *         \"coordinates\": [110, 50]\n   *       },\n   *       \"properties\": {},\n   *       \"bbox\": [110, 50, 110, 50]\n   *     }\n   *   ],\n   *   \"height\": 1,\n   *   \"leaf\": true,\n   *   \"minX\": 110,\n   *   \"minY\": 50,\n   *   \"maxX\": 110,\n   *   \"maxY\": 50\n   * }\n   * tree.fromJSON(exported)\n   */\n  tree.fromJSON = function (json) {\n    return rbush.prototype.fromJSON.call(this, json);\n  };\n\n  /**\n   * Converts GeoJSON to {minX, minY, maxX, maxY} schema\n   *\n   * @memberof rbush\n   * @private\n   * @param {BBox|FeatureCollection|Feature} geojson feature(s) to retrieve BBox from\n   * @returns {Object} converted to {minX, minY, maxX, maxY}\n   */\n  tree.toBBox = function (geojson) {\n    var bbox;\n    if (geojson.bbox) bbox = geojson.bbox;\n    else if (Array.isArray(geojson) && geojson.length === 4) bbox = geojson;\n    else if (Array.isArray(geojson) && geojson.length === 6)\n      bbox = [geojson[0], geojson[1], geojson[3], geojson[4]];\n    else if (geojson.type === \"Feature\") bbox = turfBBox(geojson);\n    else if (geojson.type === \"FeatureCollection\") bbox = turfBBox(geojson);\n    else throw new Error(\"invalid geojson\");\n\n    return {\n      minX: bbox[0],\n      minY: bbox[1],\n      maxX: bbox[2],\n      maxY: bbox[3],\n    };\n  };\n  return tree;\n}\n\nexport { geojsonRbush };\nexport default geojsonRbush;\n"]}