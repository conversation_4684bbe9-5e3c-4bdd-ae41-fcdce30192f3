{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-flip/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,kCAA0B;AAC1B,wCAAqC;AACrC,oCAAsB;AAkBtB,SAAS,IAAA,CACP,OAAA,EACA,OAAA,EAGG;AAzBL,EAAA,IAAA,EAAA;AA2BE,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,MAAM,OAAA,EAAA,CAAS,GAAA,EAAA,OAAA,CAAQ,MAAA,EAAA,GAAR,KAAA,EAAA,GAAA,EAAkB,KAAA;AAEjC,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AAInD,EAAA,GAAA,CAAI,OAAA,IAAW,MAAA,GAAS,OAAA,IAAW,KAAA,CAAA,EAAW,QAAA,EAAU,0BAAA,OAAa,CAAA;AAErE,EAAA,6BAAA,OAAU,EAAS,QAAA,CAAU,KAAA,EAAO;AAClC,IAAA,IAAI,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA;AACf,IAAA,IAAI,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA;AACf,IAAA,KAAA,CAAM,CAAC,EAAA,EAAI,CAAA;AACX,IAAA,KAAA,CAAM,CAAC,EAAA,EAAI,CAAA;AAAA,EACb,CAAC,CAAA;AACD,EAAA,OAAO,OAAA;AACT;AAGA,IAAO,kBAAA,EAAQ,IAAA;AD3Bf;AACE;AACA;AACF,yDAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-flip/dist/cjs/index.cjs", "sourcesContent": [null, "import { coordEach } from \"@turf/meta\";\nimport { isObject, AllGeoJSON } from \"@turf/helpers\";\nimport { clone } from \"@turf/clone\";\n\n/**\n * Takes input features and flips all of their coordinates from `[x, y]` to `[y, x]`.\n *\n * @function\n * @param {GeoJSON} geojson input features\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.mutate=false] allows GeoJSON input to be mutated (significant performance increase if true)\n * @returns {GeoJSON} a feature or set of features of the same type as `input` with flipped coordinates\n * @example\n * var serbia = turf.point([20.566406, 43.421008]);\n *\n * var saudiArabia = turf.flip(serbia);\n *\n * //addToMap\n * var addToMap = [serbia, saudiArabia];\n */\nfunction flip<T extends AllGeoJSON>(\n  geojson: T,\n  options?: {\n    mutate?: boolean;\n  }\n): T {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const mutate = options.mutate ?? false;\n\n  if (!geojson) throw new Error(\"geojson is required\");\n  // ensure that we don't modify features in-place and changes to the\n  // output do not change the previous feature, including changes to nested\n  // properties.\n  if (mutate === false || mutate === undefined) geojson = clone(geojson);\n\n  coordEach(geojson, function (coord) {\n    var x = coord[0];\n    var y = coord[1];\n    coord[0] = y;\n    coord[1] = x;\n  });\n  return geojson;\n}\n\nexport { flip };\nexport default flip;\n"]}