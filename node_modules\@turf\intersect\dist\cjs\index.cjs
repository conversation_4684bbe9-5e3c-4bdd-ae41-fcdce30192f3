"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { newObj[key] = obj[key]; } } } newObj.default = obj; return newObj; } }// index.ts
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
var _polyclipts = require('polyclip-ts'); var polyclip = _interopRequireWildcard(_polyclipts);
function intersect(features, options = {}) {
  const geoms = [];
  _meta.geomEach.call(void 0, features, (geom) => {
    geoms.push(geom.coordinates);
  });
  if (geoms.length < 2) {
    throw new Error("Must specify at least 2 geometries");
  }
  const intersection2 = polyclip.intersection(geoms[0], ...geoms.slice(1));
  if (intersection2.length === 0) return null;
  if (intersection2.length === 1)
    return _helpers.polygon.call(void 0, intersection2[0], options.properties);
  return _helpers.multiPolygon.call(void 0, intersection2, options.properties);
}
var turf_intersect_default = intersect;



exports.default = turf_intersect_default; exports.intersect = intersect;
//# sourceMappingURL=index.cjs.map