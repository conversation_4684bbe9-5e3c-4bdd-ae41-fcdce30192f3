{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-intersect/dist/cjs/index.cjs", "../../index.ts"], "names": ["intersection"], "mappings": "AAAA;ACOA,wCAAsC;AACtC,kCAAyB;AACzB,8FAA0B;AAqC1B,SAAS,SAAA,CACP,QAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACsC;AAC3C,EAAA,MAAM,MAAA,EAAyB,CAAC,CAAA;AAEhC,EAAA,4BAAA,QAAS,EAAU,CAAC,IAAA,EAAA,GAAS;AAC3B,IAAA,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,WAA4B,CAAA;AAAA,EAC9C,CAAC,CAAA;AAED,EAAA,GAAA,CAAI,KAAA,CAAM,OAAA,EAAS,CAAA,EAAG;AACpB,IAAA,MAAM,IAAI,KAAA,CAAM,oCAAoC,CAAA;AAAA,EACtD;AACA,EAAA,MAAMA,cAAAA,EAAwB,QAAA,CAAA,YAAA,CAAa,KAAA,CAAM,CAAC,CAAA,EAAG,GAAG,KAAA,CAAM,KAAA,CAAM,CAAC,CAAC,CAAA;AACtE,EAAA,GAAA,CAAIA,aAAAA,CAAa,OAAA,IAAW,CAAA,EAAG,OAAO,IAAA;AACtC,EAAA,GAAA,CAAIA,aAAAA,CAAa,OAAA,IAAW,CAAA;AAC1B,IAAA,OAAO,8BAAA,aAAQA,CAAa,CAAC,CAAA,EAAG,OAAA,CAAQ,UAAU,CAAA;AACpD,EAAA,OAAO,mCAAA,aAAaA,EAAc,OAAA,CAAQ,UAAU,CAAA;AACtD;AAGA,IAAO,uBAAA,EAAQ,SAAA;ADlDf;AACE;AACA;AACF,wEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-intersect/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  Feature,\n  GeoJsonProperties,\n  MultiPolygon,\n  Polygon,\n  FeatureCollection,\n} from \"geojson\";\nimport { multiPolygon, polygon } from \"@turf/helpers\";\nimport { geomEach } from \"@turf/meta\";\nimport * as polyclip from \"polyclip-ts\";\n\n/**\n * Takes {@link Polygon|polygon} or {@link MultiPolygon|multi-polygon} geometries and\n * finds their polygonal intersection. If they don't intersect, returns null.\n *\n * @function\n * @param {FeatureCollection<Polygon | MultiPolygon>} features the features to intersect\n * @param {Object} [options={}] Optional Parameters\n * @param {Object} [options.properties={}] Translate GeoJSON Properties to Feature\n * @returns {Feature|null} returns a feature representing the area they share (either a {@link Polygon} or\n * {@link MultiPolygon}). If they do not share any area, returns `null`.\n * @example\n * var poly1 = turf.polygon([[\n *   [-122.801742, 45.48565],\n *   [-122.801742, 45.60491],\n *   [-122.584762, 45.60491],\n *   [-122.584762, 45.48565],\n *   [-122.801742, 45.48565]\n * ]]);\n *\n * var poly2 = turf.polygon([[\n *   [-122.520217, 45.535693],\n *   [-122.64038, 45.553967],\n *   [-122.720031, 45.526554],\n *   [-122.669906, 45.507309],\n *   [-122.723464, 45.446643],\n *   [-122.532577, 45.408574],\n *   [-122.487258, 45.477466],\n *   [-122.520217, 45.535693]\n * ]]);\n *\n * var intersection = turf.intersect(turf.featureCollection([poly1, poly2]));\n *\n * //addToMap\n * var addToMap = [poly1, poly2, intersection];\n */\nfunction intersect<P extends GeoJsonProperties = GeoJsonProperties>(\n  features: FeatureCollection<Polygon | MultiPolygon>,\n  options: {\n    properties?: P;\n  } = {}\n): Feature<Polygon | MultiPolygon, P> | null {\n  const geoms: polyclip.Geom[] = [];\n\n  geomEach(features, (geom) => {\n    geoms.push(geom.coordinates as polyclip.Geom);\n  });\n\n  if (geoms.length < 2) {\n    throw new Error(\"Must specify at least 2 geometries\");\n  }\n  const intersection = polyclip.intersection(geoms[0], ...geoms.slice(1));\n  if (intersection.length === 0) return null;\n  if (intersection.length === 1)\n    return polygon(intersection[0], options.properties);\n  return multiPolygon(intersection, options.properties);\n}\n\nexport { intersect };\nexport default intersect;\n"]}