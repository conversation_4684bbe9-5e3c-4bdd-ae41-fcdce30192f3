import { GeoJsonProperties, Feature, LineString } from 'geojson';

/**
 * Takes a {@link LineString|line} and returns a curved version
 * by applying a [Bezier spline](http://en.wikipedia.org/wiki/B%C3%A9zier_spline)
 * algorithm.
 *
 * The bezier spline implementation is by [<PERSON><PERSON><PERSON>](http://leszek.rybicki.cc/).
 *
 * @function
 * @param {Feature<LineString>} line input LineString
 * @param {Object} [options={}] Optional parameters
 * @param {Object} [options.properties={}] Translate properties to output
 * @param {number} [options.resolution=10000] time in milliseconds between points
 * @param {number} [options.sharpness=0.85] a measure of how curvy the path should be between splines
 * @returns {Feature<LineString>} curved line
 * @example
 * var line = turf.lineString([
 *   [-76.091308, 18.427501],
 *   [-76.695556, 18.729501],
 *   [-76.552734, 19.40443],
 *   [-74.61914, 19.134789],
 *   [-73.652343, 20.07657],
 *   [-73.157958, 20.210656]
 * ]);
 *
 * var curved = turf.bezierSpline(line);
 *
 * //addToMap
 * var addToMap = [line, curved]
 * curved.properties = { stroke: '#0F0' };
 */
declare function bezierSpline<P extends GeoJsonProperties = GeoJsonProperties>(line: Feature<LineString> | LineString, options?: {
    properties?: P;
    resolution?: number;
    sharpness?: number;
}): Feature<LineString, P>;

export { bezierSpline, bezierSpline as default };
