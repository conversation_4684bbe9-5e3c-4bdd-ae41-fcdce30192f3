"use strict";Object.defineProperty(exports, "__esModule", {value: true});var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};

// index.ts
var _bbox = require('@turf/bbox');
var _area = require('@turf/area');
var _booleanpointinpolygon = require('@turf/boolean-point-in-polygon');
var _explode = require('@turf/explode');
var _invariant = require('@turf/invariant');





var _helpers = require('@turf/helpers');

// lib/grid-to-matrix.js

var _meta = require('@turf/meta');

function gridToMatrix(grid, options) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  var zProperty = options.zProperty || "elevation";
  var flip = options.flip;
  var flags = options.flags;
  _invariant.collectionOf.call(void 0, grid, "Point", "input must contain Points");
  var pointsMatrix = sortPointsByLatLng(grid, flip);
  var matrix = [];
  for (var r = 0; r < pointsMatrix.length; r++) {
    var pointRow = pointsMatrix[r];
    var row = [];
    for (var c = 0; c < pointRow.length; c++) {
      var point = pointRow[c];
      if (point.properties[zProperty]) row.push(point.properties[zProperty]);
      else row.push(0);
      if (flags === true) point.properties.matrixPosition = [r, c];
    }
    matrix.push(row);
  }
  return matrix;
}
function sortPointsByLatLng(points, flip) {
  var pointsByLatitude = {};
  _meta.featureEach.call(void 0, points, function(point) {
    var lat = _invariant.getCoords.call(void 0, point)[1];
    if (!pointsByLatitude[lat]) pointsByLatitude[lat] = [];
    pointsByLatitude[lat].push(point);
  });
  var orderedRowsByLatitude = Object.keys(pointsByLatitude).map(function(lat) {
    var row = pointsByLatitude[lat];
    var rowOrderedByLongitude = row.sort(function(a, b) {
      return _invariant.getCoords.call(void 0, a)[0] - _invariant.getCoords.call(void 0, b)[0];
    });
    return rowOrderedByLongitude;
  });
  var pointMatrix = orderedRowsByLatitude.sort(function(a, b) {
    if (flip) return _invariant.getCoords.call(void 0, a[0])[1] - _invariant.getCoords.call(void 0, b[0])[1];
    else return _invariant.getCoords.call(void 0, b[0])[1] - _invariant.getCoords.call(void 0, a[0])[1];
  });
  return pointMatrix;
}

// index.ts
var _marchingsquares = require('marchingsquares');
function isobands(pointGrid, breaks, options) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  const zProperty = options.zProperty || "elevation";
  const commonProperties = options.commonProperties || {};
  const breaksProperties = options.breaksProperties || [];
  _invariant.collectionOf.call(void 0, pointGrid, "Point", "Input must contain Points");
  if (!breaks) throw new Error("breaks is required");
  if (!Array.isArray(breaks)) throw new Error("breaks is not an Array");
  if (!_helpers.isObject.call(void 0, commonProperties))
    throw new Error("commonProperties is not an Object");
  if (!Array.isArray(breaksProperties))
    throw new Error("breaksProperties is not an Array");
  const matrix = gridToMatrix(pointGrid, { zProperty, flip: true });
  let contours = createContourLines(matrix, breaks, zProperty);
  contours = rescaleContours(contours, matrix, pointGrid);
  const multipolygons = contours.map((contour, index) => {
    if (breaksProperties[index] && !_helpers.isObject.call(void 0, breaksProperties[index])) {
      throw new Error("Each mappedProperty is required to be an Object");
    }
    const contourProperties = __spreadValues(__spreadValues({}, commonProperties), breaksProperties[index]);
    contourProperties[zProperty] = contour[zProperty];
    const multiP = _helpers.multiPolygon.call(void 0, 
      contour.groupedRings,
      contourProperties
    );
    return multiP;
  });
  return _helpers.featureCollection.call(void 0, multipolygons);
}
function createContourLines(matrix, breaks, property) {
  const contours = [];
  for (let i = 1; i < breaks.length; i++) {
    const lowerBand = +breaks[i - 1];
    const upperBand = +breaks[i];
    const isobandsCoords = _marchingsquares.isoBands.call(void 0, matrix, lowerBand, upperBand - lowerBand);
    const nestedRings = orderByArea(isobandsCoords);
    const groupedRings = groupNestedRings(nestedRings);
    contours.push({
      groupedRings,
      [property]: lowerBand + "-" + upperBand
    });
  }
  return contours;
}
function rescaleContours(contours, matrix, points) {
  const gridBbox = _bbox.bbox.call(void 0, points);
  const originalWidth = gridBbox[2] - gridBbox[0];
  const originalHeigth = gridBbox[3] - gridBbox[1];
  const x0 = gridBbox[0];
  const y0 = gridBbox[1];
  const matrixWidth = matrix[0].length - 1;
  const matrixHeight = matrix.length - 1;
  const scaleX = originalWidth / matrixWidth;
  const scaleY = originalHeigth / matrixHeight;
  return contours.map(function(contour) {
    contour.groupedRings = contour.groupedRings.map(
      function(lineRingSet) {
        return lineRingSet.map(function(lineRing) {
          return lineRing.map((point) => [
            point[0] * scaleX + x0,
            point[1] * scaleY + y0
          ]);
        });
      }
    );
    return contour;
  });
}
function orderByArea(ringsCoords) {
  const ringsWithArea = ringsCoords.map(function(coords) {
    return { ring: coords, area: _area.area.call(void 0, _helpers.polygon.call(void 0, [coords])) };
  });
  ringsWithArea.sort(function(a, b) {
    return b.area - a.area;
  });
  return ringsWithArea.map(function(x) {
    return x.ring;
  });
}
function groupNestedRings(orderedLinearRings) {
  const lrList = orderedLinearRings.map((lr) => {
    return { lrCoordinates: lr, grouped: false };
  });
  const groupedLinearRingsCoords = [];
  while (!allGrouped(lrList)) {
    for (let i = 0; i < lrList.length; i++) {
      if (!lrList[i].grouped) {
        const group = [];
        group.push(lrList[i].lrCoordinates);
        lrList[i].grouped = true;
        const outerMostPoly = _helpers.polygon.call(void 0, [lrList[i].lrCoordinates]);
        for (let j = i + 1; j < lrList.length; j++) {
          if (!lrList[j].grouped) {
            const lrPoly = _helpers.polygon.call(void 0, [lrList[j].lrCoordinates]);
            if (isInside(lrPoly, outerMostPoly)) {
              group.push(lrList[j].lrCoordinates);
              lrList[j].grouped = true;
            }
          }
        }
        groupedLinearRingsCoords.push(group);
      }
    }
  }
  return groupedLinearRingsCoords;
}
function isInside(testPolygon, targetPolygon) {
  const points = _explode.explode.call(void 0, testPolygon);
  for (let i = 0; i < points.features.length; i++) {
    if (!_booleanpointinpolygon.booleanPointInPolygon.call(void 0, points.features[i], targetPolygon)) {
      return false;
    }
  }
  return true;
}
function allGrouped(list) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].grouped === false) {
      return false;
    }
  }
  return true;
}
var turf_isobands_default = isobands;



exports.default = turf_isobands_default; exports.isobands = isobands;
//# sourceMappingURL=index.cjs.map