﻿<!DOCTYPE html>
<html class="no-js css-menubar" lang="zh-cn">

<head>
    <title>弹窗子页面</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 移动设备 viewport -->
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui">
    <meta name="author" content="火星科技 http://cesium.marsgis.cn ">
    <!-- 360浏览器默认使用Webkit内核 -->
    <meta name="renderer" content="webkit">
    <!-- Chrome浏览器添加桌面快捷方式（安卓） -->
    <link rel="icon" type="image/png" href="../../img/favicon/favicon.png">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Safari浏览器添加到主屏幕（IOS） -->
    <link rel="icon" sizes="192x192" href="img/favicon/apple-touch-icon.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="火星科技MarsGIS">
    <!-- Win8标题栏及ICON图标 -->
    <link rel="apple-touch-icon-precomposed" href="../../img/favicon/apple-touch-icon.png">
    <meta name="msapplication-TileImage" content="../../img/favicon/<EMAIL>">
    <meta name="msapplication-TileColor" content="#62a8ea">

    <!-- 第3方lib引入 -->
    <script type="text/javascript" src="../../lib/include-lib.js?time=20200102" libpath="../../lib/"
        include="jquery,font-awesome,bootstrap,admui-frame"></script>
    <link href="../../css/widget-win.css" rel="stylesheet" />
    <link href="view.css?time=20200102" rel="stylesheet" />
</head>

<body>
    <!-- 面板 -->
    <div class="infoview">
        <table id="paramView" class="talbe_style zbxd" style="display: block">
            <tr>
                <td class="nametd">起点：</td>
                <td>
                    <input id="inputStartPoint" value="" type="text" class="form-control serachContent mcxd_start_text"
                        autocomplete="off" />
                    <ul class="searchval" id="searchval_start">
                    </ul>
                </td>
                <td>
                    <button type="button" id="drawStartPoint" class="btn btn-primary">图上选点</button>
                </td>
            </tr>
            <tr>
                <td class="nametd">终点：</td>
                <td>
                    <input id="inputEndPoint" value="" type="text" class="form-control serachContent mcxd_end_text"
                        autocomplete="off" />
                    <ul class="searchval searchval_end" id="searchval_end">

                    </ul>
                </td>
                <td>
                    <button type="button" id="drawEndPoint" class="btn btn-primary">图上选点</button>
                </td>
            </tr>
        </table>
        <div class="line"></div>
        <div class="route_box" id="routeBox" style="display: none;">
            <table class="router_table" border="1" bordercolor="#ffffff" cellspacing="0" cellpadding="0">
                <thead class="title">
                    <td width="40">序号</td>
                    <td>推荐线路</td>
                </thead>
                <tbody class="routerContent" id="routerContent">
                </tbody>
            </table>

            <div style="padding-top: 10px;">
                <table class="talbe_style" style="margin: auto;">
                    <tr>
                        <td>
                            <span>速度：</span>
                            <input style="width: 60%" id="changeSpeed" value="120" type="number" class="form-control "
                                autocomplete="off" />（km/h）
                        </td>
                    </tr>
                </table>

            </div>
        </div>
    </div>
    <!--页面js-->
    <script src="view.js?time=20200102"></script>
</body>

</html>