// index.js
import { length } from "@turf/length";
import { lineSliceAlong } from "@turf/line-slice-along";
import { flattenEach } from "@turf/meta";
import { featureCollection, isObject } from "@turf/helpers";
function lineChunk(geojson, segmentLength, options) {
  options = options || {};
  if (!isObject(options)) throw new Error("options is invalid");
  var units = options.units;
  var reverse = options.reverse;
  if (!geojson) throw new Error("geojson is required");
  if (segmentLength <= 0)
    throw new Error("segmentLength must be greater than 0");
  var results = [];
  flattenEach(geojson, function(feature) {
    if (reverse)
      feature.geometry.coordinates = feature.geometry.coordinates.reverse();
    sliceLineSegments(feature, segmentLength, units, function(segment) {
      results.push(segment);
    });
  });
  return featureCollection(results);
}
function sliceLineSegments(line, segmentLength, units, callback) {
  var lineLength = length(line, { units });
  if (lineLength <= segmentLength) return callback(line);
  var numberOfSegments = lineLength / segmentLength;
  if (!Number.isInteger(numberOfSegments)) {
    numberOfSegments = Math.floor(numberOfSegments) + 1;
  }
  for (var i = 0; i < numberOfSegments; i++) {
    var outline = lineSliceAlong(
      line,
      segmentLength * i,
      segmentLength * (i + 1),
      { units }
    );
    callback(outline, i);
  }
}
var turf_line_chunk_default = lineChunk;
export {
  turf_line_chunk_default as default,
  lineChunk
};
//# sourceMappingURL=index.js.map