{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import type { Feature, Polygon } from \"geojson\";\nimport type { AllGeoJSON } from \"@turf/helpers\";\nimport { bbox } from \"@turf/bbox\";\nimport { bboxPolygon } from \"@turf/bbox-polygon\";\n\n/**\n * Takes any number of features and returns a rectangular {@link Polygon} that encompasses all vertices.\n *\n * @function\n * @param {GeoJSON} geojson input features\n * @returns {Feature<Polygon>} a rectangular Polygon feature that encompasses all vertices\n * @example\n * var features = turf.featureCollection([\n *   turf.point([-75.343, 39.984], {\"name\": \"Location A\"}),\n *   turf.point([-75.833, 39.284], {\"name\": \"Location B\"}),\n *   turf.point([-75.534, 39.123], {\"name\": \"Location C\"})\n * ]);\n *\n * var enveloped = turf.envelope(features);\n *\n * //addToMap\n * var addToMap = [features, enveloped];\n */\nfunction envelope(geojson: AllGeoJSON): Feature<Polygon> {\n  return bboxPolygon(bbox(geojson));\n}\n\nexport { envelope };\nexport default envelope;\n"], "mappings": ";AAEA,SAAS,YAAY;AACrB,SAAS,mBAAmB;AAoB5B,SAAS,SAAS,SAAuC;AACvD,SAAO,YAAY,KAAK,OAAO,CAAC;AAClC;AAGA,IAAO,wBAAQ;", "names": []}