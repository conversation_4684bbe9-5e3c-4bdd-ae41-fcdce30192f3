# @turf/line-chunk

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineChunk

Divides a [LineString][1] into chunks of a specified length.
If the line is shorter than the segment length then the original line is returned.

### Parameters

*   `geojson` **([FeatureCollection][2] | [Geometry][3] | [Feature][4]<([LineString][1] | [MultiLineString][5])>)** the lines to split
*   `segmentLength` **[number][6]** how long to make each segment
*   `options` **[Object][7]** Optional parameters (optional, default `{}`)

    *   `options.units` **[string][8]** units can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)
    *   `options.reverse` **[boolean][9]** reverses coordinates to start the first chunked segment at the end (optional, default `false`)

### Examples

```javascript
var line = turf.lineString([[-95, 40], [-93, 45], [-85, 50]]);

var chunk = turf.lineChunk(line, 15, {units: 'miles'});

//addToMap
var addToMap = [chunk];
```

Returns **[FeatureCollection][2]<[LineString][1]>** collection of line segments

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3.1

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[9]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/line-chunk
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
