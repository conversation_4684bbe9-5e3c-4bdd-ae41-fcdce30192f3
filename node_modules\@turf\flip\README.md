# @turf/flip

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## flip

Takes input features and flips all of their coordinates from `[x, y]` to `[y, x]`.

### Parameters

*   `geojson` **[GeoJSON][1]** input features
*   `options` **[Object][2]** Optional parameters (optional, default `{}`)

    *   `options.mutate` **[boolean][3]** allows GeoJSON input to be mutated (significant performance increase if true) (optional, default `false`)

### Examples

```javascript
var serbia = turf.point([20.566406, 43.421008]);

var saudiArabia = turf.flip(serbia);

//addToMap
var addToMap = [serbia, saudiArabia];
```

Returns **[GeoJSON][1]** a feature or set of features of the same type as `input` with flipped coordinates

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/flip
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
