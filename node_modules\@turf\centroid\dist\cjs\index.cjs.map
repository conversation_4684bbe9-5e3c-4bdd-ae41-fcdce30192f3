{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-centroid/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wCAAkC;AAClC,kCAA0B;AAkB1B,SAAS,QAAA,CACP,OAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACc;AACnB,EAAA,IAAI,KAAA,EAAO,CAAA;AACX,EAAA,IAAI,KAAA,EAAO,CAAA;AACX,EAAA,IAAI,IAAA,EAAM,CAAA;AACV,EAAA,6BAAA;AAAA,IACE,OAAA;AAAA,IACA,QAAA,CAAU,KAAA,EAAO;AACf,MAAA,KAAA,GAAQ,KAAA,CAAM,CAAC,CAAA;AACf,MAAA,KAAA,GAAQ,KAAA,CAAM,CAAC,CAAA;AACf,MAAA,GAAA,EAAA;AAAA,IACF,CAAA;AAAA,IACA;AAAA,EACF,CAAA;AACA,EAAA,OAAO,4BAAA,CAAO,KAAA,EAAO,GAAA,EAAK,KAAA,EAAO,GAAG,CAAA,EAAG,OAAA,CAAQ,UAAU,CAAA;AAC3D;AAGA,IAAO,sBAAA,EAAQ,QAAA;ADvBf;AACE;AACA;AACF,qEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-centroid/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, GeoJsonProperties, Point } from \"geojson\";\nimport { point, AllGeoJSON } from \"@turf/helpers\";\nimport { coordEach } from \"@turf/meta\";\n\n/**\n * Computes the centroid as the mean of all vertices within the object.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional Parameters\n * @param {Object} [options.properties={}] an Object that is used as the {@link Feature}'s properties\n * @returns {Feature<Point>} the centroid of the input object\n * @example\n * var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);\n *\n * var centroid = turf.centroid(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, centroid]\n */\nfunction centroid<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: AllGeoJSON,\n  options: {\n    properties?: P;\n  } = {}\n): Feature<Point, P> {\n  let xSum = 0;\n  let ySum = 0;\n  let len = 0;\n  coordEach(\n    geojson,\n    function (coord) {\n      xSum += coord[0];\n      ySum += coord[1];\n      len++;\n    },\n    true\n  );\n  return point([xSum / len, ySum / len], options.properties);\n}\n\nexport { centroid };\nexport default centroid;\n"]}