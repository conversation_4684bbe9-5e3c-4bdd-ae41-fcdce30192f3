/* 2017-7-20 21:21:23 | 修改 木遥（QQ：346819890） */
 
/*init*/
html, body, div, ol, ul, li, dl, dt, dd, h1, h2, h3, h4, h5, h6, input, button, textarea, p, span, table, th, td, from {
	margin:0;
	padding:0
}
body, input, button, select, textarea { 
	font:14px/22px "Microsoft Yahei", "Helvetica Neue";
	-webkit-font-smoothing:antialiased;
	-moz-osx-font-smoothing:grayscale;
	-moz-font-feature-settings:"liga", "kern"
}
/*scrollbar*/
body, html {
	scrollbar-base-color:#f4f7fc;
	scrollbar-track-color:#f4f7fc;
	scrollbar-face-color:#797979;
	scrollbar-arrow-color:#f4f7fc;
	scrollbar-shadow-color:#f4f7fc;
	scrollbar-3dlight-color:#f4f7fc;
	scrollbar-highlight-color:#f4f7fc;
	scrollbar-darkshadow-color:#f4f7fc;
}
::-webkit-scrollbar-button {
	height: 0;
	width: 0;
	display: none
}
::-webkit-scrollbar-track {
	background: transparent
}
::-webkit-scrollbar-track, ::-webkit-scrollbar-thumb {
	border: 0
}
::-webkit-scrollbar {
	height: 6px;
	width: 6px;
	background: transparent;
	border-radius: 4px
}
::-webkit-scrollbar-thumb {
	padding-top: 100px;
	-webkit-box-shadow: inset 1px 1px 0 rgba(0, 0, 0, .1), inset -1px -1px 0 rgba(0, 0, 0, .07);
	background-color: #797979;
	min-height: 28px;
	border-radius: 4px;
	background-clip:
	padding-box
}
::-webkit-scrollbar-track, ::-webkit-scrollbar-thumb {
	border: 0
}
::-webkit-scrollbar-thumb:hover {
	-webkit-box-shadow:inset 1px 1px 1px rgba(0, 0, 0, .25);
	background-color:rgba(0, 0, 0, .4)
}
::-webkit-scrollbar-thumb:active {
	-webkit-box-shadow:inset 1px 1px 3px rgba(0, 0, 0, .35);
	background-color:rgba(0, 0, 0, .5)
}
table {
	border-collapse:collapse;
	border-spacing:0
}
img, a img {
	border:0
}
a {
	color:inherit;
	outline:medium none;
	text-decoration:none;
	outline:none;
	cursor:pointer;
	transition:all 0.5s ease
}
a:hover {
	text-decoration:none
}
ol, ul, li, dl, dt, dd {
	list-style:none
}
em, cite, i {
	font-style:normal
}
p {
	word-break:break-all;
	text-align:justify;
	text-justify:distribute-all-lines;
	text-justify:inter-ideograph;
	word-break:normal
}
input:focus, textarea:focus {
	outline:none
}
input::-ms-clear {
	display:none
}
textarea {
	resize:none
}
.clear:after {
	content:" ";
	display:block;
	height:0;
	visibility:hidden
}
.clear {
	clear:both;
	zoom:1
}
.fl {
	float:left !important
}
.fr {
	float:right !important
}
.show {
	display:block !important
}
.hide {
	display:none !important
}
.over {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap
}
.pos-re {
	position:relative
}
.pos-ab {
	position:absolute
}
.m10 {
	margin: 10px
}
.ml10 {
	margin-left: 10px
}
.center {
	text-align: center !important
} 

 
/*mp_box*/
.mp_box {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.mp_icon_close {
	height: 40px;
	line-height: 38px;
	float: right;
	text-align: center;
	cursor: pointer;
	text-align: center;
	text-indent: 0;
	width: 30px;
}

.mp_head {
	line-height: 35px;
	border-bottom: #dedede 1px solid;
	overflow: hidden;
}
.mp_head ul li {
	float:left;
	display: inline-block;
	cursor:pointer;
}
.mp_head ul li i {
    width: 28px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    background: #f5f5f5;
    border: #dedede 1px solid;
    color: #3374e0;
    display: inline-block;
    font-size: 16px;
    margin-left: 10px;
}



.mp_head .active i, .mp_head ul li i:hover {
	background: #3374e0;
	color: #fff;
	border-color: #3374e0;
}

/*tab*/
.mp_tab_card {
	position: relative;
	overflow: hidden;
}
.mp_tab_card .mp_tab_tit {
	position: absolute;
	bottom: 0;
	height: 32px;
	width: 100%;
	white-space: nowrap;
	border-top: 1px solid #ddd;
	background: #f2f2f2;
	padding-left: 10px;
	box-sizing: border-box;
}
.mp_tab_card .mp_tab_tit li {
	display: inline-block;
	float: left;
	line-height: 25px;
	min-width: 45px;
	padding: 0 10px;
	cursor: pointer;
	text-align: center;
	box-sizing: border-box;
	color: #333;
}
.mp_tab_card .mp_tab_tit .cur {
	color: #3374e0;
	border: 1px solid #ddd;
	border-top: none;
	height: 28px;
	line-height: 26px;
	background: #fff;
	margin-top: -1px;
	border-radius: 0 0 6px 6px;
}
.mp_tab_card .mp_tab_tit .disabled {
	display:none;
}
.mp_tab_con>li, .mp_tab_con>div {
	display: none
}
.mp_tab_con>.cur {
	display: block
}
/*select*/
.mp_select {
	height:28px;
	line-height:22px;
	padding: 0 5px;
	box-sizing: border-box;
	font-size: 12px;
	border:1px solid #e6e6e6;
	background-color:#fff;
	border-radius:3px;
	outline:0;
	box-sizing:border-box;
	position:relative;
}
.mp_select:after {
	content: '';
	width: 0;
	height: 0;
	overflow: hidden;
	cursor: pointer;
	transition: all .2s;
	position: absolute;
	border-width: 6px;
	display: inline-block;
	top: 10px;
	right: 5px;
	border-style: solid dashed dashed;
	border-color: #c2c2c2 transparent transparent;
}
.mp_selected:after {
	top: 4px;
	border-style: dashed dashed solid;
	border-color: transparent transparent #c2c2c2;
}
.mp_select .mp_select_text {
	padding:0 20px 0 0;
	height:28px;
	line-height:26px;
	overflow: hidden
}
.mp_select_ul {
	background: #fff;
	border: #d6d6d6 1px solid;
	border-radius: 3px;
	padding: 5px 0;
	box-shadow: 1px 2px 1px rgba(0,0,0,.15);
	position: absolute;
	left: 0;
	z-index: 1000;
	color: #333;
	text-align: center;
	width: 100%;
	display: none
}
.mp_select_ul li {
	margin: 0 5px;
	border-radius: 3px;
	text-align: left;
	height: 28px;
	line-height: 28px;
	text-indent: 5px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap
}
.mp_select_ul li:hover {
	background: #ecf1f8
}
.mp_select_ul .selecton, .mp_select_ul .selecton:hover {
	background: #3374e0!important;
	color: #fff!important
}
/*mp_mark*/
.mp_mark {
    overflow-x:hidden;
    overflow-y:auto;
}
.mp_mark ul li {
	float: left;
	display: inline-block;
	width: 80px;
	text-align: center;
	padding-bottom: 10px;
}
.mp_mark ul li i {
	width: 55px;
	height: 55px;
	line-height: 50px;
	text-align: center;
	display: inline-block;
	margin: 0 auto;
	background: #def1f0;
	border: #dedede 1px solid;
	font-size: 26px;
	color: #333;
	cursor: pointer;
	padding: 0;
}
.mp_mark .markon i, .mp_mark ul li i:hover {
	background: #3374e0;
	border: #3374e0 1px solid;
	color: #fff;
}
.mp_mark ul li i img{
	margin: -2px 0 0 0;
    width: 100%;
    height: 100%;
}
.mp_icon_txt {
	font-weight: bold;
}


/*mp_tree*/
.mp_tree {
	overflow: auto;
	overflow-x: hidden;
}
.open {
	height: 30px;
	line-height: 28px;
	background: #def1f0;
	cursor: pointer;
}
.bdt{
	border-top: #ddd 1px solid
}
.tree_icon {
	width: 12px;
    height: 12px;
	text-align: center;
	line-height: 8px;
	border: #333 1px solid;
	border-radius: 2px;
	margin: 0 5px;
	color: #333;
	display: inline-block;
}
.mp_attr .nametd {
	width:40%;
}

.mp_attr table {
	width: 100%;
	border: 0;
	font-size: 12px;
}
.mp_attr table tr td {
	padding: 5px 5px 5px 10px;
	text-align: left;
	border: #cde1de 1px solid;
	max-width: 150px;
    word-wrap: break-word;
}
.mp_attr table tr td:first-child {
	border-left:none;
}
.mp_input {
	height:28px;
	line-height:22px;
	padding: 3px 5px;
	box-sizing: border-box;
	font-size: 12px;
	border:1px solid #e6e6e6;
	background-color:#fff;
	border-radius:3px;
	display:block;
	width:100%;
	outline:0;
	box-sizing:border-box
}
.mp_input:hover {
	border-color:#c9c9c9!important
}
.mp_input:focus {
	border-color:#3374e0!important
}
.ptb5 {
	padding-top: 5px;
	padding-bottom: 5px;
}
/*progress*/
.puiprogress{position: relative; margin: 10px 10px 0 0;}
.puiprogress_bg{height: 10px;border-radius: 5px; overflow: hidden;background:#E4E4E4;}
.puiprogress_bar{background: #537cc0; width: 0; height: 10px; border-radius: 5px;}
.puiprogress_btn{width: 20px; height: 20px; border-radius: 50%; position: absolute;background:#ccc; left: 0px; margin-left: -10px; top:-5px; cursor: pointer;box-sizing:border-box;}
.puiprogress_text{text-align: center;}
/*btn*/
.mp_btn {
	display: inline-block;
	height: 30px;
	line-height: 28px;
	padding: 0 20px;
	background: #3374e0;
	border: 1px solid #3374e0;
	color: #fff;
	white-space: nowrap;
	text-align: center;
	font-size: 14px;
	border-radius: 3px;
	cursor: pointer;
	outline: 0;
	box-sizing: border-box;
}
.mp_btn:hover {
	background: #2e68c7
}
.mp_btn_yellow {
	border: 1px solid #ffab40;
	background: #ffab40;
	color: #fff;
}
.mp_btn_yellow:hover {
	background: #e69b39
}
/*local*/
.local {
	padding: 0 10px 10px 10px;
}
.local .open {
	background: none;
}
.local .mp_attr table tr td:first-child {
	border-left: #cde1de 1px solid;
	/*width: 40px;*/
}


/*dark*/
.dark .mp_head ul li i{ 
    background-color: rgba(63, 72, 84, 0.6);
    border: #dedede 1px solid;
    color: #ffffff;
}
.dark .mp_select {
    background-color: rgba(63, 72, 84, 0.6);
}
.dark .mp_select_ul {
    color: #ffffff;
    background-color: rgba(63, 72, 84, 0.8);
} 
.dark .mp_select_ul li:hover {
    background-color: rgba(63, 72, 84, 1);
}
.dark .mp_select_ul .selecton, .mp_select_ul .selecton:hover {
	background:  rgba(63, 72, 84, 1)!important;
	color: #fff!important
}

.dark .open {
    background: #1c3b3a;
}

.dark .tree_icon {  
    color: #ffffff; 
}
.dark .mp_input {
    background-color: rgba(63, 72, 84, 0.6);
}
.dark .mp_mark ul li i {
    background-color: rgba(63, 72, 84, 0.6);
}
.dark .mp_tab_card .mp_tab_tit {
    background-color: rgba(63, 72, 84, 0.6);
}

.dark .mp_tab_card .mp_tab_tit li {
    color: #808080;
}

.dark .mp_tab_card .mp_tab_tit .cur {
    color: #ffffff;
    background-color: rgba(63, 72, 84, 0.6);
}
