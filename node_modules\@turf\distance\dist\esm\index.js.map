{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { getCoord } from \"@turf/invariant\";\nimport { radiansToLength, degreesToRadians, Coord, Units } from \"@turf/helpers\";\n\n//http://en.wikipedia.org/wiki/Haversine_formula\n//http://www.movable-type.co.uk/scripts/latlong.html\n\n/**\n * Calculates the distance between two {@link Coord|coordinates} in degrees, radians, miles, or kilometers.\n * This uses the [Haversine formula](http://en.wikipedia.org/wiki/Haversine_formula) to account for global curvature.\n *\n * @function\n * @param {Coord} from origin coordinate\n * @param {Coord} to destination coordinate\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] can be degrees, radians, miles, or kilometers\n * @returns {number} distance between the two coordinates\n * @example\n * var from = turf.point([-75.343, 39.984]);\n * var to = turf.point([-75.534, 39.123]);\n * var options = {units: 'miles'};\n *\n * var distance = turf.distance(from, to, options);\n *\n * //addToMap\n * var addToMap = [from, to];\n * from.properties.distance = distance;\n * to.properties.distance = distance;\n */\nfunction distance(\n  from: Coord,\n  to: Coord,\n  options: {\n    units?: Units;\n  } = {}\n) {\n  var coordinates1 = getCoord(from);\n  var coordinates2 = getCoord(to);\n  var dLat = degreesToRadians(coordinates2[1] - coordinates1[1]);\n  var dLon = degreesToRadians(coordinates2[0] - coordinates1[0]);\n  var lat1 = degreesToRadians(coordinates1[1]);\n  var lat2 = degreesToRadians(coordinates2[1]);\n\n  var a =\n    Math.pow(Math.sin(dLat / 2), 2) +\n    Math.pow(Math.sin(dLon / 2), 2) * Math.cos(lat1) * Math.cos(lat2);\n\n  return radiansToLength(\n    2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)),\n    options.units\n  );\n}\n\nexport { distance };\nexport default distance;\n"], "mappings": ";AAAA,SAAS,gBAAgB;AACzB,SAAS,iBAAiB,wBAAsC;AA2BhE,SAAS,SACP,MACA,IACA,UAEI,CAAC,GACL;AACA,MAAI,eAAe,SAAS,IAAI;AAChC,MAAI,eAAe,SAAS,EAAE;AAC9B,MAAI,OAAO,iBAAiB,aAAa,CAAC,IAAI,aAAa,CAAC,CAAC;AAC7D,MAAI,OAAO,iBAAiB,aAAa,CAAC,IAAI,aAAa,CAAC,CAAC;AAC7D,MAAI,OAAO,iBAAiB,aAAa,CAAC,CAAC;AAC3C,MAAI,OAAO,iBAAiB,aAAa,CAAC,CAAC;AAE3C,MAAI,IACF,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,IAC9B,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAElE,SAAO;AAAA,IACL,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,IAC7C,QAAQ;AAAA,EACV;AACF;AAGA,IAAO,wBAAQ;", "names": []}