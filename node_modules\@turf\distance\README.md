# @turf/distance

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## distance

Calculates the distance between two [coordinates][1] in degrees, radians, miles, or kilometers.
This uses the [Haversine formula][2] to account for global curvature.

### Parameters

*   `from` **[Coord][1]** origin coordinate
*   `to` **[Coord][1]** destination coordinate
*   `options` **[Object][3]** Optional parameters (optional, default `{}`)

    *   `options.units` **[string][4]** can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)

### Examples

```javascript
var from = turf.point([-75.343, 39.984]);
var to = turf.point([-75.534, 39.123]);
var options = {units: 'miles'};

var distance = turf.distance(from, to, options);

//addToMap
var addToMap = [from, to];
from.properties.distance = distance;
to.properties.distance = distance;
```

Returns **[number][5]** distance between the two coordinates

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[2]: http://en.wikipedia.org/wiki/Haversine_formula

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/distance
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
