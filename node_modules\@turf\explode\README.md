# @turf/explode

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## explode

Takes a feature or set of features and returns all positions as [points][1].

### Parameters

*   `geojson` **[GeoJSON][2]** input features

### Examples

```javascript
var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);

var explode = turf.explode(polygon);

//addToMap
var addToMap = [polygon, explode]
```

*   Throws **[Error][3]** if it encounters an unknown geometry type

Returns **[FeatureCollection][4]\<point>** points representing the exploded input features

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error

[4]: https://tools.ietf.org/html/rfc7946#section-3.3

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/explode
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
