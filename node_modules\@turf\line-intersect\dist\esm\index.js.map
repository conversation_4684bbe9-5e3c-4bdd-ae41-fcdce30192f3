{"version": 3, "sources": ["../../index.ts", "../../lib/sweepline-intersections-export.ts"], "sourcesContent": ["import { feature, featureCollection, point } from \"@turf/helpers\";\nimport {\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport type { Intersection } from \"sweepline-intersections\";\nimport { sweeplineIntersections as findIntersections } from \"./lib/sweepline-intersections-export.js\";\n\n/**\n * Takes any LineString or Polygon GeoJSON and returns the intersecting point(s).\n *\n * @function\n * @param {GeoJSON} line1 any LineString or Polygon\n * @param {GeoJSON} line2 any LineString or Polygon\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.removeDuplicates=true] remove duplicate intersections\n * @param {boolean} [options.ignoreSelfIntersections=true] ignores self-intersections on input features\n * @returns {FeatureCollection<Point>} point(s) that intersect both\n * @example\n * var line1 = turf.lineString([[126, -11], [129, -21]]);\n * var line2 = turf.lineString([[123, -18], [131, -14]]);\n * var intersects = turf.lineIntersect(line1, line2);\n *\n * //addToMap\n * var addToMap = [line1, line2, intersects]\n */\nfunction lineIntersect<\n  G1 extends LineString | MultiLineString | Polygon | MultiPolygon,\n  G2 extends LineString | MultiLineString | Polygon | MultiPolygon,\n>(\n  line1: FeatureCollection<G1> | Feature<G1> | G1,\n  line2: FeatureCollection<G2> | Feature<G2> | G2,\n  options: {\n    removeDuplicates?: boolean;\n    ignoreSelfIntersections?: boolean;\n  } = {}\n): FeatureCollection<Point> {\n  const { removeDuplicates = true, ignoreSelfIntersections = true } = options;\n  let features: Feature<G1 | G2>[] = [];\n  if (line1.type === \"FeatureCollection\")\n    features = features.concat(line1.features);\n  else if (line1.type === \"Feature\") features.push(line1);\n  else if (\n    line1.type === \"LineString\" ||\n    line1.type === \"Polygon\" ||\n    line1.type === \"MultiLineString\" ||\n    line1.type === \"MultiPolygon\"\n  ) {\n    features.push(feature(line1));\n  }\n\n  if (line2.type === \"FeatureCollection\")\n    features = features.concat(line2.features);\n  else if (line2.type === \"Feature\") features.push(line2);\n  else if (\n    line2.type === \"LineString\" ||\n    line2.type === \"Polygon\" ||\n    line2.type === \"MultiLineString\" ||\n    line2.type === \"MultiPolygon\"\n  ) {\n    features.push(feature(line2));\n  }\n\n  const intersections = findIntersections(\n    featureCollection(features),\n    ignoreSelfIntersections\n  );\n\n  let results: Intersection[] = [];\n  if (removeDuplicates) {\n    const unique: Record<string, boolean> = {};\n    intersections.forEach((intersection) => {\n      const key = intersection.join(\",\");\n      if (!unique[key]) {\n        unique[key] = true;\n        results.push(intersection);\n      }\n    });\n  } else {\n    results = intersections;\n  }\n  return featureCollection(results.map((r) => point(r)));\n}\n\nexport { lineIntersect };\nexport default lineIntersect;\n", "// Get around problems with moduleResolution node16 and some older libraries.\n// Manifests as \"This expression is not callable ... has no call signatures\"\n// https://stackoverflow.com/a/74709714\n\nimport lib from \"sweepline-intersections\";\n\nexport const sweeplineIntersections = lib as unknown as typeof lib.default;\n"], "mappings": ";AAAA,SAAS,SAAS,mBAAmB,aAAa;;;ACIlD,OAAO,SAAS;AAET,IAAM,yBAAyB;;;ADyBtC,SAAS,cAIP,OACA,OACA,UAGI,CAAC,GACqB;AAC1B,QAAM,EAAE,mBAAmB,MAAM,0BAA0B,KAAK,IAAI;AACpE,MAAI,WAA+B,CAAC;AACpC,MAAI,MAAM,SAAS;AACjB,eAAW,SAAS,OAAO,MAAM,QAAQ;AAAA,WAClC,MAAM,SAAS,UAAW,UAAS,KAAK,KAAK;AAAA,WAEpD,MAAM,SAAS,gBACf,MAAM,SAAS,aACf,MAAM,SAAS,qBACf,MAAM,SAAS,gBACf;AACA,aAAS,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC9B;AAEA,MAAI,MAAM,SAAS;AACjB,eAAW,SAAS,OAAO,MAAM,QAAQ;AAAA,WAClC,MAAM,SAAS,UAAW,UAAS,KAAK,KAAK;AAAA,WAEpD,MAAM,SAAS,gBACf,MAAM,SAAS,aACf,MAAM,SAAS,qBACf,MAAM,SAAS,gBACf;AACA,aAAS,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC9B;AAEA,QAAM,gBAAgB;AAAA,IACpB,kBAAkB,QAAQ;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI,UAA0B,CAAC;AAC/B,MAAI,kBAAkB;AACpB,UAAM,SAAkC,CAAC;AACzC,kBAAc,QAAQ,CAAC,iBAAiB;AACtC,YAAM,MAAM,aAAa,KAAK,GAAG;AACjC,UAAI,CAAC,OAAO,GAAG,GAAG;AAChB,eAAO,GAAG,IAAI;AACd,gBAAQ,KAAK,YAAY;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,cAAU;AAAA,EACZ;AACA,SAAO,kBAAkB,QAAQ,IAAI,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC;AACvD;AAGA,IAAO,8BAAQ;", "names": []}