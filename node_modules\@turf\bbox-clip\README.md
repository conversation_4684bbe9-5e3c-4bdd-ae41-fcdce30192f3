# @turf/bbox-clip

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## bboxClip

Takes a [Feature][1] and a bbox and clips the feature to the bbox using
[lineclip][2].
May result in degenerate edges when clipping Polygons.

### Parameters

*   `feature` **[Feature][1]<([LineString][3] | [MultiLineString][4] | [Polygon][5] | [MultiPolygon][6])>** feature to clip to the bbox
*   `bbox` **[BBox][7]** extent in \[minX, minY, maxX, maxY] order

### Examples

```javascript
var bbox = [0, 0, 10, 10];
var poly = turf.polygon([[[2, 2], [8, 4], [12, 8], [3, 7], [2, 2]]]);

var clipped = turf.bboxClip(poly, bbox);

//addToMap
var addToMap = [bbox, poly, clipped]
```

Returns **[Feature][1]<([LineString][3] | [MultiLineString][4] | [Polygon][5] | [MultiPolygon][6])>** clipped Feature

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://github.com/mapbox/lineclip

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[7]: https://tools.ietf.org/html/rfc7946#section-5

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/bbox-clip
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
