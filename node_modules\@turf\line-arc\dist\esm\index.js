// index.ts
import { circle } from "@turf/circle";
import { destination } from "@turf/destination";
import { lineString } from "@turf/helpers";
function lineArc(center, radius, bearing1, bearing2, options = {}) {
  const steps = options.steps || 64;
  const angle1 = convertAngleTo360(bearing1);
  const angle2 = convertAngleTo360(bearing2);
  const properties = !Array.isArray(center) && center.type === "Feature" ? center.properties : {};
  if (angle1 === angle2) {
    return lineString(
      circle(center, radius, options).geometry.coordinates[0],
      properties
    );
  }
  const arcStartDegree = angle1;
  const arcEndDegree = angle1 < angle2 ? angle2 : angle2 + 360;
  let alpha = arcStartDegree;
  const coordinates = [];
  let i = 0;
  const arcStep = (arcEndDegree - arcStartDegree) / steps;
  while (alpha <= arcEndDegree) {
    coordinates.push(
      destination(center, radius, alpha, options).geometry.coordinates
    );
    i++;
    alpha = arcStartDegree + i * arcStep;
  }
  return lineString(coordinates, properties);
}
function convertAngleTo360(alpha) {
  let beta = alpha % 360;
  if (beta < 0) {
    beta += 360;
  }
  return beta;
}
var turf_line_arc_default = lineArc;
export {
  turf_line_arc_default as default,
  lineArc
};
//# sourceMappingURL=index.js.map