{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, Geometry, Position } from \"geojson\";\nimport { getGeom } from \"@turf/invariant\";\nimport { polygon, lineString } from \"@turf/helpers\";\nimport { booleanDisjoint } from \"@turf/boolean-disjoint\";\nimport { booleanCrosses } from \"@turf/boolean-crosses\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { booleanPointOnLine as isPointOnLine } from \"@turf/boolean-point-on-line\";\n\n/**\n * booleanValid checks if the geometry is a valid according to the OGC Simple Feature Specification.\n *\n * @function\n * @param {Geometry|Feature<any>} feature GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n *\n * turf.booleanValid(line); // => true\n * turf.booleanValid({foo: \"bar\"}); // => false\n */\nfunction booleanValid(feature: Feature<any> | Geometry) {\n  // Automatic False\n  if (!feature.type) return false;\n\n  // Parse GeoJSON\n  const geom = getGeom(feature);\n  const type = geom.type;\n  const coords = geom.coordinates;\n\n  switch (type) {\n    case \"Point\":\n      return coords.length > 1;\n    case \"MultiPoint\":\n      for (var i = 0; i < coords.length; i++) {\n        if (coords[i].length < 2) return false;\n      }\n      return true;\n    case \"LineString\":\n      if (coords.length < 2) return false;\n      for (var i = 0; i < coords.length; i++) {\n        if (coords[i].length < 2) return false;\n      }\n      return true;\n    case \"MultiLineString\":\n      if (coords.length < 2) return false;\n      for (var i = 0; i < coords.length; i++) {\n        if (coords[i].length < 2) return false;\n      }\n      return true;\n    case \"Polygon\":\n      for (var i = 0; i < geom.coordinates.length; i++) {\n        if (coords[i].length < 4) return false;\n        if (!checkRingsClose(coords[i])) return false;\n        if (checkRingsForSpikesPunctures(coords[i])) return false;\n        if (i > 0) {\n          if (\n            lineIntersect(polygon([coords[0]]), polygon([coords[i]])).features\n              .length > 1\n          )\n            return false;\n        }\n      }\n      return true;\n    case \"MultiPolygon\":\n      for (var i = 0; i < geom.coordinates.length; i++) {\n        var poly: any = geom.coordinates[i];\n\n        for (var ii = 0; ii < poly.length; ii++) {\n          if (poly[ii].length < 4) return false;\n          if (!checkRingsClose(poly[ii])) return false;\n          if (checkRingsForSpikesPunctures(poly[ii])) return false;\n          if (ii === 0) {\n            if (!checkPolygonAgainstOthers(poly, geom.coordinates, i))\n              return false;\n          }\n          if (ii > 0) {\n            if (\n              lineIntersect(polygon([poly[0]]), polygon([poly[ii]])).features\n                .length > 1\n            )\n              return false;\n          }\n        }\n      }\n      return true;\n    default:\n      return false;\n  }\n}\n\nfunction checkRingsClose(geom: Position[]) {\n  return (\n    geom[0][0] === geom[geom.length - 1][0] &&\n    geom[0][1] === geom[geom.length - 1][1]\n  );\n}\n\nfunction checkRingsForSpikesPunctures(geom: Position[]) {\n  for (var i = 0; i < geom.length - 1; i++) {\n    var point = geom[i];\n    for (var ii = i + 1; ii < geom.length - 2; ii++) {\n      var seg = [geom[ii], geom[ii + 1]];\n      if (isPointOnLine(point, lineString(seg))) return true;\n    }\n  }\n  return false;\n}\n\nfunction checkPolygonAgainstOthers(\n  poly: Position[][],\n  geom: Position[][][],\n  index: number\n) {\n  var polyToCheck = polygon(poly);\n  for (var i = index + 1; i < geom.length; i++) {\n    if (!booleanDisjoint(polyToCheck, polygon(geom[i]))) {\n      if (booleanCrosses(polyToCheck, lineString(geom[i][0]))) return false;\n    }\n  }\n  return true;\n}\n\nexport { booleanValid };\nexport default booleanValid;\n"], "mappings": ";AACA,SAAS,eAAe;AACxB,SAAS,SAAS,kBAAkB;AACpC,SAAS,uBAAuB;AAChC,SAAS,sBAAsB;AAC/B,SAAS,qBAAqB;AAC9B,SAAS,sBAAsB,qBAAqB;AAcpD,SAAS,aAAa,SAAkC;AAEtD,MAAI,CAAC,QAAQ,KAAM,QAAO;AAG1B,QAAM,OAAO,QAAQ,OAAO;AAC5B,QAAM,OAAO,KAAK;AAClB,QAAM,SAAS,KAAK;AAEpB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,OAAO,SAAS;AAAA,IACzB,KAAK;AACH,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,EAAG,QAAO;AAAA,MACnC;AACA,aAAO;AAAA,IACT,KAAK;AACH,UAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,EAAG,QAAO;AAAA,MACnC;AACA,aAAO;AAAA,IACT,KAAK;AACH,UAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,EAAG,QAAO;AAAA,MACnC;AACA,aAAO;AAAA,IACT,KAAK;AACH,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,YAAI,OAAO,CAAC,EAAE,SAAS,EAAG,QAAO;AACjC,YAAI,CAAC,gBAAgB,OAAO,CAAC,CAAC,EAAG,QAAO;AACxC,YAAI,6BAA6B,OAAO,CAAC,CAAC,EAAG,QAAO;AACpD,YAAI,IAAI,GAAG;AACT,cACE,cAAc,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,SACvD,SAAS;AAEZ,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT,KAAK;AACH,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,YAAI,OAAY,KAAK,YAAY,CAAC;AAElC,iBAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM;AACvC,cAAI,KAAK,EAAE,EAAE,SAAS,EAAG,QAAO;AAChC,cAAI,CAAC,gBAAgB,KAAK,EAAE,CAAC,EAAG,QAAO;AACvC,cAAI,6BAA6B,KAAK,EAAE,CAAC,EAAG,QAAO;AACnD,cAAI,OAAO,GAAG;AACZ,gBAAI,CAAC,0BAA0B,MAAM,KAAK,aAAa,CAAC;AACtD,qBAAO;AAAA,UACX;AACA,cAAI,KAAK,GAAG;AACV,gBACE,cAAc,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,SACpD,SAAS;AAEZ,qBAAO;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAEA,SAAS,gBAAgB,MAAkB;AACzC,SACE,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC,KACtC,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;AAE1C;AAEA,SAAS,6BAA6B,MAAkB;AACtD,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,QAAI,QAAQ,KAAK,CAAC;AAClB,aAAS,KAAK,IAAI,GAAG,KAAK,KAAK,SAAS,GAAG,MAAM;AAC/C,UAAI,MAAM,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC;AACjC,UAAI,cAAc,OAAO,WAAW,GAAG,CAAC,EAAG,QAAO;AAAA,IACpD;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,0BACP,MACA,MACA,OACA;AACA,MAAI,cAAc,QAAQ,IAAI;AAC9B,WAAS,IAAI,QAAQ,GAAG,IAAI,KAAK,QAAQ,KAAK;AAC5C,QAAI,CAAC,gBAAgB,aAAa,QAAQ,KAAK,CAAC,CAAC,CAAC,GAAG;AACnD,UAAI,eAAe,aAAa,WAAW,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAG,QAAO;AAAA,IAClE;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAO,6BAAQ;", "names": []}