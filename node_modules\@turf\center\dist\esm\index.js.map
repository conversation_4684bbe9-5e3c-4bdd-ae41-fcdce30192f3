{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { BBox, Feature, GeoJsonProperties, Point } from \"geojson\";\nimport { bbox } from \"@turf/bbox\";\nimport { point, Id, AllGeoJSON } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Feature} or {@link FeatureCollection} and returns the absolute center point of all features.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] Translate GeoJSON Properties to Point\n * @param {Object} [options.bbox={}] Translate GeoJSON BBox to Point\n * @param {Object} [options.id={}] Translate GeoJSON Id to Point\n * @returns {Feature<Point>} a Point feature at the absolute center point of all input features\n * @example\n * var features = turf.points([\n *   [-97.522259, 35.4691],\n *   [-97.502754, 35.463455],\n *   [-97.508269, 35.463245]\n * ]);\n *\n * var center = turf.center(features);\n *\n * //addToMap\n * var addToMap = [features, center]\n * center.properties['marker-size'] = 'large';\n * center.properties['marker-color'] = '#000';\n */\nfunction center<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: AllGeoJSON,\n  options: { properties?: P; bbox?: BBox; id?: Id } = {}\n): Feature<Point, P> {\n  const ext = bbox(geojson);\n  const x = (ext[0] + ext[2]) / 2;\n  const y = (ext[1] + ext[3]) / 2;\n  return point([x, y], options.properties, options);\n}\n\nexport { center };\nexport default center;\n"], "mappings": ";AACA,SAAS,YAAY;AACrB,SAAS,aAA6B;AA0BtC,SAAS,OACP,SACA,UAAoD,CAAC,GAClC;AACnB,QAAM,MAAM,KAAK,OAAO;AACxB,QAAM,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAC9B,QAAM,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAC9B,SAAO,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,YAAY,OAAO;AAClD;AAGA,IAAO,sBAAQ;", "names": []}