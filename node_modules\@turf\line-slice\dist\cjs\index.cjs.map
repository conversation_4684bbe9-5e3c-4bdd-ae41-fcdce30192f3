{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-slice/dist/cjs/index.cjs", "../../index.js"], "names": [], "mappings": "AAAA;ACAA,4CAAmC;AACnC,wCAAyC;AACzC,iEAAmC;AA+BnC,SAAS,SAAA,CAAU,OAAA,EAAS,MAAA,EAAQ,IAAA,EAAM;AAExC,EAAA,IAAI,OAAA,EAAS,kCAAA,IAAc,CAAA;AAC3B,EAAA,GAAA,CAAI,gCAAA,IAAY,EAAA,IAAM,YAAA;AACpB,IAAA,MAAM,IAAI,KAAA,CAAM,2BAA2B,CAAA;AAE7C,EAAA,IAAI,YAAA,EAAc,oDAAA,IAAmB,EAAM,OAAO,CAAA;AAClD,EAAA,IAAI,WAAA,EAAa,oDAAA,IAAmB,EAAM,MAAM,CAAA;AAChD,EAAA,IAAI,IAAA;AACJ,EAAA,GAAA,CAAI,WAAA,CAAY,UAAA,CAAW,MAAA,GAAS,UAAA,CAAW,UAAA,CAAW,KAAA,EAAO;AAC/D,IAAA,KAAA,EAAO,CAAC,WAAA,EAAa,UAAU,CAAA;AAAA,EACjC,EAAA,KAAO;AACL,IAAA,KAAA,EAAO,CAAC,UAAA,EAAY,WAAW,CAAA;AAAA,EACjC;AACA,EAAA,IAAI,WAAA,EAAa,CAAC,IAAA,CAAK,CAAC,CAAA,CAAE,QAAA,CAAS,WAAW,CAAA;AAC9C,EAAA,IAAA,CAAA,IACM,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,CAAE,UAAA,CAAW,MAAA,EAAQ,CAAA,EACnC,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,CAAE,UAAA,CAAW,MAAA,EAAQ,CAAA,EAC/B,CAAA,EAAA,EACA;AACA,IAAA,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AAAA,EAC3B;AACA,EAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,CAAC,CAAA,CAAE,QAAA,CAAS,WAAW,CAAA;AAC5C,EAAA,OAAO,iCAAA,UAAW,EAAY,IAAA,CAAK,UAAU,CAAA;AAC/C;AAGA,IAAO,wBAAA,EAAQ,SAAA;ADpCf;AACE;AACA;AACF,yEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-slice/dist/cjs/index.cjs", "sourcesContent": [null, "import { getCoords, getType } from \"@turf/invariant\";\nimport { lineString as linestring } from \"@turf/helpers\";\nimport { nearestPointOnLine } from \"@turf/nearest-point-on-line\";\n\n/**\n * Takes a {@link LineString|line}, a start {@link Point}, and a stop point\n * and returns a subsection of the line in-between those points.\n * The start & stop points don't need to fall exactly on the line.\n *\n * This can be useful for extracting only the part of a route between waypoints.\n *\n * @function\n * @param {Coord} startPt starting point\n * @param {Coord} stopPt stopping point\n * @param {Feature<LineString>|LineString} line line to slice\n * @returns {Feature<LineString>} sliced line\n * @example\n * var line = turf.lineString([\n *     [-77.031669, 38.878605],\n *     [-77.029609, 38.881946],\n *     [-77.020339, 38.884084],\n *     [-77.025661, 38.885821],\n *     [-77.021884, 38.889563],\n *     [-77.019824, 38.892368]\n * ]);\n * var start = turf.point([-77.029609, 38.881946]);\n * var stop = turf.point([-77.021884, 38.889563]);\n *\n * var sliced = turf.lineSlice(start, stop, line);\n *\n * //addToMap\n * var addToMap = [start, stop, line]\n */\nfunction lineSlice(startPt, stopPt, line) {\n  // Validation\n  var coords = getCoords(line);\n  if (getType(line) !== \"LineString\")\n    throw new Error(\"line must be a LineString\");\n\n  var startVertex = nearestPointOnLine(line, startPt);\n  var stopVertex = nearestPointOnLine(line, stopPt);\n  var ends;\n  if (startVertex.properties.index <= stopVertex.properties.index) {\n    ends = [startVertex, stopVertex];\n  } else {\n    ends = [stopVertex, startVertex];\n  }\n  var clipCoords = [ends[0].geometry.coordinates];\n  for (\n    var i = ends[0].properties.index + 1;\n    i < ends[1].properties.index + 1;\n    i++\n  ) {\n    clipCoords.push(coords[i]);\n  }\n  clipCoords.push(ends[1].geometry.coordinates);\n  return linestring(clipCoords, line.properties);\n}\n\nexport { lineSlice };\nexport default lineSlice;\n"]}