{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-crosses/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,qDAA8B;AAC9B,sDAA8B;AAC9B,uEAAsC;AACtC,4CAAwB;AACxB,wCAAsB;AAqBtB,SAAS,cAAA,CACP,QAAA,EACA,QAAA,EACS;AACT,EAAA,IAAI,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC5B,EAAA,IAAI,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC5B,EAAA,IAAI,MAAA,EAAQ,KAAA,CAAM,IAAA;AAClB,EAAA,IAAI,MAAA,EAAQ,KAAA,CAAM,IAAA;AAElB,EAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,IACb,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,OAAO,8BAAA,CAA+B,KAAA,EAAO,KAAK,CAAA;AAAA,QACpD,KAAK,SAAA;AACH,UAAA,OAAO,uBAAA,CAAwB,KAAA,EAAO,KAAK,CAAA;AAAA,QAC7C,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,OAAO,8BAAA,CAA+B,KAAA,EAAO,KAAK,CAAA;AAAA,QACpD,KAAK,YAAA;AACH,UAAA,OAAO,kBAAA,CAAmB,KAAA,EAAO,KAAK,CAAA;AAAA,QACxC,KAAK,SAAA;AACH,UAAA,OAAO,2BAAA,CAA4B,KAAA,EAAO,KAAK,CAAA;AAAA,QACjD,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,SAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,OAAO,uBAAA,CAAwB,KAAA,EAAO,KAAK,CAAA;AAAA,QAC7C,KAAK,YAAA;AACH,UAAA,OAAO,2BAAA,CAA4B,KAAA,EAAO,KAAK,CAAA;AAAA,QACjD,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,EACnE;AACF;AAEA,SAAS,8BAAA,CACP,UAAA,EACA,UAAA,EACA;AACA,EAAA,IAAI,cAAA,EAAgB,KAAA;AACpB,EAAA,IAAI,cAAA,EAAgB,KAAA;AACpB,EAAA,IAAI,YAAA,EAAc,UAAA,CAAW,WAAA,CAAY,MAAA;AACzC,EAAA,IAAI,EAAA,EAAI,CAAA;AACR,EAAA,MAAA,CAAO,EAAA,EAAI,YAAA,GAAe,CAAC,cAAA,GAAiB,CAAC,aAAA,EAAe;AAC1D,IAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,UAAA,CAAW,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG,EAAA,EAAA,EAAM;AAC7D,MAAA,IAAI,eAAA,EAAiB,IAAA;AACrB,MAAA,GAAA,CAAI,GAAA,IAAO,EAAA,GAAK,GAAA,IAAO,UAAA,CAAW,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG;AACxD,QAAA,eAAA,EAAiB,KAAA;AAAA,MACnB;AACA,MAAA,GAAA,CACE,oBAAA;AAAA,QACE,UAAA,CAAW,WAAA,CAAY,EAAE,CAAA;AAAA,QACzB,UAAA,CAAW,WAAA,CAAY,GAAA,EAAK,CAAC,CAAA;AAAA,QAC7B,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA;AAAA,QACxB;AAAA,MACF,CAAA,EACA;AACA,QAAA,cAAA,EAAgB,IAAA;AAAA,MAClB,EAAA,KAAO;AACL,QAAA,cAAA,EAAgB,IAAA;AAAA,MAClB;AAAA,IACF;AACA,IAAA,CAAA,EAAA;AAAA,EACF;AACA,EAAA,OAAO,cAAA,GAAiB,aAAA;AAC1B;AAEA,SAAS,kBAAA,CAAmB,WAAA,EAAyB,WAAA,EAAyB;AAC5E,EAAA,IAAI,iBAAA,EAAmB,0CAAA,WAAc,EAAa,WAAW,CAAA;AAC7D,EAAA,GAAA,CAAI,gBAAA,CAAiB,QAAA,CAAS,OAAA,EAAS,CAAA,EAAG;AACxC,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,WAAA,CAAY,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AAC3D,MAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,WAAA,CAAY,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG,EAAA,EAAA,EAAM;AAC9D,QAAA,IAAI,eAAA,EAAiB,IAAA;AACrB,QAAA,GAAA,CAAI,GAAA,IAAO,EAAA,GAAK,GAAA,IAAO,WAAA,CAAY,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG;AACzD,UAAA,eAAA,EAAiB,KAAA;AAAA,QACnB;AACA,QAAA,GAAA,CACE,oBAAA;AAAA,UACE,WAAA,CAAY,WAAA,CAAY,CAAC,CAAA;AAAA,UACzB,WAAA,CAAY,WAAA,CAAY,EAAA,EAAI,CAAC,CAAA;AAAA,UAC7B,WAAA,CAAY,WAAA,CAAY,EAAE,CAAA;AAAA,UAC1B;AAAA,QACF,CAAA,EACA;AACA,UAAA,OAAO,IAAA;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,KAAA;AACT;AAEA,SAAS,2BAAA,CAA4B,UAAA,EAAwB,OAAA,EAAkB;AAC7E,EAAA,MAAM,KAAA,EAAY,0CAAA,OAAqB,CAAA;AACvC,EAAA,MAAM,iBAAA,EAAmB,0CAAA,UAAc,EAAY,IAAI,CAAA;AACvD,EAAA,GAAA,CAAI,gBAAA,CAAiB,QAAA,CAAS,OAAA,EAAS,CAAA,EAAG;AACxC,IAAA,OAAO,IAAA;AAAA,EACT;AACA,EAAA,OAAO,KAAA;AACT;AAEA,SAAS,uBAAA,CAAwB,UAAA,EAAwB,OAAA,EAAkB;AACzE,EAAA,IAAI,cAAA,EAAgB,KAAA;AACpB,EAAA,IAAI,cAAA,EAAgB,KAAA;AACpB,EAAA,IAAI,YAAA,EAAc,UAAA,CAAW,WAAA,CAAY,MAAA;AACzC,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,YAAA,GAAA,CAAgB,CAAC,cAAA,GAAiB,CAAC,aAAA,CAAA,EAAgB,CAAA,EAAA,EAAK;AAC1E,IAAA,GAAA,CAAI,0DAAA,4BAAsB,UAAM,CAAW,WAAA,CAAY,CAAC,CAAC,CAAA,EAAG,OAAO,CAAA,EAAG;AACpE,MAAA,cAAA,EAAgB,IAAA;AAAA,IAClB,EAAA,KAAO;AACL,MAAA,cAAA,EAAgB,IAAA;AAAA,IAClB;AAAA,EACF;AAEA,EAAA,OAAO,cAAA,GAAiB,aAAA;AAC1B;AAcA,SAAS,oBAAA,CACP,gBAAA,EACA,cAAA,EACA,EAAA,EACA,MAAA,EACA;AACA,EAAA,IAAI,IAAA,EAAM,EAAA,CAAG,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AACpC,EAAA,IAAI,IAAA,EAAM,EAAA,CAAG,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AACpC,EAAA,IAAI,IAAA,EAAM,cAAA,CAAe,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AAChD,EAAA,IAAI,IAAA,EAAM,cAAA,CAAe,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AAChD,EAAA,IAAI,MAAA,EAAQ,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,GAAA;AAC9B,EAAA,GAAA,CAAI,MAAA,IAAU,CAAA,EAAG;AACf,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,MAAA,EAAQ;AACV,IAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA,EAAG;AAClC,MAAA,OAAO,IAAA,EAAM,EAAA,EACT,gBAAA,CAAiB,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,cAAA,CAAe,CAAC,EAAA,EACzD,cAAA,CAAe,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,gBAAA,CAAiB,CAAC,CAAA;AAAA,IAC/D;AACA,IAAA,OAAO,IAAA,EAAM,EAAA,EACT,gBAAA,CAAiB,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,cAAA,CAAe,CAAC,EAAA,EACzD,cAAA,CAAe,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,gBAAA,CAAiB,CAAC,CAAA;AAAA,EAC/D,EAAA,KAAO;AACL,IAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA,EAAG;AAClC,MAAA,OAAO,IAAA,EAAM,EAAA,EACT,gBAAA,CAAiB,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,EAAI,cAAA,CAAe,CAAC,EAAA,EACvD,cAAA,CAAe,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AAAA,IAC7D;AACA,IAAA,OAAO,IAAA,EAAM,EAAA,EACT,gBAAA,CAAiB,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,EAAI,cAAA,CAAe,CAAC,EAAA,EACvD,cAAA,CAAe,CAAC,EAAA,EAAI,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AAAA,EAC7D;AACF;AAGA,IAAO,6BAAA,EAAQ,cAAA;AD/Df;AACE;AACA;AACF,wFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-crosses/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Geometry, Polygon, LineString, MultiPoint } from \"geojson\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { polygonToLine } from \"@turf/polygon-to-line\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { getGeom } from \"@turf/invariant\";\nimport { point } from \"@turf/helpers\";\n\n/**\n * Boolean-Crosses returns True if the intersection results in a geometry whose dimension is one less than\n * the maximum dimension of the two source geometries and the intersection set is interior to\n * both source geometries.\n *\n * Boolean-Crosses returns t (TRUE) for only multipoint/polygon, multipoint/linestring, linestring/linestring, linestring/polygon, and linestring/multipolygon comparisons.\n * Other comparisons are not supported as they are outside the OpenGIS Simple Features spec and may give unexpected results.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line1 = turf.lineString([[-2, 2], [4, 2]]);\n * var line2 = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n *\n * var cross = turf.booleanCrosses(line1, line2);\n * //=true\n */\nfunction booleanCrosses(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  var geom1 = getGeom(feature1);\n  var geom2 = getGeom(feature2);\n  var type1 = geom1.type;\n  var type2 = geom2.type;\n\n  switch (type1) {\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"LineString\":\n          return doMultiPointAndLineStringCross(geom1, geom2);\n        case \"Polygon\":\n          return doesMultiPointCrossPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"MultiPoint\": // An inverse operation\n          return doMultiPointAndLineStringCross(geom2, geom1);\n        case \"LineString\":\n          return doLineStringsCross(geom1, geom2);\n        case \"Polygon\":\n          return doLineStringAndPolygonCross(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"MultiPoint\": // An inverse operation\n          return doesMultiPointCrossPoly(geom2, geom1);\n        case \"LineString\": // An inverse operation\n          return doLineStringAndPolygonCross(geom2, geom1);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction doMultiPointAndLineStringCross(\n  multiPoint: MultiPoint,\n  lineString: LineString\n) {\n  var foundIntPoint = false;\n  var foundExtPoint = false;\n  var pointLength = multiPoint.coordinates.length;\n  var i = 0;\n  while (i < pointLength && !foundIntPoint && !foundExtPoint) {\n    for (var i2 = 0; i2 < lineString.coordinates.length - 1; i2++) {\n      var incEndVertices = true;\n      if (i2 === 0 || i2 === lineString.coordinates.length - 2) {\n        incEndVertices = false;\n      }\n      if (\n        isPointOnLineSegment(\n          lineString.coordinates[i2],\n          lineString.coordinates[i2 + 1],\n          multiPoint.coordinates[i],\n          incEndVertices\n        )\n      ) {\n        foundIntPoint = true;\n      } else {\n        foundExtPoint = true;\n      }\n    }\n    i++;\n  }\n  return foundIntPoint && foundExtPoint;\n}\n\nfunction doLineStringsCross(lineString1: LineString, lineString2: LineString) {\n  var doLinesIntersect = lineIntersect(lineString1, lineString2);\n  if (doLinesIntersect.features.length > 0) {\n    for (var i = 0; i < lineString1.coordinates.length - 1; i++) {\n      for (var i2 = 0; i2 < lineString2.coordinates.length - 1; i2++) {\n        var incEndVertices = true;\n        if (i2 === 0 || i2 === lineString2.coordinates.length - 2) {\n          incEndVertices = false;\n        }\n        if (\n          isPointOnLineSegment(\n            lineString1.coordinates[i],\n            lineString1.coordinates[i + 1],\n            lineString2.coordinates[i2],\n            incEndVertices\n          )\n        ) {\n          return true;\n        }\n      }\n    }\n  }\n  return false;\n}\n\nfunction doLineStringAndPolygonCross(lineString: LineString, polygon: Polygon) {\n  const line: any = polygonToLine(polygon);\n  const doLinesIntersect = lineIntersect(lineString, line);\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\nfunction doesMultiPointCrossPoly(multiPoint: MultiPoint, polygon: Polygon) {\n  var foundIntPoint = false;\n  var foundExtPoint = false;\n  var pointLength = multiPoint.coordinates.length;\n  for (let i = 0; i < pointLength && (!foundIntPoint || !foundExtPoint); i++) {\n    if (booleanPointInPolygon(point(multiPoint.coordinates[i]), polygon)) {\n      foundIntPoint = true;\n    } else {\n      foundExtPoint = true;\n    }\n  }\n\n  return foundExtPoint && foundIntPoint;\n}\n\n/**\n * Is a point on a line segment\n * Only takes into account outer rings\n * See http://stackoverflow.com/a/4833823/1979085\n *\n * @private\n * @param {number[]} lineSegmentStart coord pair of start of line\n * @param {number[]} lineSegmentEnd coord pair of end of line\n * @param {number[]} pt coord pair of point to check\n * @param {boolean} incEnd whether the point is allowed to fall on the line ends\n * @returns {boolean} true/false\n */\nfunction isPointOnLineSegment(\n  lineSegmentStart: number[],\n  lineSegmentEnd: number[],\n  pt: number[],\n  incEnd: boolean\n) {\n  var dxc = pt[0] - lineSegmentStart[0];\n  var dyc = pt[1] - lineSegmentStart[1];\n  var dxl = lineSegmentEnd[0] - lineSegmentStart[0];\n  var dyl = lineSegmentEnd[1] - lineSegmentStart[1];\n  var cross = dxc * dyl - dyc * dxl;\n  if (cross !== 0) {\n    return false;\n  }\n  if (incEnd) {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0\n        ? lineSegmentStart[0] <= pt[0] && pt[0] <= lineSegmentEnd[0]\n        : lineSegmentEnd[0] <= pt[0] && pt[0] <= lineSegmentStart[0];\n    }\n    return dyl > 0\n      ? lineSegmentStart[1] <= pt[1] && pt[1] <= lineSegmentEnd[1]\n      : lineSegmentEnd[1] <= pt[1] && pt[1] <= lineSegmentStart[1];\n  } else {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0\n        ? lineSegmentStart[0] < pt[0] && pt[0] < lineSegmentEnd[0]\n        : lineSegmentEnd[0] < pt[0] && pt[0] < lineSegmentStart[0];\n    }\n    return dyl > 0\n      ? lineSegmentStart[1] < pt[1] && pt[1] < lineSegmentEnd[1]\n      : lineSegmentEnd[1] < pt[1] && pt[1] < lineSegmentStart[1];\n  }\n}\n\nexport { booleanCrosses };\nexport default booleanCrosses;\n"]}