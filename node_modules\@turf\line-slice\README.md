# @turf/line-slice

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineSlice

Takes a [line][1], a start [Point][2], and a stop point
and returns a subsection of the line in-between those points.
The start & stop points don't need to fall exactly on the line.

This can be useful for extracting only the part of a route between waypoints.

### Parameters

*   `startPt` **[Coord][3]** starting point
*   `stopPt` **[Coord][3]** stopping point
*   `line` **([Feature][4]<[LineString][1]> | [LineString][1])** line to slice

### Examples

```javascript
var line = turf.lineString([
    [-77.031669, 38.878605],
    [-77.029609, 38.881946],
    [-77.020339, 38.884084],
    [-77.025661, 38.885821],
    [-77.021884, 38.889563],
    [-77.019824, 38.892368]
]);
var start = turf.point([-77.029609, 38.881946]);
var stop = turf.point([-77.021884, 38.889563]);

var sliced = turf.lineSlice(start, stop, line);

//addToMap
var addToMap = [start, stop, line]
```

Returns **[Feature][4]<[LineString][1]>** sliced line

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/line-slice
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
