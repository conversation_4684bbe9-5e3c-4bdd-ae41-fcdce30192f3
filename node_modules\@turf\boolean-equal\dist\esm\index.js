// index.ts
import { geojsonEquality } from "geojson-equality-ts";
import { cleanCoords } from "@turf/clean-coords";
import { getGeom } from "@turf/invariant";
function booleanEqual(feature1, feature2, options = {}) {
  let precision = options.precision;
  precision = precision === void 0 || precision === null || isNaN(precision) ? 6 : precision;
  if (typeof precision !== "number" || !(precision >= 0)) {
    throw new Error("precision must be a positive number");
  }
  const type1 = getGeom(feature1).type;
  const type2 = getGeom(feature2).type;
  if (type1 !== type2) return false;
  return geojsonEquality(cleanCoords(feature1), cleanCoords(feature2), {
    precision
  });
}
var turf_boolean_equal_default = booleanEqual;
export {
  booleanEqual,
  turf_boolean_equal_default as default
};
//# sourceMappingURL=index.js.map