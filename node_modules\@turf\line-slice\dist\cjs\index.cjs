"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.js
var _invariant = require('@turf/invariant');
var _helpers = require('@turf/helpers');
var _nearestpointonline = require('@turf/nearest-point-on-line');
function lineSlice(startPt, stopPt, line) {
  var coords = _invariant.getCoords.call(void 0, line);
  if (_invariant.getType.call(void 0, line) !== "LineString")
    throw new Error("line must be a LineString");
  var startVertex = _nearestpointonline.nearestPointOnLine.call(void 0, line, startPt);
  var stopVertex = _nearestpointonline.nearestPointOnLine.call(void 0, line, stopPt);
  var ends;
  if (startVertex.properties.index <= stopVertex.properties.index) {
    ends = [startVertex, stopVertex];
  } else {
    ends = [stopVertex, startVertex];
  }
  var clipCoords = [ends[0].geometry.coordinates];
  for (var i = ends[0].properties.index + 1; i < ends[1].properties.index + 1; i++) {
    clipCoords.push(coords[i]);
  }
  clipCoords.push(ends[1].geometry.coordinates);
  return _helpers.lineString.call(void 0, clipCoords, line.properties);
}
var turf_line_slice_default = lineSlice;



exports.default = turf_line_slice_default; exports.lineSlice = lineSlice;
//# sourceMappingURL=index.cjs.map