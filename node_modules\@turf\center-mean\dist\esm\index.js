// index.ts
import { geomEach, coordEach } from "@turf/meta";
import { isNumber, point } from "@turf/helpers";
function centerMean(geojson, options = {}) {
  let sumXs = 0;
  let sumYs = 0;
  let sumNs = 0;
  geomEach(geojson, function(geom, featureIndex, properties) {
    let weight = options.weight ? properties == null ? void 0 : properties[options.weight] : void 0;
    weight = weight === void 0 || weight === null ? 1 : weight;
    if (!isNumber(weight))
      throw new Error(
        "weight value must be a number for feature index " + featureIndex
      );
    weight = Number(weight);
    if (weight > 0) {
      coordEach(geom, function(coord) {
        sumXs += coord[0] * weight;
        sumYs += coord[1] * weight;
        sumNs += weight;
      });
    }
  });
  return point([sumXs / sumNs, sumYs / sumNs], options.properties, options);
}
var turf_center_mean_default = centerMean;
export {
  centerMean,
  turf_center_mean_default as default
};
//# sourceMappingURL=index.js.map