"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.js
var _bearing = require('@turf/bearing');
var _distance = require('@turf/distance');
var _destination = require('@turf/destination');
var _helpers = require('@turf/helpers');
function lineSliceAlong(line, startDist, stopDist, options) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  var coords;
  var slice = [];
  if (line.type === "Feature") coords = line.geometry.coordinates;
  else if (line.type === "LineString") coords = line.coordinates;
  else throw new Error("input must be a LineString Feature or Geometry");
  var origCoordsLength = coords.length;
  var travelled = 0;
  var overshot, direction, interpolated;
  for (var i = 0; i < coords.length; i++) {
    if (startDist >= travelled && i === coords.length - 1) break;
    else if (travelled > startDist && slice.length === 0) {
      overshot = startDist - travelled;
      if (!overshot) {
        slice.push(coords[i]);
        return _helpers.lineString.call(void 0, slice);
      }
      direction = _bearing.bearing.call(void 0, coords[i], coords[i - 1]) - 180;
      interpolated = _destination.destination.call(void 0, coords[i], overshot, direction, options);
      slice.push(interpolated.geometry.coordinates);
    }
    if (travelled >= stopDist) {
      overshot = stopDist - travelled;
      if (!overshot) {
        slice.push(coords[i]);
        return _helpers.lineString.call(void 0, slice);
      }
      direction = _bearing.bearing.call(void 0, coords[i], coords[i - 1]) - 180;
      interpolated = _destination.destination.call(void 0, coords[i], overshot, direction, options);
      slice.push(interpolated.geometry.coordinates);
      return _helpers.lineString.call(void 0, slice);
    }
    if (travelled >= startDist) {
      slice.push(coords[i]);
    }
    if (i === coords.length - 1) {
      return _helpers.lineString.call(void 0, slice);
    }
    travelled += _distance.distance.call(void 0, coords[i], coords[i + 1], options);
  }
  if (travelled < startDist && coords.length === origCoordsLength)
    throw new Error("Start position is beyond line");
  var last = coords[coords.length - 1];
  return _helpers.lineString.call(void 0, [last, last]);
}
var turf_line_slice_along_default = lineSliceAlong;



exports.default = turf_line_slice_along_default; exports.lineSliceAlong = lineSliceAlong;
//# sourceMappingURL=index.cjs.map