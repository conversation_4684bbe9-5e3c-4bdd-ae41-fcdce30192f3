{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-kinks/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACSA,wCAAsB;AAwBtB,SAAS,KAAA,CACP,SAAA,EAC0B;AAC1B,EAAA,IAAI,WAAA;AACJ,EAAA,IAAI,OAAA;AACJ,EAAA,MAAM,QAAA,EAAoC;AAAA,IACxC,IAAA,EAAM,mBAAA;AAAA,IACN,QAAA,EAAU,CAAC;AAAA,EACb,CAAA;AACA,EAAA,GAAA,CAAI,SAAA,CAAU,KAAA,IAAS,SAAA,EAAW;AAChC,IAAA,QAAA,EAAU,SAAA,CAAU,QAAA;AAAA,EACtB,EAAA,KAAO;AACL,IAAA,QAAA,EAAU,SAAA;AAAA,EACZ;AACA,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,YAAA,EAAc;AACjC,IAAA,YAAA,EAAc,CAAC,OAAA,CAAQ,WAAW,CAAA;AAAA,EACpC,EAAA,KAAA,GAAA,CAAW,OAAA,CAAQ,KAAA,IAAS,iBAAA,EAAmB;AAC7C,IAAA,YAAA,EAAc,OAAA,CAAQ,WAAA;AAAA,EACxB,EAAA,KAAA,GAAA,CAAW,OAAA,CAAQ,KAAA,IAAS,cAAA,EAAgB;AAC1C,IAAA,YAAA,EAAc,CAAC,CAAA,CAAE,MAAA,CAAO,GAAG,OAAA,CAAQ,WAAW,CAAA;AAAA,EAChD,EAAA,KAAA,GAAA,CAAW,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW;AACrC,IAAA,YAAA,EAAc,OAAA,CAAQ,WAAA;AAAA,EACxB,EAAA,KAAO;AACL,IAAA,MAAM,IAAI,KAAA;AAAA,MACR;AAAA,IAEF,CAAA;AAAA,EACF;AACA,EAAA,WAAA,CAAY,OAAA,CAAQ,CAAC,KAAA,EAAA,GAAe;AAClC,IAAA,WAAA,CAAY,OAAA,CAAQ,CAAC,KAAA,EAAA,GAAe;AAClC,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AAGzC,QAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AACzC,UAAA,GAAA,CAAI,MAAA,IAAU,KAAA,EAAO;AAEnB,YAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,CAAC,EAAA,IAAM,CAAA,EAAG;AACzB,cAAA,QAAA;AAAA,YACF;AAEA,YAAA,GAAA;AAAA;AAAA,cAEE,EAAA,IAAM,EAAA,GACN,EAAA,IAAM,KAAA,CAAM,OAAA,EAAS,EAAA;AAAA,cAErB,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,KAAA,CAAM,KAAA,CAAM,OAAA,EAAS,CAAC,CAAA,CAAE,CAAC,EAAA,GACzC,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,KAAA,CAAM,KAAA,CAAM,OAAA,EAAS,CAAC,CAAA,CAAE,CAAC;AAAA,YAAA,EACzC;AACA,cAAA,QAAA;AAAA,YACF;AAAA,UACF;AAEA,UAAA,MAAM,aAAA,EAAoB,cAAA;AAAA,YACxB,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,YACV,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,YACV,KAAA,CAAM,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,YACd,KAAA,CAAM,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,YACd,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,YACV,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,YACV,KAAA,CAAM,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,YACd,KAAA,CAAM,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC;AAAA,UAChB,CAAA;AACA,UAAA,GAAA,CAAI,YAAA,EAAc;AAChB,YAAA,OAAA,CAAQ,QAAA,CAAS,IAAA,CAAK,4BAAA,CAAO,YAAA,CAAa,CAAC,CAAA,EAAG,YAAA,CAAa,CAAC,CAAC,CAAC,CAAC,CAAA;AAAA,UACjE;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC,CAAA;AAAA,EACH,CAAC,CAAA;AACD,EAAA,OAAO,OAAA;AACT;AAGA,SAAS,cAAA,CACP,WAAA,EACA,WAAA,EACA,SAAA,EACA,SAAA,EACA,WAAA,EACA,WAAA,EACA,SAAA,EACA,SAAA,EACA;AAIA,EAAA,IAAI,WAAA;AACJ,EAAA,IAAI,CAAA;AACJ,EAAA,IAAI,CAAA;AACJ,EAAA,IAAI,UAAA;AACJ,EAAA,IAAI,UAAA;AACJ,EAAA,MAAM,OAAA,EAAS;AAAA,IACb,CAAA,EAAG,IAAA;AAAA,IACH,CAAA,EAAG,IAAA;AAAA,IACH,OAAA,EAAS,KAAA;AAAA,IACT,OAAA,EAAS;AAAA,EACX,CAAA;AACA,EAAA,YAAA,EAAA,CACG,UAAA,EAAY,WAAA,EAAA,EAAA,CAAgB,UAAA,EAAY,WAAA,EAAA,EAAA,CACxC,UAAA,EAAY,WAAA,EAAA,EAAA,CAAgB,UAAA,EAAY,WAAA,CAAA;AAC3C,EAAA,GAAA,CAAI,YAAA,IAAgB,CAAA,EAAG;AACrB,IAAA,GAAA,CAAI,MAAA,CAAO,EAAA,IAAM,KAAA,GAAQ,MAAA,CAAO,EAAA,IAAM,IAAA,EAAM;AAC1C,MAAA,OAAO,MAAA;AAAA,IACT,EAAA,KAAO;AACL,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,EAAA,EAAI,YAAA,EAAc,WAAA;AAClB,EAAA,EAAA,EAAI,YAAA,EAAc,WAAA;AAClB,EAAA,WAAA,EAAA,CAAc,UAAA,EAAY,WAAA,EAAA,EAAe,EAAA,EAAA,CAAK,UAAA,EAAY,WAAA,EAAA,EAAe,CAAA;AACzE,EAAA,WAAA,EAAA,CAAc,UAAA,EAAY,WAAA,EAAA,EAAe,EAAA,EAAA,CAAK,UAAA,EAAY,WAAA,EAAA,EAAe,CAAA;AACzE,EAAA,EAAA,EAAI,WAAA,EAAa,WAAA;AACjB,EAAA,EAAA,EAAI,WAAA,EAAa,WAAA;AAGjB,EAAA,MAAA,CAAO,EAAA,EAAI,YAAA,EAAc,EAAA,EAAA,CAAK,UAAA,EAAY,WAAA,CAAA;AAC1C,EAAA,MAAA,CAAO,EAAA,EAAI,YAAA,EAAc,EAAA,EAAA,CAAK,UAAA,EAAY,WAAA,CAAA;AAG1C,EAAA,GAAA,CAAI,EAAA,GAAK,EAAA,GAAK,EAAA,GAAK,CAAA,EAAG;AACpB,IAAA,MAAA,CAAO,QAAA,EAAU,IAAA;AAAA,EACnB;AAEA,EAAA,GAAA,CAAI,EAAA,GAAK,EAAA,GAAK,EAAA,GAAK,CAAA,EAAG;AACpB,IAAA,MAAA,CAAO,QAAA,EAAU,IAAA;AAAA,EACnB;AAEA,EAAA,GAAA,CAAI,MAAA,CAAO,QAAA,GAAW,MAAA,CAAO,OAAA,EAAS;AACpC,IAAA,OAAO,CAAC,MAAA,CAAO,CAAA,EAAG,MAAA,CAAO,CAAC,CAAA;AAAA,EAC5B,EAAA,KAAO;AACL,IAAA,OAAO,KAAA;AAAA,EACT;AACF;AAGA,IAAO,mBAAA,EAAQ,KAAA;ADjEf;AACE;AACA;AACF,4DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-kinks/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { point } from \"@turf/helpers\";\n\n/**\n * Takes a {@link LineString|linestring}, {@link MultiLineString|multi-linestring},\n * {@link MultiPolygon|multi-polygon} or {@link Polygon|polygon} and\n * returns {@link Point|points} at all self-intersections.\n *\n * @function\n * @param {Feature<LineString|MultiLineString|MultiPolygon|Polygon>} featureIn input feature\n * @returns {FeatureCollection<Point>} self-intersections\n * @example\n * var poly = turf.polygon([[\n *   [-12.034835, 8.901183],\n *   [-12.060413, 8.899826],\n *   [-12.03638, 8.873199],\n *   [-12.059383, 8.871418],\n *   [-12.034835, 8.901183]\n * ]]);\n *\n * var kinks = turf.kinks(poly);\n *\n * //addToMap\n * var addToMap = [poly, kinks]\n */\nfunction kinks<T extends LineString | MultiLineString | Polygon | MultiPolygon>(\n  featureIn: Feature<T> | T\n): FeatureCollection<Point> {\n  let coordinates: any;\n  let feature: any;\n  const results: FeatureCollection<Point> = {\n    type: \"FeatureCollection\",\n    features: [],\n  };\n  if (featureIn.type === \"Feature\") {\n    feature = featureIn.geometry;\n  } else {\n    feature = featureIn;\n  }\n  if (feature.type === \"LineString\") {\n    coordinates = [feature.coordinates];\n  } else if (feature.type === \"MultiLineString\") {\n    coordinates = feature.coordinates;\n  } else if (feature.type === \"MultiPolygon\") {\n    coordinates = [].concat(...feature.coordinates);\n  } else if (feature.type === \"Polygon\") {\n    coordinates = feature.coordinates;\n  } else {\n    throw new Error(\n      \"Input must be a LineString, MultiLineString, \" +\n        \"Polygon, or MultiPolygon Feature or Geometry\"\n    );\n  }\n  coordinates.forEach((line1: any) => {\n    coordinates.forEach((line2: any) => {\n      for (let i = 0; i < line1.length - 1; i++) {\n        // start iteration at i, intersections for k < i have already\n        // been checked in previous outer loop iterations\n        for (let k = i; k < line2.length - 1; k++) {\n          if (line1 === line2) {\n            // segments are adjacent and always share a vertex, not a kink\n            if (Math.abs(i - k) === 1) {\n              continue;\n            }\n            // first and last segment in a closed lineString or ring always share a vertex, not a kink\n            if (\n              // segments are first and last segment of lineString\n              i === 0 &&\n              k === line1.length - 2 &&\n              // lineString is closed\n              line1[i][0] === line1[line1.length - 1][0] &&\n              line1[i][1] === line1[line1.length - 1][1]\n            ) {\n              continue;\n            }\n          }\n\n          const intersection: any = lineIntersects(\n            line1[i][0],\n            line1[i][1],\n            line1[i + 1][0],\n            line1[i + 1][1],\n            line2[k][0],\n            line2[k][1],\n            line2[k + 1][0],\n            line2[k + 1][1]\n          );\n          if (intersection) {\n            results.features.push(point([intersection[0], intersection[1]]));\n          }\n        }\n      }\n    });\n  });\n  return results;\n}\n\n// modified from http://jsfiddle.net/justin_c_rounds/Gd2S2/light/\nfunction lineIntersects(\n  line1StartX: any,\n  line1StartY: any,\n  line1EndX: any,\n  line1EndY: any,\n  line2StartX: any,\n  line2StartY: any,\n  line2EndX: any,\n  line2EndY: any\n) {\n  // if the lines intersect, the result contains the x and y of the\n  // intersection (treating the lines as infinite) and booleans for whether\n  // line segment 1 or line segment 2 contain the point\n  let denominator;\n  let a;\n  let b;\n  let numerator1;\n  let numerator2;\n  const result = {\n    x: null,\n    y: null,\n    onLine1: false,\n    onLine2: false,\n  };\n  denominator =\n    (line2EndY - line2StartY) * (line1EndX - line1StartX) -\n    (line2EndX - line2StartX) * (line1EndY - line1StartY);\n  if (denominator === 0) {\n    if (result.x !== null && result.y !== null) {\n      return result;\n    } else {\n      return false;\n    }\n  }\n  a = line1StartY - line2StartY;\n  b = line1StartX - line2StartX;\n  numerator1 = (line2EndX - line2StartX) * a - (line2EndY - line2StartY) * b;\n  numerator2 = (line1EndX - line1StartX) * a - (line1EndY - line1StartY) * b;\n  a = numerator1 / denominator;\n  b = numerator2 / denominator;\n\n  // if we cast these lines infinitely in both directions, they intersect here:\n  result.x = line1StartX + a * (line1EndX - line1StartX);\n  result.y = line1StartY + a * (line1EndY - line1StartY);\n\n  // if line1 is a segment and line2 is infinite, they intersect if:\n  if (a >= 0 && a <= 1) {\n    result.onLine1 = true;\n  }\n  // if line2 is a segment and line1 is infinite, they intersect if:\n  if (b >= 0 && b <= 1) {\n    result.onLine2 = true;\n  }\n  // if line1 and line2 are segments, they intersect if both of the above are true\n  if (result.onLine1 && result.onLine2) {\n    return [result.x, result.y];\n  } else {\n    return false;\n  }\n}\n\nexport { kinks };\nexport default kinks;\n"]}