{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-distance/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,4CAAyB;AACzB,wCAAgE;AA2BhE,SAAS,QAAA,CACP,IAAA,EACA,EAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACL;AACA,EAAA,IAAI,aAAA,EAAe,iCAAA,IAAa,CAAA;AAChC,EAAA,IAAI,aAAA,EAAe,iCAAA,EAAW,CAAA;AAC9B,EAAA,IAAI,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,EAAA,EAAI,YAAA,CAAa,CAAC,CAAC,CAAA;AAC7D,EAAA,IAAI,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,EAAA,EAAI,YAAA,CAAa,CAAC,CAAC,CAAA;AAC7D,EAAA,IAAI,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AAC3C,EAAA,IAAI,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AAE3C,EAAA,IAAI,EAAA,EACF,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,KAAA,EAAO,CAAC,CAAA,EAAG,CAAC,EAAA,EAC9B,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,KAAA,EAAO,CAAC,CAAA,EAAG,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAI,CAAA;AAElE,EAAA,OAAO,sCAAA;AAAA,IACL,EAAA,EAAI,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,CAAC,CAAA,EAAG,IAAA,CAAK,IAAA,CAAK,EAAA,EAAI,CAAC,CAAC,CAAA;AAAA,IAC7C,OAAA,CAAQ;AAAA,EACV,CAAA;AACF;AAGA,IAAO,sBAAA,EAAQ,QAAA;ADpCf;AACE;AACA;AACF,qEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-distance/dist/cjs/index.cjs", "sourcesContent": [null, "import { getCoord } from \"@turf/invariant\";\nimport { radiansToLength, degreesToRadians, Coord, Units } from \"@turf/helpers\";\n\n//http://en.wikipedia.org/wiki/Haversine_formula\n//http://www.movable-type.co.uk/scripts/latlong.html\n\n/**\n * Calculates the distance between two {@link Coord|coordinates} in degrees, radians, miles, or kilometers.\n * This uses the [Haversine formula](http://en.wikipedia.org/wiki/Haversine_formula) to account for global curvature.\n *\n * @function\n * @param {Coord} from origin coordinate\n * @param {Coord} to destination coordinate\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] can be degrees, radians, miles, or kilometers\n * @returns {number} distance between the two coordinates\n * @example\n * var from = turf.point([-75.343, 39.984]);\n * var to = turf.point([-75.534, 39.123]);\n * var options = {units: 'miles'};\n *\n * var distance = turf.distance(from, to, options);\n *\n * //addToMap\n * var addToMap = [from, to];\n * from.properties.distance = distance;\n * to.properties.distance = distance;\n */\nfunction distance(\n  from: Coord,\n  to: Coord,\n  options: {\n    units?: Units;\n  } = {}\n) {\n  var coordinates1 = getCoord(from);\n  var coordinates2 = getCoord(to);\n  var dLat = degreesToRadians(coordinates2[1] - coordinates1[1]);\n  var dLon = degreesToRadians(coordinates2[0] - coordinates1[0]);\n  var lat1 = degreesToRadians(coordinates1[1]);\n  var lat2 = degreesToRadians(coordinates2[1]);\n\n  var a =\n    Math.pow(Math.sin(dLat / 2), 2) +\n    Math.pow(Math.sin(dLon / 2), 2) * Math.cos(lat1) * Math.cos(lat2);\n\n  return radiansToLength(\n    2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)),\n    options.units\n  );\n}\n\nexport { distance };\nexport default distance;\n"]}