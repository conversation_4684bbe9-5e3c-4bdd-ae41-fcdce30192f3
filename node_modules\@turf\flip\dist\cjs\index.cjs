"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _meta = require('@turf/meta');
var _helpers = require('@turf/helpers');
var _clone = require('@turf/clone');
function flip(geojson, options) {
  var _a;
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  const mutate = (_a = options.mutate) != null ? _a : false;
  if (!geojson) throw new Error("geojson is required");
  if (mutate === false || mutate === void 0) geojson = _clone.clone.call(void 0, geojson);
  _meta.coordEach.call(void 0, geojson, function(coord) {
    var x = coord[0];
    var y = coord[1];
    coord[0] = y;
    coord[1] = x;
  });
  return geojson;
}
var turf_flip_default = flip;



exports.default = turf_flip_default; exports.flip = flip;
//# sourceMappingURL=index.cjs.map