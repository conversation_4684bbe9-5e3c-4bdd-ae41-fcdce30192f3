{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-isobands/dist/cjs/index.cjs", "../../index.ts", "../../lib/grid-to-matrix.js"], "names": ["isObject", "collectionOf"], "mappings": "AAAA,6EAAI,UAAU,EAAE,MAAM,CAAC,cAAc;AACrC,IAAI,oBAAoB,EAAE,MAAM,CAAC,qBAAqB;AACtD,IAAI,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,cAAc;AAClD,IAAI,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,oBAAoB;AACxD,IAAI,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK;AAC/J,IAAI,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG;AAC/B,EAAE,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChC,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AAClC,MAAM,eAAe,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,EAAE,GAAG,CAAC,mBAAmB;AACzB,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,mBAAmB,CAAC,CAAC,CAAC,EAAE;AAC7C,MAAM,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AACpC,QAAQ,eAAe,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI;AACJ,EAAE,OAAO,CAAC;AACV,CAAC;AACD;AACA;ACjBA,kCAAqB;AACrB,kCAAqB;AACrB,uEAAsC;AACtC,wCAAwB;AACxB,4CAA6B;AAC7B;AACE;AACA;AACA;AACA;AAAA,wCACK;ADmBP;AACA;AE9BA;AACA,kCAA4B;AAC5B;AAkCA,SAAS,YAAA,CAAa,IAAA,EAAM,OAAA,EAAS;AAEnC,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,IAAI,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,WAAA;AACrC,EAAA,IAAI,KAAA,EAAO,OAAA,CAAQ,IAAA;AACnB,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,KAAA;AAGpB,EAAA,qCAAA,IAAa,EAAM,OAAA,EAAS,2BAA2B,CAAA;AAEvD,EAAA,IAAI,aAAA,EAAe,kBAAA,CAAmB,IAAA,EAAM,IAAI,CAAA;AAEhD,EAAA,IAAI,OAAA,EAAS,CAAC,CAAA;AAGd,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,YAAA,CAAa,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC5C,IAAA,IAAI,SAAA,EAAW,YAAA,CAAa,CAAC,CAAA;AAC7B,IAAA,IAAI,IAAA,EAAM,CAAC,CAAA;AACX,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,MAAA,IAAI,MAAA,EAAQ,QAAA,CAAS,CAAC,CAAA;AAEtB,MAAA,GAAA,CAAI,KAAA,CAAM,UAAA,CAAW,SAAS,CAAA,EAAG,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,SAAS,CAAC,CAAA;AAAA,MAAA,KAChE,GAAA,CAAI,IAAA,CAAK,CAAC,CAAA;AAEf,MAAA,GAAA,CAAI,MAAA,IAAU,IAAA,EAAM,KAAA,CAAM,UAAA,CAAW,eAAA,EAAiB,CAAC,CAAA,EAAG,CAAC,CAAA;AAAA,IAC7D;AACA,IAAA,MAAA,CAAO,IAAA,CAAK,GAAG,CAAA;AAAA,EACjB;AAEA,EAAA,OAAO,MAAA;AACT;AAUA,SAAS,kBAAA,CAAmB,MAAA,EAAQ,IAAA,EAAM;AACxC,EAAA,IAAI,iBAAA,EAAmB,CAAC,CAAA;AAGxB,EAAA,+BAAA,MAAY,EAAQ,QAAA,CAAU,KAAA,EAAO;AACnC,IAAA,IAAI,IAAA,EAAM,kCAAA,KAAe,CAAA,CAAE,CAAC,CAAA;AAC5B,IAAA,GAAA,CAAI,CAAC,gBAAA,CAAiB,GAAG,CAAA,EAAG,gBAAA,CAAiB,GAAG,EAAA,EAAI,CAAC,CAAA;AACrD,IAAA,gBAAA,CAAiB,GAAG,CAAA,CAAE,IAAA,CAAK,KAAK,CAAA;AAAA,EAClC,CAAC,CAAA;AAGD,EAAA,IAAI,sBAAA,EAAwB,MAAA,CAAO,IAAA,CAAK,gBAAgB,CAAA,CAAE,GAAA,CAAI,QAAA,CAAU,GAAA,EAAK;AAC3E,IAAA,IAAI,IAAA,EAAM,gBAAA,CAAiB,GAAG,CAAA;AAC9B,IAAA,IAAI,sBAAA,EAAwB,GAAA,CAAI,IAAA,CAAK,QAAA,CAAU,CAAA,EAAG,CAAA,EAAG;AACnD,MAAA,OAAO,kCAAA,CAAW,CAAA,CAAE,CAAC,EAAA,EAAI,kCAAA,CAAW,CAAA,CAAE,CAAC,CAAA;AAAA,IACzC,CAAC,CAAA;AACD,IAAA,OAAO,qBAAA;AAAA,EACT,CAAC,CAAA;AAGD,EAAA,IAAI,YAAA,EAAc,qBAAA,CAAsB,IAAA,CAAK,QAAA,CAAU,CAAA,EAAG,CAAA,EAAG;AAC3D,IAAA,GAAA,CAAI,IAAA,EAAM,OAAO,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,IAAA,KAClD,OAAO,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,EACpD,CAAC,CAAA;AAED,EAAA,OAAO,WAAA;AACT;AF3BA;AACA;ACtDA,kDAAyB;AAsBzB,SAAS,QAAA,CACP,SAAA,EACA,MAAA,EACA,OAAA,EAKiC;AAEjC,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAACA,+BAAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,MAAM,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,WAAA;AACvC,EAAA,MAAM,iBAAA,EAAmB,OAAA,CAAQ,iBAAA,GAAoB,CAAC,CAAA;AACtD,EAAA,MAAM,iBAAA,EAAmB,OAAA,CAAQ,iBAAA,GAAoB,CAAC,CAAA;AAGtD,EAAAC,qCAAAA,SAAa,EAAW,OAAA,EAAS,2BAA2B,CAAA;AAC5D,EAAA,GAAA,CAAI,CAAC,MAAA,EAAQ,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACjD,EAAA,GAAA,CAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,wBAAwB,CAAA;AACpE,EAAA,GAAA,CAAI,CAACD,+BAAAA,gBAAyB,CAAA;AAC5B,IAAA,MAAM,IAAI,KAAA,CAAM,mCAAmC,CAAA;AACrD,EAAA,GAAA,CAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,gBAAgB,CAAA;AACjC,IAAA,MAAM,IAAI,KAAA,CAAM,kCAAkC,CAAA;AAGpD,EAAA,MAAM,OAAA,EAAS,YAAA,CAAa,SAAA,EAAW,EAAE,SAAA,EAAsB,IAAA,EAAM,KAAK,CAAC,CAAA;AAC3E,EAAA,IAAI,SAAA,EAAW,kBAAA,CAAmB,MAAA,EAAQ,MAAA,EAAQ,SAAS,CAAA;AAC3D,EAAA,SAAA,EAAW,eAAA,CAAgB,QAAA,EAAU,MAAA,EAAQ,SAAS,CAAA;AAEtD,EAAA,MAAM,cAAA,EAAgB,QAAA,CAAS,GAAA,CAAI,CAAC,OAAA,EAAS,KAAA,EAAA,GAAU;AACrD,IAAA,GAAA,CAAI,gBAAA,CAAiB,KAAK,EAAA,GAAK,CAACA,+BAAAA,gBAAS,CAAiB,KAAK,CAAC,CAAA,EAAG;AACjE,MAAA,MAAM,IAAI,KAAA,CAAM,iDAAiD,CAAA;AAAA,IACnE;AAEA,IAAA,MAAM,kBAAA,EAAoB,cAAA,CAAA,cAAA,CAAA,CAAA,CAAA,EACrB,gBAAA,CAAA,EACA,gBAAA,CAAiB,KAAK,CAAA,CAAA;AAG3B,IAAA,iBAAA,CAAkB,SAAS,EAAA,EAAK,OAAA,CAA2B,SAAS,CAAA;AAEpE,IAAA,MAAM,OAAA,EAAS,mCAAA;AAAA,MACb,OAAA,CAAQ,YAAA;AAAA,MACR;AAAA,IACF,CAAA;AACA,IAAA,OAAO,MAAA;AAAA,EACT,CAAC,CAAA;AAED,EAAA,OAAO,wCAAA,aAA+B,CAAA;AACxC;AAeA,SAAS,kBAAA,CACP,MAAA,EACA,MAAA,EACA,QAAA,EACgB;AAChB,EAAA,MAAM,SAAA,EAA2B,CAAC,CAAA;AAClC,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,IAAA,MAAM,UAAA,EAAY,CAAC,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA;AAC/B,IAAA,MAAM,UAAA,EAAY,CAAC,MAAA,CAAO,CAAC,CAAA;AAE3B,IAAA,MAAM,eAAA,EAAiB,uCAAA,MAAS,EAAQ,SAAA,EAAW,UAAA,EAAY,SAAS,CAAA;AAKxE,IAAA,MAAM,YAAA,EAAc,WAAA,CAAY,cAAc,CAAA;AAC9C,IAAA,MAAM,aAAA,EAAe,gBAAA,CAAiB,WAAW,CAAA;AAEjD,IAAA,QAAA,CAAS,IAAA,CAAK;AAAA,MACZ,YAAA;AAAA,MACA,CAAC,QAAQ,CAAA,EAAG,UAAA,EAAY,IAAA,EAAM;AAAA,IAChC,CAAC,CAAA;AAAA,EACH;AACA,EAAA,OAAO,QAAA;AACT;AAWA,SAAS,eAAA,CACP,QAAA,EACA,MAAA,EACA,MAAA,EACgB;AAEhB,EAAA,MAAM,SAAA,EAAW,wBAAA,MAAW,CAAA;AAC5B,EAAA,MAAM,cAAA,EAAgB,QAAA,CAAS,CAAC,EAAA,EAAI,QAAA,CAAS,CAAC,CAAA;AAC9C,EAAA,MAAM,eAAA,EAAiB,QAAA,CAAS,CAAC,EAAA,EAAI,QAAA,CAAS,CAAC,CAAA;AAG/C,EAAA,MAAM,GAAA,EAAK,QAAA,CAAS,CAAC,CAAA;AACrB,EAAA,MAAM,GAAA,EAAK,QAAA,CAAS,CAAC,CAAA;AAErB,EAAA,MAAM,YAAA,EAAc,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA;AACvC,EAAA,MAAM,aAAA,EAAe,MAAA,CAAO,OAAA,EAAS,CAAA;AAErC,EAAA,MAAM,OAAA,EAAS,cAAA,EAAgB,WAAA;AAC/B,EAAA,MAAM,OAAA,EAAS,eAAA,EAAiB,YAAA;AAGhC,EAAA,OAAO,QAAA,CAAS,GAAA,CAAI,QAAA,CAAU,OAAA,EAAS;AACrC,IAAA,OAAA,CAAQ,aAAA,EAAgB,OAAA,CAAQ,YAAA,CAAgC,GAAA;AAAA,MAC9D,QAAA,CAAU,WAAA,EAAa;AACrB,QAAA,OAAO,WAAA,CAAY,GAAA,CAAI,QAAA,CAAU,QAAA,EAAU;AACzC,UAAA,OAAO,QAAA,CAAS,GAAA,CAAI,CAAC,KAAA,EAAA,GAAoB;AAAA,YACvC,KAAA,CAAM,CAAC,EAAA,EAAI,OAAA,EAAS,EAAA;AAAA,YACpB,KAAA,CAAM,CAAC,EAAA,EAAI,OAAA,EAAS;AAAA,UACtB,CAAC,CAAA;AAAA,QACH,CAAC,CAAA;AAAA,MACH;AAAA,IACF,CAAA;AAEA,IAAA,OAAO,OAAA;AAAA,EACT,CAAC,CAAA;AACH;AAWA,SAAS,WAAA,CAAY,WAAA,EAAyC;AAC5D,EAAA,MAAM,cAAA,EAAgB,WAAA,CAAY,GAAA,CAAI,QAAA,CAAU,MAAA,EAAQ;AAEtD,IAAA,OAAO,EAAE,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,wBAAA,8BAAK,CAAS,MAAM,CAAC,CAAC,EAAE,CAAA;AAAA,EACvD,CAAC,CAAA;AACD,EAAA,aAAA,CAAc,IAAA,CAAK,QAAA,CAAU,CAAA,EAAG,CAAA,EAAG;AAEjC,IAAA,OAAO,CAAA,CAAE,KAAA,EAAO,CAAA,CAAE,IAAA;AAAA,EACpB,CAAC,CAAA;AAED,EAAA,OAAO,aAAA,CAAc,GAAA,CAAI,QAAA,CAAU,CAAA,EAAG;AACpC,IAAA,OAAO,CAAA,CAAE,IAAA;AAAA,EACX,CAAC,CAAA;AACH;AAWA,SAAS,gBAAA,CAAiB,kBAAA,EAAkD;AAE1E,EAAA,MAAM,OAAA,EAAS,kBAAA,CAAmB,GAAA,CAAI,CAAC,EAAA,EAAA,GAAO;AAC5C,IAAA,OAAO,EAAE,aAAA,EAAe,EAAA,EAAI,OAAA,EAAS,MAAM,CAAA;AAAA,EAC7C,CAAC,CAAA;AACD,EAAA,MAAM,yBAAA,EAA2C,CAAC,CAAA;AAElD,EAAA,MAAA,CAAO,CAAC,UAAA,CAAW,MAAM,CAAA,EAAG;AAC1B,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,MAAA,GAAA,CAAI,CAAC,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS;AAEtB,QAAA,MAAM,MAAA,EAAsB,CAAC,CAAA;AAC7B,QAAA,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA,CAAE,aAAa,CAAA;AAClC,QAAA,MAAA,CAAO,CAAC,CAAA,CAAE,QAAA,EAAU,IAAA;AACpB,QAAA,MAAM,cAAA,EAAgB,8BAAA,CAAS,MAAA,CAAO,CAAC,CAAA,CAAE,aAAa,CAAC,CAAA;AAEvD,QAAA,IAAA,CAAA,IAAS,EAAA,EAAI,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC1C,UAAA,GAAA,CAAI,CAAC,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS;AACtB,YAAA,MAAM,OAAA,EAAS,8BAAA,CAAS,MAAA,CAAO,CAAC,CAAA,CAAE,aAAa,CAAC,CAAA;AAChD,YAAA,GAAA,CAAI,QAAA,CAAS,MAAA,EAAQ,aAAa,CAAA,EAAG;AACnC,cAAA,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA,CAAE,aAAa,CAAA;AAClC,cAAA,MAAA,CAAO,CAAC,CAAA,CAAE,QAAA,EAAU,IAAA;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAEA,QAAA,wBAAA,CAAyB,IAAA,CAAK,KAAK,CAAA;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,wBAAA;AACT;AAQA,SAAS,QAAA,CACP,WAAA,EACA,aAAA,EACS;AACT,EAAA,MAAM,OAAA,EAAS,8BAAA,WAAmB,CAAA;AAClC,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC/C,IAAA,GAAA,CAAI,CAAC,0DAAA,MAAsB,CAAO,QAAA,CAAS,CAAC,CAAA,EAAG,aAAa,CAAA,EAAG;AAC7D,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAOA,SAAS,UAAA,CACP,IAAA,EACS;AACT,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpC,IAAA,GAAA,CAAI,IAAA,CAAK,CAAC,CAAA,CAAE,QAAA,IAAY,KAAA,EAAO;AAC7B,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,sBAAA,EAAQ,QAAA;ADhFf;AACE;AACA;AACF,qEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-isobands/dist/cjs/index.cjs", "sourcesContent": [null, "import { bbox } from \"@turf/bbox\";\nimport { area } from \"@turf/area\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { explode } from \"@turf/explode\";\nimport { collectionOf } from \"@turf/invariant\";\nimport {\n  polygon,\n  multiPolygon,\n  featureCollection,\n  isObject,\n} from \"@turf/helpers\";\n\nimport {\n  FeatureCollection,\n  Point,\n  GeoJsonProperties,\n  MultiPolygon,\n  Position,\n  Polygon,\n  Feature,\n} from \"geojson\";\n\nimport { gridToMatrix } from \"./lib/grid-to-matrix.js\";\nimport { isoBands } from \"marchingsquares\";\n\ntype GroupRingProps = { [prop: string]: string };\ntype GroupedRings =\n  | {\n      groupedRings: Position[][][];\n    }\n  | GroupRingProps;\n\n/**\n * Takes a square or rectangular grid {@link FeatureCollection} of {@link Point} features with z-values and an array of\n * value breaks and generates filled contour isobands.\n *\n * @function\n * @param {FeatureCollection<Point>} pointGrid input points - must be square or rectangular\n * @param {Array<number>} breaks where to draw contours\n * @param {Object} [options={}] options on output\n * @param {string} [options.zProperty='elevation'] the property name in `points` from which z-values will be pulled\n * @param {Object} [options.commonProperties={}] GeoJSON properties passed to ALL isobands\n * @param {Array<Object>} [options.breaksProperties=[]] GeoJSON properties passed, in order, to the correspondent isoband (order defined by breaks)\n * @returns {FeatureCollection<MultiPolygon>} a FeatureCollection of {@link MultiPolygon} features representing isobands\n */\nfunction isobands(\n  pointGrid: FeatureCollection<Point>,\n  breaks: number[],\n  options?: {\n    zProperty?: string;\n    commonProperties?: GeoJsonProperties;\n    breaksProperties?: GeoJsonProperties[];\n  }\n): FeatureCollection<MultiPolygon> {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const zProperty = options.zProperty || \"elevation\";\n  const commonProperties = options.commonProperties || {};\n  const breaksProperties = options.breaksProperties || [];\n\n  // Validation\n  collectionOf(pointGrid, \"Point\", \"Input must contain Points\");\n  if (!breaks) throw new Error(\"breaks is required\");\n  if (!Array.isArray(breaks)) throw new Error(\"breaks is not an Array\");\n  if (!isObject(commonProperties))\n    throw new Error(\"commonProperties is not an Object\");\n  if (!Array.isArray(breaksProperties))\n    throw new Error(\"breaksProperties is not an Array\");\n\n  // Isoband methods\n  const matrix = gridToMatrix(pointGrid, { zProperty: zProperty, flip: true });\n  let contours = createContourLines(matrix, breaks, zProperty);\n  contours = rescaleContours(contours, matrix, pointGrid);\n\n  const multipolygons = contours.map((contour, index) => {\n    if (breaksProperties[index] && !isObject(breaksProperties[index])) {\n      throw new Error(\"Each mappedProperty is required to be an Object\");\n    }\n    // collect all properties\n    const contourProperties = {\n      ...commonProperties,\n      ...breaksProperties[index],\n    };\n\n    contourProperties[zProperty] = (contour as GroupRingProps)[zProperty];\n\n    const multiP = multiPolygon(\n      contour.groupedRings as Position[][][],\n      contourProperties\n    );\n    return multiP;\n  });\n\n  return featureCollection(multipolygons);\n}\n\n/**\n * Creates the contours lines (featuresCollection of polygon features) from the 2D data grid\n *\n * Marchingsquares process the grid data as a 3D representation of a function on a 2D plane, therefore it\n * assumes the points (x-y coordinates) are one 'unit' distance. The result of the IsoBands function needs to be\n * rescaled, with turfjs, to the original area and proportions on the map\n *\n * @private\n * @param {Array<Array<number>>} matrix Grid Data\n * @param {Array<number>} breaks Breaks\n * @param {string} [property='elevation'] Property\n * @returns {Array<any>} contours\n */\nfunction createContourLines(\n  matrix: number[][],\n  breaks: number[],\n  property: string\n): GroupedRings[] {\n  const contours: GroupedRings[] = [];\n  for (let i = 1; i < breaks.length; i++) {\n    const lowerBand = +breaks[i - 1]; // make sure the breaks value is a number\n    const upperBand = +breaks[i];\n\n    const isobandsCoords = isoBands(matrix, lowerBand, upperBand - lowerBand);\n    // as per GeoJson rules for creating a Polygon, make sure the first element\n    // in the array of LinearRings represents the exterior ring (i.e. biggest area),\n    // and any subsequent elements represent interior rings (i.e. smaller area);\n    // this avoids rendering issues of the MultiPolygons on the map\n    const nestedRings = orderByArea(isobandsCoords);\n    const groupedRings = groupNestedRings(nestedRings);\n\n    contours.push({\n      groupedRings: groupedRings as Position[][][],\n      [property]: lowerBand + \"-\" + upperBand,\n    });\n  }\n  return contours;\n}\n\n/**\n * Transform isobands of 2D grid to polygons for the map\n *\n * @private\n * @param {Array<any>} contours Contours\n * @param {Array<Array<number>>} matrix Grid Data\n * @param {Object} points Points by Latitude\n * @returns {Array<any>} contours\n */\nfunction rescaleContours(\n  contours: GroupedRings[],\n  matrix: number[][],\n  points: FeatureCollection<Point>\n): GroupedRings[] {\n  // get dimensions (on the map) of the original grid\n  const gridBbox = bbox(points); // [ minX, minY, maxX, maxY ]\n  const originalWidth = gridBbox[2] - gridBbox[0];\n  const originalHeigth = gridBbox[3] - gridBbox[1];\n\n  // get origin, which is the first point of the last row on the rectangular data on the map\n  const x0 = gridBbox[0];\n  const y0 = gridBbox[1];\n  // get number of cells per side\n  const matrixWidth = matrix[0].length - 1;\n  const matrixHeight = matrix.length - 1;\n  // calculate the scaling factor between matrix and rectangular grid on the map\n  const scaleX = originalWidth / matrixWidth;\n  const scaleY = originalHeigth / matrixHeight;\n\n  // resize and shift each point/line of the isobands\n  return contours.map(function (contour) {\n    contour.groupedRings = (contour.groupedRings as Position[][][]).map(\n      function (lineRingSet) {\n        return lineRingSet.map(function (lineRing) {\n          return lineRing.map((point: Position) => [\n            point[0] * scaleX + x0,\n            point[1] * scaleY + y0,\n          ]);\n        });\n      }\n    );\n\n    return contour;\n  });\n}\n\n/*  utility functions */\n\n/**\n * Returns an array of coordinates (of LinearRings) in descending order by area\n *\n * @private\n * @param {Array<LineString>} ringsCoords array of closed LineString\n * @returns {Array} array of the input LineString ordered by area\n */\nfunction orderByArea(ringsCoords: Position[][]): Position[][] {\n  const ringsWithArea = ringsCoords.map(function (coords) {\n    // associate each lineRing with its area\n    return { ring: coords, area: area(polygon([coords])) };\n  });\n  ringsWithArea.sort(function (a, b) {\n    // bigger --> smaller\n    return b.area - a.area;\n  });\n  // create a new array of linearRings coordinates ordered by their area\n  return ringsWithArea.map(function (x) {\n    return x.ring;\n  });\n}\n\n/**\n * Returns an array of arrays of coordinates, each representing\n * a set of (coordinates of) nested LinearRings,\n * i.e. the first ring contains all the others\n *\n * @private\n * @param {Array} orderedLinearRings array of coordinates (of LinearRings) in descending order by area\n * @returns {Array<Array>} Array of coordinates of nested LinearRings\n */\nfunction groupNestedRings(orderedLinearRings: Position[][]): Position[][][] {\n  // create a list of the (coordinates of) LinearRings\n  const lrList = orderedLinearRings.map((lr) => {\n    return { lrCoordinates: lr, grouped: false };\n  });\n  const groupedLinearRingsCoords: Position[][][] = [];\n\n  while (!allGrouped(lrList)) {\n    for (let i = 0; i < lrList.length; i++) {\n      if (!lrList[i].grouped) {\n        // create new group starting with the larger not already grouped ring\n        const group: Position[][] = [];\n        group.push(lrList[i].lrCoordinates);\n        lrList[i].grouped = true;\n        const outerMostPoly = polygon([lrList[i].lrCoordinates]);\n        // group all the rings contained by the outermost ring\n        for (let j = i + 1; j < lrList.length; j++) {\n          if (!lrList[j].grouped) {\n            const lrPoly = polygon([lrList[j].lrCoordinates]);\n            if (isInside(lrPoly, outerMostPoly)) {\n              group.push(lrList[j].lrCoordinates);\n              lrList[j].grouped = true;\n            }\n          }\n        }\n        // insert the new group\n        groupedLinearRingsCoords.push(group);\n      }\n    }\n  }\n  return groupedLinearRingsCoords;\n}\n\n/**\n * @private\n * @param {Polygon} testPolygon polygon of interest\n * @param {Polygon} targetPolygon polygon you want to compare with\n * @returns {boolean} true if test-Polygon is inside target-Polygon\n */\nfunction isInside(\n  testPolygon: Feature<Polygon>,\n  targetPolygon: Feature<Polygon>\n): boolean {\n  const points = explode(testPolygon);\n  for (let i = 0; i < points.features.length; i++) {\n    if (!booleanPointInPolygon(points.features[i], targetPolygon)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * @private\n * @param {Array<Object>} list list of objects which might contain the 'group' attribute\n * @returns {boolean} true if all the objects in the list are marked as grouped\n */\nfunction allGrouped(\n  list: { grouped: boolean; lrCoordinates: Position[] }[]\n): boolean {\n  for (let i = 0; i < list.length; i++) {\n    if (list[i].grouped === false) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport { isobands };\nexport default isobands;\n", "import { getCoords, collectionOf } from \"@turf/invariant\";\nimport { featureEach } from \"@turf/meta\";\nimport { isObject } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Point} grid and returns a correspondent matrix {Array<Array<number>>}\n * of the 'property' values\n *\n * @name gridToMatrix\n * @param {FeatureCollection<Point>} grid of points\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.zProperty='elevation'] the property name in `points` from which z-values will be pulled\n * @param {boolean} [options.flip=false] returns the matrix upside-down\n * @param {boolean} [options.flags=false] flags, adding a `matrixPosition` array field ([row, column]) to its properties,\n * the grid points with coordinates on the matrix\n * @returns {Array<Array<number>>} matrix of property values\n * @example\n *   var extent = [-70.823364, -33.553984, -70.473175, -33.302986];\n *   var cellSize = 3;\n *   var grid = turf.pointGrid(extent, cellSize);\n *   // add a random property to each point between 0 and 60\n *   for (var i = 0; i < grid.features.length; i++) {\n *     grid.features[i].properties.elevation = (Math.random() * 60);\n *   }\n *   gridToMatrix(grid);\n *   //= [\n *     [ 1, 13, 10,  9, 10, 13, 18],\n *     [34,  8,  5,  4,  5,  8, 13],\n *     [10,  5,  2,  1,  2,  5,  4],\n *     [ 0,  4, 56, 19,  1,  4,  9],\n *     [10,  5,  2,  1,  2,  5, 10],\n *     [57,  8,  5,  4,  5,  0, 57],\n *     [ 3, 13, 10,  9,  5, 13, 18],\n *     [18, 13, 10,  9, 78, 13, 18]\n *   ]\n */\nfunction gridToMatrix(grid, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var zProperty = options.zProperty || \"elevation\";\n  var flip = options.flip;\n  var flags = options.flags;\n\n  // validation\n  collectionOf(grid, \"Point\", \"input must contain Points\");\n\n  var pointsMatrix = sortPointsByLatLng(grid, flip);\n\n  var matrix = [];\n  // create property matrix from sorted points\n  // looping order matters here\n  for (var r = 0; r < pointsMatrix.length; r++) {\n    var pointRow = pointsMatrix[r];\n    var row = [];\n    for (var c = 0; c < pointRow.length; c++) {\n      var point = pointRow[c];\n      // Check if zProperty exist\n      if (point.properties[zProperty]) row.push(point.properties[zProperty]);\n      else row.push(0);\n      // add flags\n      if (flags === true) point.properties.matrixPosition = [r, c];\n    }\n    matrix.push(row);\n  }\n\n  return matrix;\n}\n\n/**\n * Sorts points by latitude and longitude, creating a 2-dimensional array of points\n *\n * @private\n * @param {FeatureCollection<Point>} points GeoJSON Point features\n * @param {boolean} [flip=false] returns the matrix upside-down\n * @returns {Array<Array<Point>>} points ordered by latitude and longitude\n */\nfunction sortPointsByLatLng(points, flip) {\n  var pointsByLatitude = {};\n\n  // divide points by rows with the same latitude\n  featureEach(points, function (point) {\n    var lat = getCoords(point)[1];\n    if (!pointsByLatitude[lat]) pointsByLatitude[lat] = [];\n    pointsByLatitude[lat].push(point);\n  });\n\n  // sort points (with the same latitude) by longitude\n  var orderedRowsByLatitude = Object.keys(pointsByLatitude).map(function (lat) {\n    var row = pointsByLatitude[lat];\n    var rowOrderedByLongitude = row.sort(function (a, b) {\n      return getCoords(a)[0] - getCoords(b)[0];\n    });\n    return rowOrderedByLongitude;\n  });\n\n  // sort rows (of points with the same latitude) by latitude\n  var pointMatrix = orderedRowsByLatitude.sort(function (a, b) {\n    if (flip) return getCoords(a[0])[1] - getCoords(b[0])[1];\n    else return getCoords(b[0])[1] - getCoords(a[0])[1];\n  });\n\n  return pointMatrix;\n}\n\nexport { gridToMatrix };\nexport default gridToMatrix;\n"]}