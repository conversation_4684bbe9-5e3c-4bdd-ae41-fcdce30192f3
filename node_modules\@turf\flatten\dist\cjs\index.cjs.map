{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-flatten/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,kCAA4B;AAC5B,wCAAkC;AA6ClC,SAAS,OAAA,CAAQ,OAAA,EAAwC;AACvD,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AAEnD,EAAA,IAAI,QAAA,EAAqB,CAAC,CAAA;AAC1B,EAAA,+BAAA,OAAY,EAAS,QAAA,CAAU,OAAA,EAAS;AACtC,IAAA,OAAA,CAAQ,IAAA,CAAK,OAAO,CAAA;AAAA,EACtB,CAAC,CAAA;AACD,EAAA,OAAO,wCAAA,OAAyB,CAAA;AAClC;AAGA,IAAO,qBAAA,EAAQ,OAAA;AD7Cf;AACE;AACA;AACF,kEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-flatten/dist/cjs/index.cjs", "sourcesContent": [null, "import { flattenEach } from \"@turf/meta\";\nimport { featureCollection } from \"@turf/helpers\";\nimport type { AllGeoJSON } from \"@turf/helpers\";\nimport type {\n  Feature,\n  Point,\n  MultiPoint,\n  LineString,\n  MultiLineString,\n  FeatureCollection,\n  Polygon,\n  MultiPolygon,\n} from \"geojson\";\n\n/**\n * Flattens any {@link GeoJSON} to a {@link FeatureCollection} inspired by [geojson-flatten](https://github.com/tmcw/geojson-flatten).\n *\n * @function\n * @param {GeoJSON} geojson any valid GeoJSON Object\n * @returns {FeatureCollection<any>} all Multi-Geometries are flattened into single Features\n * @example\n * var multiGeometry = turf.multiPolygon([\n *   [[[102.0, 2.0], [103.0, 2.0], [103.0, 3.0], [102.0, 3.0], [102.0, 2.0]]],\n *   [[[100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0]],\n *   [[100.2, 0.2], [100.8, 0.2], [100.8, 0.8], [100.2, 0.8], [100.2, 0.2]]]\n * ]);\n *\n * var flatten = turf.flatten(multiGeometry);\n *\n * //addToMap\n * var addToMap = [flatten]\n */\nfunction flatten<T extends Point | MultiPoint>(\n  geojson: Feature<T> | FeatureCollection<T> | T\n): FeatureCollection<Point>;\n\nfunction flatten<T extends LineString | MultiLineString>(\n  geojson: Feature<T> | FeatureCollection<T> | T\n): FeatureCollection<LineString>;\n\nfunction flatten<T extends Polygon | MultiPolygon>(\n  geojson: Feature<T> | FeatureCollection<T> | T\n): FeatureCollection<Polygon>;\n\nfunction flatten(geojson: AllGeoJSON): FeatureCollection<any>;\n\nfunction flatten(geojson: AllGeoJSON): FeatureCollection {\n  if (!geojson) throw new Error(\"geojson is required\");\n\n  var results: Feature[] = [];\n  flattenEach(geojson, function (feature) {\n    results.push(feature);\n  });\n  return featureCollection(results);\n}\n\nexport { flatten };\nexport default flatten;\n"]}