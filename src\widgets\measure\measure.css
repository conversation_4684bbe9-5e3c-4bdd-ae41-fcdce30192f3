/* 测量工具样式 */
#measurePanel {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 280px;
    max-height: 600px;
    background: rgba(42, 42, 42, 0.95);
    border: 1px solid #555;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.cesium-panel-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 12px 15px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #555;
}

.cesium-panel-title {
    font-size: 14px;
    font-weight: bold;
    margin: 0;
}

.cesium-panel-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.cesium-panel-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.cesium-panel-content {
    padding: 15px;
    max-height: 520px;
    overflow-y: auto;
}

.measure-toolbar {
    margin-bottom: 15px;
}

.measure-group {
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid #444;
}

.measure-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.measure-group label {
    display: block;
    color: #ccc;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.measure-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.cesium-button {
    background: linear-gradient(135deg, #555, #444);
    border: 1px solid #666;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 80px;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
}

.cesium-button:hover {
    background: linear-gradient(135deg, #666, #555);
    border-color: #777;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cesium-button:active {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cesium-button.active {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-color: #4a90e2;
    box-shadow: 0 0 8px rgba(74, 144, 226, 0.4);
}

.cesium-button-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    border-color: #e74c3c !important;
}

.cesium-button-danger:hover {
    background: linear-gradient(135deg, #f56c6c, #e74c3c) !important;
    border-color: #f56c6c !important;
}

.cesium-button i {
    font-size: 12px;
}

/* 测量结果区域 */
.measure-results {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    border: 1px solid #444;
}

.measure-result-header {
    padding: 10px 12px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px 6px 0 0;
}

.measure-result-header span {
    color: #ccc;
    font-size: 12px;
    font-weight: bold;
}

.clear-results-btn {
    background: none;
    border: 1px solid #666;
    color: #ccc;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 10px;
    transition: all 0.2s;
}

.clear-results-btn:hover {
    background: #666;
    color: white;
}

.measure-result-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
}

.measure-result-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #555;
    border-radius: 4px;
    padding: 8px 10px;
    margin-bottom: 6px;
    transition: background-color 0.2s;
}

.measure-result-item:hover {
    background: rgba(255, 255, 255, 0.08);
}

.measure-result-item:last-child {
    margin-bottom: 0;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.result-type {
    color: #4a90e2;
    font-size: 11px;
    font-weight: bold;
}

.result-time {
    color: #888;
    font-size: 10px;
}

.result-value {
    color: #fff;
    font-size: 13px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

/* 滚动条样式 */
.cesium-panel-content::-webkit-scrollbar,
.measure-result-list::-webkit-scrollbar {
    width: 6px;
}

.cesium-panel-content::-webkit-scrollbar-track,
.measure-result-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.cesium-panel-content::-webkit-scrollbar-thumb,
.measure-result-list::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 3px;
}

.cesium-panel-content::-webkit-scrollbar-thumb:hover,
.measure-result-list::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #measurePanel {
        width: 260px;
        right: 10px;
    }
    
    .measure-buttons {
        flex-direction: column;
    }
    
    .cesium-button {
        width: 100%;
        min-width: auto;
    }
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

#measurePanel {
    animation: slideIn 0.3s ease-out;
}

/* 工具提示样式 */
.measure-tooltip {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 10000;
    max-width: 200px;
    word-wrap: break-word;
}

/* 按钮图标对齐 */
.cesium-button i {
    margin-right: 4px;
}

/* 特殊状态样式 */
.measure-group.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.cesium-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.cesium-button:disabled:hover {
    transform: none;
    box-shadow: none;
}
