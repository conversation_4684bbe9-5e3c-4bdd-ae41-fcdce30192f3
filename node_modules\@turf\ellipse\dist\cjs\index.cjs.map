{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-ellipse/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA;AACE;AACA;AACA;AACA;AAAA,wCAGK;AACP,2DAAiC;AACjC,yDAAgC;AAChC,4CAAyB;AAyBzB,SAAS,OAAA,CACP,MAAA,EACA,SAAA,EACA,SAAA,EACA,OAAA,EAOkB;AAElB,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,EAAA;AAC/B,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,YAAA;AAC/B,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,CAAA;AAC/B,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,MAAA;AAC/B,EAAA,MAAM,WAAA,EAAa,OAAA,CAAQ,WAAA,GAAc,CAAC,CAAA;AAG1C,EAAA,GAAA,CAAI,CAAC,MAAA,EAAQ,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACjD,EAAA,GAAA,CAAI,CAAC,SAAA,EAAW,MAAM,IAAI,KAAA,CAAM,uBAAuB,CAAA;AACvD,EAAA,GAAA,CAAI,CAAC,SAAA,EAAW,MAAM,IAAI,KAAA,CAAM,uBAAuB,CAAA;AACvD,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,2BAA2B,CAAA;AACnE,EAAA,GAAA,CAAI,CAAC,+BAAA,KAAc,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,wBAAwB,CAAA;AAC9D,EAAA,GAAA,CAAI,CAAC,+BAAA,KAAc,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,wBAAwB,CAAA;AAE9D,EAAA,MAAM,aAAA,EAAe,iCAAA,MAAe,CAAA;AACpC,EAAA,GAAA,CAAI,MAAA,IAAU,SAAA,EAAW;AACvB,IAAA,MAAM,MAAA,EAAQ,gDAAA,MAAiB,EAAQ,SAAA,EAAW,EAAA,EAAI,EAAE,MAAM,CAAC,CAAA;AAC/D,IAAA,MAAM,MAAA,EAAQ,gDAAA,MAAiB,EAAQ,SAAA,EAAW,CAAA,EAAG,EAAE,MAAM,CAAC,CAAA;AAC9D,IAAA,UAAA,EAAY,iCAAA,KAAc,CAAA,CAAE,CAAC,EAAA,EAAI,YAAA,CAAa,CAAC,CAAA;AAC/C,IAAA,UAAA,EAAY,iCAAA,KAAc,CAAA,CAAE,CAAC,EAAA,EAAI,YAAA,CAAa,CAAC,CAAA;AAAA,EACjD;AAEA,EAAA,MAAM,YAAA,EAA0B,CAAC,CAAA;AACjC,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,EAAO,EAAA,GAAK,CAAA,EAAG;AACjC,IAAA,MAAM,UAAA,EAAa,EAAA,EAAI,CAAA,IAAA,EAAQ,KAAA;AAC/B,IAAA,IAAI,EAAA,EACD,UAAA,EAAY,UAAA,EACb,IAAA,CAAK,IAAA;AAAA,MACH,IAAA,CAAK,GAAA,CAAI,SAAA,EAAW,CAAC,EAAA,EACnB,IAAA,CAAK,GAAA,CAAI,SAAA,EAAW,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,SAAA,CAAU,SAAS,CAAA,EAAG,CAAC;AAAA,IAC7D,CAAA;AACF,IAAA,IAAI,EAAA,EACD,UAAA,EAAY,UAAA,EACb,IAAA,CAAK,IAAA;AAAA,MACH,IAAA,CAAK,GAAA,CAAI,SAAA,EAAW,CAAC,EAAA,EACnB,IAAA,CAAK,GAAA,CAAI,SAAA,EAAW,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,SAAA,CAAU,SAAS,CAAA,EAAG,CAAC;AAAA,IAC7D,CAAA;AAEF,IAAA,GAAA,CAAI,UAAA,EAAY,CAAA,GAAA,GAAO,UAAA,GAAa,CAAA,GAAA,EAAM,EAAA,EAAI,CAAC,CAAA;AAC/C,IAAA,GAAA,CAAI,UAAA,EAAY,CAAA,IAAA,GAAQ,UAAA,GAAa,CAAA,GAAA,EAAM,EAAA,EAAI,CAAC,CAAA;AAChD,IAAA,GAAA,CAAI,MAAA,IAAU,SAAA,EAAW;AACvB,MAAA,MAAM,SAAA,EAAW,uCAAA,KAAsB,CAAA;AACvC,MAAA,MAAM,KAAA,EAAO,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,QAAQ,EAAA,EAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,QAAQ,CAAA;AAC3D,MAAA,MAAM,KAAA,EAAO,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,QAAQ,EAAA,EAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,QAAQ,CAAA;AAC3D,MAAA,EAAA,EAAI,IAAA;AACJ,MAAA,EAAA,EAAI,IAAA;AAAA,IACN;AAEA,IAAA,WAAA,CAAY,IAAA,CAAK,CAAC,EAAA,EAAI,YAAA,CAAa,CAAC,CAAA,EAAG,EAAA,EAAI,YAAA,CAAa,CAAC,CAAC,CAAC,CAAA;AAAA,EAC7D;AACA,EAAA,WAAA,CAAY,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC,CAAA;AAC/B,EAAA,GAAA,CAAI,MAAA,IAAU,SAAA,EAAW;AACvB,IAAA,OAAO,8BAAA,CAAS,WAAW,CAAA,EAAG,UAAU,CAAA;AAAA,EAC1C,EAAA,KAAO;AACL,IAAA,OAAO,8CAAA,8BAAgB,CAAS,WAAW,CAAA,EAAG,UAAU,CAAA,EAAG,KAAA,EAAO;AAAA,MAChE;AAAA,IACF,CAAC,CAAA;AAAA,EACH;AACF;AASA,SAAS,SAAA,CAAU,GAAA,EAAa;AAC9B,EAAA,MAAM,IAAA,EAAO,IAAA,EAAM,IAAA,CAAK,GAAA,EAAM,GAAA;AAC9B,EAAA,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA;AACrB;AAGA,IAAO,qBAAA,EAAQ,OAAA;AD1Df;AACE;AACA;AACF,kEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-ellipse/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  degreesToRadians,\n  polygon,\n  isObject,\n  isN<PERSON>ber,\n  Coord,\n  Units,\n} from \"@turf/helpers\";\nimport { rhumbDestination } from \"@turf/rhumb-destination\";\nimport { transformRotate } from \"@turf/transform-rotate\";\nimport { getCoord } from \"@turf/invariant\";\nimport { GeoJsonProperties, Feature, Polygon } from \"geojson\";\n\n/**\n * Takes a {@link Point} and calculates the ellipse polygon given two semi-axes expressed in variable units and steps for precision.\n *\n * @param {Coord} center center point\n * @param {number} xSemiAxis semi (major) axis of the ellipse along the x-axis\n * @param {number} ySemiAxis semi (minor) axis of the ellipse along the y-axis\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.angle=0] angle of rotation in decimal degrees, positive clockwise\n * @param {Coord} [options.pivot=center] point around which any rotation will be performed\n * @param {number} [options.steps=64] number of steps\n * @param {string} [options.units='kilometers'] unit of measurement for axes\n * @param {Object} [options.properties={}] properties\n * @returns {Feature<Polygon>} ellipse polygon\n * @example\n * var center = [-75, 40];\n * var xSemiAxis = 5;\n * var ySemiAxis = 2;\n * var ellipse = turf.ellipse(center, xSemiAxis, ySemiAxis);\n *\n * //addToMap\n * var addToMap = [turf.point(center), ellipse]\n */\nfunction ellipse(\n  center: Coord,\n  xSemiAxis: number,\n  ySemiAxis: number,\n  options: {\n    steps?: number;\n    units?: Units;\n    angle?: number;\n    pivot?: Coord;\n    properties?: GeoJsonProperties;\n  }\n): Feature<Polygon> {\n  // Optional params\n  options = options || {};\n  const steps = options.steps || 64;\n  const units = options.units || \"kilometers\";\n  const angle = options.angle || 0;\n  const pivot = options.pivot || center;\n  const properties = options.properties || {};\n\n  // validation\n  if (!center) throw new Error(\"center is required\");\n  if (!xSemiAxis) throw new Error(\"xSemiAxis is required\");\n  if (!ySemiAxis) throw new Error(\"ySemiAxis is required\");\n  if (!isObject(options)) throw new Error(\"options must be an object\");\n  if (!isNumber(steps)) throw new Error(\"steps must be a number\");\n  if (!isNumber(angle)) throw new Error(\"angle must be a number\");\n\n  const centerCoords = getCoord(center);\n  if (units !== \"degrees\") {\n    const xDest = rhumbDestination(center, xSemiAxis, 90, { units });\n    const yDest = rhumbDestination(center, ySemiAxis, 0, { units });\n    xSemiAxis = getCoord(xDest)[0] - centerCoords[0];\n    ySemiAxis = getCoord(yDest)[1] - centerCoords[1];\n  }\n\n  const coordinates: number[][] = [];\n  for (let i = 0; i < steps; i += 1) {\n    const stepAngle = (i * -360) / steps;\n    let x =\n      (xSemiAxis * ySemiAxis) /\n      Math.sqrt(\n        Math.pow(ySemiAxis, 2) +\n          Math.pow(xSemiAxis, 2) * Math.pow(getTanDeg(stepAngle), 2)\n      );\n    let y =\n      (xSemiAxis * ySemiAxis) /\n      Math.sqrt(\n        Math.pow(xSemiAxis, 2) +\n          Math.pow(ySemiAxis, 2) / Math.pow(getTanDeg(stepAngle), 2)\n      );\n\n    if (stepAngle < -90 && stepAngle >= -270) x = -x;\n    if (stepAngle < -180 && stepAngle >= -360) y = -y;\n    if (units === \"degrees\") {\n      const angleRad = degreesToRadians(angle);\n      const newx = x * Math.cos(angleRad) + y * Math.sin(angleRad);\n      const newy = y * Math.cos(angleRad) - x * Math.sin(angleRad);\n      x = newx;\n      y = newy;\n    }\n\n    coordinates.push([x + centerCoords[0], y + centerCoords[1]]);\n  }\n  coordinates.push(coordinates[0]);\n  if (units === \"degrees\") {\n    return polygon([coordinates], properties);\n  } else {\n    return transformRotate(polygon([coordinates], properties), angle, {\n      pivot,\n    });\n  }\n}\n\n/**\n * Get Tan Degrees\n *\n * @private\n * @param {number} deg Degrees\n * @returns {number} Tan Degrees\n */\nfunction getTanDeg(deg: number) {\n  const rad = (deg * Math.PI) / 180;\n  return Math.tan(rad);\n}\n\nexport { ellipse };\nexport default ellipse;\n"]}