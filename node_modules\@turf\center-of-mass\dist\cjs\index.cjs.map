{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-center-of-mass/dist/cjs/index.cjs", "../../index.ts"], "names": ["point"], "mappings": "AAAA;ACCA,sCAAuB;AACvB,0CAAyB;AACzB,wCAAsB;AACtB,4CAAkC;AAClC,kCAA0B;AAkB1B,SAAS,YAAA,CACP,OAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACc;AACnB,EAAA,OAAA,CAAQ,gCAAA,OAAe,CAAA,EAAG;AAAA,IACxB,KAAK,OAAA;AACH,MAAA,OAAO,4BAAA,iCAAM,OAAgB,CAAA,EAAG,OAAA,CAAQ,UAAU,CAAA;AAAA,IACpD,KAAK,SAAA;AACH,MAAA,IAAI,OAAA,EAAqB,CAAC,CAAA;AAC1B,MAAA,6BAAA,OAAU,EAAS,QAAA,CAAU,KAAA,EAAO;AAClC,QAAA,MAAA,CAAO,IAAA,CAAK,KAAK,CAAA;AAAA,MACnB,CAAC,CAAA;AAID,MAAA,IAAI,OAAA,EAAS,gCAAA,OAAS,EAAS,EAAE,UAAA,EAAY,OAAA,CAAQ,WAAW,CAAC,CAAA;AACjE,MAAA,IAAI,YAAA,EAAc,MAAA,CAAO,QAAA,CAAS,WAAA;AAClC,MAAA,IAAI,GAAA,EAAK,CAAA;AACT,MAAA,IAAI,GAAA,EAAK,CAAA;AACT,MAAA,IAAI,MAAA,EAAQ,CAAA;AACZ,MAAA,IAAI,CAAA,EAAG,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,CAAA;AAE/B,MAAA,IAAI,kBAAA,EAAoB,MAAA,CAAO,GAAA,CAAI,QAAA,CAAUA,MAAAA,EAAO;AAClD,QAAA,OAAO,CAACA,MAAAA,CAAM,CAAC,EAAA,EAAI,WAAA,CAAY,CAAC,CAAA,EAAGA,MAAAA,CAAM,CAAC,EAAA,EAAI,WAAA,CAAY,CAAC,CAAC,CAAA;AAAA,MAC9D,CAAC,CAAA;AAED,MAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AAEtC,QAAA,GAAA,EAAK,iBAAA,CAAkB,CAAC,CAAA;AACxB,QAAA,GAAA,EAAK,EAAA,CAAG,CAAC,CAAA;AACT,QAAA,GAAA,EAAK,EAAA,CAAG,CAAC,CAAA;AAGT,QAAA,GAAA,EAAK,iBAAA,CAAkB,EAAA,EAAI,CAAC,CAAA;AAC5B,QAAA,GAAA,EAAK,EAAA,CAAG,CAAC,CAAA;AACT,QAAA,GAAA,EAAK,EAAA,CAAG,CAAC,CAAA;AAGT,QAAA,EAAA,EAAI,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,EAAA;AAGnB,QAAA,MAAA,GAAS,CAAA;AAGT,QAAA,GAAA,GAAA,CAAO,GAAA,EAAK,EAAA,EAAA,EAAM,CAAA;AAClB,QAAA,GAAA,GAAA,CAAO,GAAA,EAAK,EAAA,EAAA,EAAM,CAAA;AAAA,MACpB;AAGA,MAAA,GAAA,CAAI,MAAA,IAAU,CAAA,EAAG;AACf,QAAA,OAAO,MAAA;AAAA,MACT,EAAA,KAAO;AAEL,QAAA,IAAI,KAAA,EAAO,MAAA,EAAQ,GAAA;AACnB,QAAA,IAAI,WAAA,EAAa,EAAA,EAAA,CAAK,EAAA,EAAI,IAAA,CAAA;AAG1B,QAAA,OAAO,4BAAA;AAAA,UACL,CAAC,WAAA,CAAY,CAAC,EAAA,EAAI,WAAA,EAAa,EAAA,EAAI,WAAA,CAAY,CAAC,EAAA,EAAI,WAAA,EAAa,EAAE,CAAA;AAAA,UACnE,OAAA,CAAQ;AAAA,QACV,CAAA;AAAA,MACF;AAAA,IACF,OAAA;AAEE,MAAA,IAAI,KAAA,EAAO,4BAAA,OAAc,CAAA;AAEzB,MAAA,GAAA,CAAI,IAAA,EAAM,OAAO,YAAA,CAAa,IAAA,EAAM,EAAE,UAAA,EAAY,OAAA,CAAQ,WAAW,CAAC,CAAA;AAAA,MAAA,KAEjE,OAAO,gCAAA,OAAS,EAAS,EAAE,UAAA,EAAY,OAAA,CAAQ,WAAW,CAAC,CAAA;AAAA,EACpE;AACF;AAGA,IAAO,4BAAA,EAAQ,YAAA;AD7Cf;AACE;AACA;AACF,mFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-center-of-mass/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, GeoJsonProperties, Point, Position } from \"geojson\";\nimport { convex } from \"@turf/convex\";\nimport { centroid } from \"@turf/centroid\";\nimport { point } from \"@turf/helpers\";\nimport { getType, getCoord } from \"@turf/invariant\";\nimport { coordEach } from \"@turf/meta\";\n\n/**\n * Takes any {@link Feature} or a {@link FeatureCollection} and returns its [center of mass](https://en.wikipedia.org/wiki/Center_of_mass) using this formula: [Centroid of Polygon](https://en.wikipedia.org/wiki/Centroid#Centroid_of_polygon).\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional Parameters\n * @param {Object} [options.properties={}] Translate Properties to Feature\n * @returns {Feature<Point>} the center of mass\n * @example\n * var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);\n *\n * var center = turf.centerOfMass(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, center]\n */\nfunction centerOfMass<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: any,\n  options: {\n    properties?: P;\n  } = {}\n): Feature<Point, P> {\n  switch (getType(geojson)) {\n    case \"Point\":\n      return point(getCoord(geojson), options.properties);\n    case \"Polygon\":\n      var coords: Position[] = [];\n      coordEach(geojson, function (coord) {\n        coords.push(coord);\n      });\n\n      // First, we neutralize the feature (set it around coordinates [0,0]) to prevent rounding errors\n      // We take any point to translate all the points around 0\n      var centre = centroid(geojson, { properties: options.properties });\n      var translation = centre.geometry.coordinates;\n      var sx = 0;\n      var sy = 0;\n      var sArea = 0;\n      var i, pi, pj, xi, xj, yi, yj, a;\n\n      var neutralizedPoints = coords.map(function (point) {\n        return [point[0] - translation[0], point[1] - translation[1]];\n      });\n\n      for (i = 0; i < coords.length - 1; i++) {\n        // pi is the current point\n        pi = neutralizedPoints[i];\n        xi = pi[0];\n        yi = pi[1];\n\n        // pj is the next point (pi+1)\n        pj = neutralizedPoints[i + 1];\n        xj = pj[0];\n        yj = pj[1];\n\n        // a is the common factor to compute the signed area and the final coordinates\n        a = xi * yj - xj * yi;\n\n        // sArea is the sum used to compute the signed area\n        sArea += a;\n\n        // sx and sy are the sums used to compute the final coordinates\n        sx += (xi + xj) * a;\n        sy += (yi + yj) * a;\n      }\n\n      // Shape has no area: fallback on turf.centroid\n      if (sArea === 0) {\n        return centre;\n      } else {\n        // Compute the signed area, and factorize 1/6A\n        var area = sArea * 0.5;\n        var areaFactor = 1 / (6 * area);\n\n        // Compute the final coordinates, adding back the values that have been neutralized\n        return point(\n          [translation[0] + areaFactor * sx, translation[1] + areaFactor * sy],\n          options.properties\n        );\n      }\n    default:\n      // Not a polygon: Compute the convex hull and work with that\n      var hull = convex(geojson);\n\n      if (hull) return centerOfMass(hull, { properties: options.properties });\n      // Hull is empty: fallback on the centroid\n      else return centroid(geojson, { properties: options.properties });\n  }\n}\n\nexport { centerOfMass };\nexport default centerOfMass;\n"]}