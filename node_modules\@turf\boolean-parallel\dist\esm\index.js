// index.ts
import { cleanCoords } from "@turf/clean-coords";
import { lineSegment } from "@turf/line-segment";
import { rhumbBearing } from "@turf/rhumb-bearing";
import { bearingToAzimuth } from "@turf/helpers";
function booleanParallel(line1, line2) {
  if (!line1) throw new Error("line1 is required");
  if (!line2) throw new Error("line2 is required");
  var type1 = getType(line1, "line1");
  if (type1 !== "LineString") throw new Error("line1 must be a LineString");
  var type2 = getType(line2, "line2");
  if (type2 !== "LineString") throw new Error("line2 must be a LineString");
  var segments1 = lineSegment(cleanCoords(line1)).features;
  var segments2 = lineSegment(cleanCoords(line2)).features;
  for (var i = 0; i < segments1.length; i++) {
    var segment1 = segments1[i].geometry.coordinates;
    if (!segments2[i]) break;
    var segment2 = segments2[i].geometry.coordinates;
    if (!isParallel(segment1, segment2)) return false;
  }
  return true;
}
function isParallel(segment1, segment2) {
  var slope1 = bearingToAzimuth(rhumbBearing(segment1[0], segment1[1]));
  var slope2 = bearingToAzimuth(rhumbBearing(segment2[0], segment2[1]));
  return slope1 === slope2 || (slope2 - slope1) % 180 === 0;
}
function getType(geojson, name) {
  if (geojson.geometry && geojson.geometry.type)
    return geojson.geometry.type;
  if (geojson.type) return geojson.type;
  throw new Error("Invalid GeoJSON object for " + name);
}
var turf_boolean_parallel_default = booleanParallel;
export {
  booleanParallel,
  turf_boolean_parallel_default as default
};
//# sourceMappingURL=index.js.map