{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  BBox,\n  Feature,\n  Geometry,\n  LineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { bbox as calcBbox } from \"@turf/bbox\";\nimport { booleanPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Boolean-within returns true if the first geometry is completely within the second geometry.\n * The interiors of both geometries must intersect and, the interior and boundary of the primary (geometry a)\n * must not intersect the exterior of the secondary (geometry b).\n * Boolean-within returns the exact opposite result of the `@turf/boolean-contains`.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n * var point = turf.point([1, 2]);\n *\n * turf.booleanWithin(point, line);\n * //=true\n */\nfunction booleanWithin(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  var geom1 = getGeom(feature1);\n  var geom2 = getGeom(feature2);\n  var type1 = geom1.type;\n  var type2 = geom2.type;\n\n  switch (type1) {\n    case \"Point\":\n      switch (type2) {\n        case \"MultiPoint\":\n          return isPointInMultiPoint(geom1, geom2);\n        case \"LineString\":\n          return booleanPointOnLine(geom1, geom2, { ignoreEndVertices: true });\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return booleanPointInPolygon(geom1, geom2, { ignoreBoundary: true });\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"MultiPoint\":\n          return isMultiPointInMultiPoint(geom1, geom2);\n        case \"LineString\":\n          return isMultiPointOnLine(geom1, geom2);\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return isMultiPointInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"LineString\":\n          return isLineOnLine(geom1, geom2);\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return isLineInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return isPolyInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction isPointInMultiPoint(point: Point, multiPoint: MultiPoint) {\n  var i;\n  var output = false;\n  for (i = 0; i < multiPoint.coordinates.length; i++) {\n    if (compareCoords(multiPoint.coordinates[i], point.coordinates)) {\n      output = true;\n      break;\n    }\n  }\n  return output;\n}\n\nfunction isMultiPointInMultiPoint(\n  multiPoint1: MultiPoint,\n  multiPoint2: MultiPoint\n) {\n  for (var i = 0; i < multiPoint1.coordinates.length; i++) {\n    var anyMatch = false;\n    for (var i2 = 0; i2 < multiPoint2.coordinates.length; i2++) {\n      if (\n        compareCoords(multiPoint1.coordinates[i], multiPoint2.coordinates[i2])\n      ) {\n        anyMatch = true;\n      }\n    }\n    if (!anyMatch) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isMultiPointOnLine(multiPoint: MultiPoint, lineString: LineString) {\n  var foundInsidePoint = false;\n\n  for (var i = 0; i < multiPoint.coordinates.length; i++) {\n    if (!booleanPointOnLine(multiPoint.coordinates[i], lineString)) {\n      return false;\n    }\n    if (!foundInsidePoint) {\n      foundInsidePoint = booleanPointOnLine(\n        multiPoint.coordinates[i],\n        lineString,\n        { ignoreEndVertices: true }\n      );\n    }\n  }\n  return foundInsidePoint;\n}\n\nfunction isMultiPointInPoly(multiPoint: MultiPoint, polygon: Polygon) {\n  var output = true;\n  var oneInside = false;\n  var isInside = false;\n  for (var i = 0; i < multiPoint.coordinates.length; i++) {\n    isInside = booleanPointInPolygon(multiPoint.coordinates[i], polygon);\n    if (!isInside) {\n      output = false;\n      break;\n    }\n    if (!oneInside) {\n      isInside = booleanPointInPolygon(multiPoint.coordinates[i], polygon, {\n        ignoreBoundary: true,\n      });\n    }\n  }\n  return output && isInside;\n}\n\nfunction isLineOnLine(lineString1: LineString, lineString2: LineString) {\n  for (var i = 0; i < lineString1.coordinates.length; i++) {\n    if (!booleanPointOnLine(lineString1.coordinates[i], lineString2)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isLineInPoly(linestring: LineString, polygon: Polygon) {\n  var polyBbox = calcBbox(polygon);\n  var lineBbox = calcBbox(linestring);\n  if (!doBBoxOverlap(polyBbox, lineBbox)) {\n    return false;\n  }\n  var foundInsidePoint = false;\n\n  for (var i = 0; i < linestring.coordinates.length; i++) {\n    if (!booleanPointInPolygon(linestring.coordinates[i], polygon)) {\n      return false;\n    }\n    if (!foundInsidePoint) {\n      foundInsidePoint = booleanPointInPolygon(\n        linestring.coordinates[i],\n        polygon,\n        { ignoreBoundary: true }\n      );\n    }\n    if (!foundInsidePoint && i < linestring.coordinates.length - 1) {\n      var midpoint = getMidpoint(\n        linestring.coordinates[i],\n        linestring.coordinates[i + 1]\n      );\n      foundInsidePoint = booleanPointInPolygon(midpoint, polygon, {\n        ignoreBoundary: true,\n      });\n    }\n  }\n  return foundInsidePoint;\n}\n\n/**\n * Is Polygon2 in Polygon1\n * Only takes into account outer rings\n *\n * @private\n * @param {Polygon} geometry1\n * @param {Polygon|MultiPolygon} geometry2\n * @returns {boolean} true/false\n */\nfunction isPolyInPoly(geometry1: Polygon, geometry2: Polygon | MultiPolygon) {\n  var poly1Bbox = calcBbox(geometry1);\n  var poly2Bbox = calcBbox(geometry2);\n  if (!doBBoxOverlap(poly2Bbox, poly1Bbox)) {\n    return false;\n  }\n  for (var i = 0; i < geometry1.coordinates[0].length; i++) {\n    if (!booleanPointInPolygon(geometry1.coordinates[0][i], geometry2)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction doBBoxOverlap(bbox1: BBox, bbox2: BBox) {\n  if (bbox1[0] > bbox2[0]) return false;\n  if (bbox1[2] < bbox2[2]) return false;\n  if (bbox1[1] > bbox2[1]) return false;\n  if (bbox1[3] < bbox2[3]) return false;\n  return true;\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true/false if coord pairs match\n */\nfunction compareCoords(pair1: number[], pair2: number[]) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\n/**\n * getMidpoint\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {Position} midpoint of pair1 and pair2\n */\nfunction getMidpoint(pair1: number[], pair2: number[]) {\n  return [(pair1[0] + pair2[0]) / 2, (pair1[1] + pair2[1]) / 2];\n}\n\nexport { booleanWithin };\nexport default booleanWithin;\n"], "mappings": ";AAUA,SAAS,QAAQ,gBAAgB;AACjC,SAAS,0BAA0B;AACnC,SAAS,6BAA6B;AACtC,SAAS,eAAe;AAmBxB,SAAS,cACP,UACA,UACS;AACT,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,QAAQ,MAAM;AAClB,MAAI,QAAQ,MAAM;AAElB,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,oBAAoB,OAAO,KAAK;AAAA,QACzC,KAAK;AACH,iBAAO,mBAAmB,OAAO,OAAO,EAAE,mBAAmB,KAAK,CAAC;AAAA,QACrE,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,sBAAsB,OAAO,OAAO,EAAE,gBAAgB,KAAK,CAAC;AAAA,QACrE;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,yBAAyB,OAAO,KAAK;AAAA,QAC9C,KAAK;AACH,iBAAO,mBAAmB,OAAO,KAAK;AAAA,QACxC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,mBAAmB,OAAO,KAAK;AAAA,QACxC;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,aAAa,OAAO,KAAK;AAAA,QAClC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,aAAa,OAAO,KAAK;AAAA,QAClC;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,aAAa,OAAO,KAAK;AAAA,QAClC;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF;AACE,YAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,EACnE;AACF;AAEA,SAAS,oBAAoB,OAAc,YAAwB;AACjE,MAAI;AACJ,MAAI,SAAS;AACb,OAAK,IAAI,GAAG,IAAI,WAAW,YAAY,QAAQ,KAAK;AAClD,QAAI,cAAc,WAAW,YAAY,CAAC,GAAG,MAAM,WAAW,GAAG;AAC/D,eAAS;AACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,yBACP,aACA,aACA;AACA,WAAS,IAAI,GAAG,IAAI,YAAY,YAAY,QAAQ,KAAK;AACvD,QAAI,WAAW;AACf,aAAS,KAAK,GAAG,KAAK,YAAY,YAAY,QAAQ,MAAM;AAC1D,UACE,cAAc,YAAY,YAAY,CAAC,GAAG,YAAY,YAAY,EAAE,CAAC,GACrE;AACA,mBAAW;AAAA,MACb;AAAA,IACF;AACA,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,YAAwB,YAAwB;AAC1E,MAAI,mBAAmB;AAEvB,WAAS,IAAI,GAAG,IAAI,WAAW,YAAY,QAAQ,KAAK;AACtD,QAAI,CAAC,mBAAmB,WAAW,YAAY,CAAC,GAAG,UAAU,GAAG;AAC9D,aAAO;AAAA,IACT;AACA,QAAI,CAAC,kBAAkB;AACrB,yBAAmB;AAAA,QACjB,WAAW,YAAY,CAAC;AAAA,QACxB;AAAA,QACA,EAAE,mBAAmB,KAAK;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,YAAwB,SAAkB;AACpE,MAAI,SAAS;AACb,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,WAAW,YAAY,QAAQ,KAAK;AACtD,eAAW,sBAAsB,WAAW,YAAY,CAAC,GAAG,OAAO;AACnE,QAAI,CAAC,UAAU;AACb,eAAS;AACT;AAAA,IACF;AACA,QAAI,CAAC,WAAW;AACd,iBAAW,sBAAsB,WAAW,YAAY,CAAC,GAAG,SAAS;AAAA,QACnE,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,UAAU;AACnB;AAEA,SAAS,aAAa,aAAyB,aAAyB;AACtE,WAAS,IAAI,GAAG,IAAI,YAAY,YAAY,QAAQ,KAAK;AACvD,QAAI,CAAC,mBAAmB,YAAY,YAAY,CAAC,GAAG,WAAW,GAAG;AAChE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,YAAwB,SAAkB;AAC9D,MAAI,WAAW,SAAS,OAAO;AAC/B,MAAI,WAAW,SAAS,UAAU;AAClC,MAAI,CAAC,cAAc,UAAU,QAAQ,GAAG;AACtC,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB;AAEvB,WAAS,IAAI,GAAG,IAAI,WAAW,YAAY,QAAQ,KAAK;AACtD,QAAI,CAAC,sBAAsB,WAAW,YAAY,CAAC,GAAG,OAAO,GAAG;AAC9D,aAAO;AAAA,IACT;AACA,QAAI,CAAC,kBAAkB;AACrB,yBAAmB;AAAA,QACjB,WAAW,YAAY,CAAC;AAAA,QACxB;AAAA,QACA,EAAE,gBAAgB,KAAK;AAAA,MACzB;AAAA,IACF;AACA,QAAI,CAAC,oBAAoB,IAAI,WAAW,YAAY,SAAS,GAAG;AAC9D,UAAI,WAAW;AAAA,QACb,WAAW,YAAY,CAAC;AAAA,QACxB,WAAW,YAAY,IAAI,CAAC;AAAA,MAC9B;AACA,yBAAmB,sBAAsB,UAAU,SAAS;AAAA,QAC1D,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AAWA,SAAS,aAAa,WAAoB,WAAmC;AAC3E,MAAI,YAAY,SAAS,SAAS;AAClC,MAAI,YAAY,SAAS,SAAS;AAClC,MAAI,CAAC,cAAc,WAAW,SAAS,GAAG;AACxC,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,YAAY,CAAC,EAAE,QAAQ,KAAK;AACxD,QAAI,CAAC,sBAAsB,UAAU,YAAY,CAAC,EAAE,CAAC,GAAG,SAAS,GAAG;AAClE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,OAAa,OAAa;AAC/C,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAG,QAAO;AAChC,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAG,QAAO;AAChC,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAG,QAAO;AAChC,MAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAG,QAAO;AAChC,SAAO;AACT;AAUA,SAAS,cAAc,OAAiB,OAAiB;AACvD,SAAO,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC;AACtD;AAUA,SAAS,YAAY,OAAiB,OAAiB;AACrD,SAAO,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC;AAC9D;AAGA,IAAO,8BAAQ;", "names": []}