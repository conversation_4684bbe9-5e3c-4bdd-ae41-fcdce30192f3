# @turf/difference

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## difference

Finds the difference between multiple [polygons][1] by clipping the subsequent polygon from the first.

### Parameters

*   `features` **[FeatureCollection][2]<([Polygon][1] | [MultiPolygon][3])>** input Polygon features

### Examples

```javascript
var polygon1 = turf.polygon([[
  [128, -26],
  [141, -26],
  [141, -21],
  [128, -21],
  [128, -26]
]], {
  "fill": "#F00",
  "fill-opacity": 0.1
});
var polygon2 = turf.polygon([[
  [126, -28],
  [140, -28],
  [140, -20],
  [126, -20],
  [126, -28]
]], {
  "fill": "#00F",
  "fill-opacity": 0.1
});

var difference = turf.difference(turf.featureCollection([polygon1, polygon2]));

//addToMap
var addToMap = [polygon1, polygon2, difference];
```

Returns **([Feature][4]<([Polygon][1] | [MultiPolygon][3])> | null)** a Polygon or MultiPolygon feature showing the area of `polygon1` excluding the area of `polygon2` (if empty returns `null`)

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/difference
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
