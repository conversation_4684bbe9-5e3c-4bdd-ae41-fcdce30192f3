/* 演示2个弹窗的情形 */
//模块：
mars3d.widget.bindClass(mars3d.widget.BaseWidget.extend({
    options: {
        //弹窗
        view: [{
            type: "window",
            url: "viewLeft.html",
            name: "left",
            windowOptions: { 
                "noTitle": true,
                "closeBtn": 0,
                "width": 200,
                "position": {
                    "top": 70,
                    "bottom": 0,
                    "left": 0
                }
            }
        }, {
            type: "window",
            url: "viewRight.html",
            name: "right",
            windowOptions: { 
                "noTitle": true,
                "closeBtn": 0,
                "width": 200,
                "position": {
                    "top": 70,
                    "bottom": 0,
                    "right": 0
                }
            }
        }],
    },
    //初始化[仅执行1次]
    create: function () {


    },
    viewWindowLeft: null,
    viewWindowRight: null,
    //每个窗口创建完成后调用
    winCreateOK: function (opt, result) {
        if (opt.name == "left") {
            this.viewWindowLeft = result;

            this.viewWindowLeft.testShowText('这是从地图主页面发送的交互请求1'); //test
        }
        else {
            this.viewWindowRight = result;

            this.viewWindowRight.testShowText('这是从地图主页面发送的交互请求2');//test
        }
    },
    //打开激活
    activate: function () {
        if (this.viewWindowLeft)
            this.viewWindowLeft.testShowText('这是从地图主页面发送的交互请求3');

        if (this.viewWindowRight)
            this.viewWindowRight.testShowText('这是从地图主页面发送的交互请求4');

    },
    //关闭释放
    disable: function () {


    },
    testCenterAt1: function () {
        this.viewer.mars.centerAt({ "y": 31.981816, "x": 118.782446, "z": 10607.4, "heading": 5.5, "pitch": -51.9, "roll": 0 });

    },
    testCenterAt2: function () {
        this.viewer.mars.centerAt({ "y": 31.686288, "x": 117.229619, "z": 11333.9, "heading": 359.2, "pitch": -39.5, "roll": 360 });

    },
 







}));

