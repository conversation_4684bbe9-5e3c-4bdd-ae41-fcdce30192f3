{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, Geometry, MultiPoint } from \"geojson\";\nimport { segmentEach } from \"@turf/meta\";\nimport { getGeom } from \"@turf/invariant\";\nimport { lineOverlap } from \"@turf/line-overlap\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { geojsonEquality } from \"geojson-equality-ts\";\n\n/**\n * Compares two geometries of the same dimension and returns true if their intersection set results in a geometry\n * different from both but of the same dimension. It applies to Polygon/Polygon, LineString/LineString,\n * Multipoint/Multipoint, MultiLineString/MultiLineString and MultiPolygon/MultiPolygon.\n *\n * In other words, it returns true if the two geometries overlap, provided that neither completely contains the other.\n *\n * @function\n * @param  {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} feature1 input\n * @param  {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} feature2 input\n * @returns {boolean} true/false\n * @example\n * var poly1 = turf.polygon([[[0,0],[0,5],[5,5],[5,0],[0,0]]]);\n * var poly2 = turf.polygon([[[1,1],[1,6],[6,6],[6,1],[1,1]]]);\n * var poly3 = turf.polygon([[[10,10],[10,15],[15,15],[15,10],[10,10]]]);\n *\n * turf.booleanOverlap(poly1, poly2)\n * //=true\n * turf.booleanOverlap(poly2, poly3)\n * //=false\n */\nfunction booleanOverlap(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  const geom1 = getGeom(feature1);\n  const geom2 = getGeom(feature2);\n  const type1 = geom1.type;\n  const type2 = geom2.type;\n\n  if (\n    (type1 === \"MultiPoint\" && type2 !== \"MultiPoint\") ||\n    ((type1 === \"LineString\" || type1 === \"MultiLineString\") &&\n      type2 !== \"LineString\" &&\n      type2 !== \"MultiLineString\") ||\n    ((type1 === \"Polygon\" || type1 === \"MultiPolygon\") &&\n      type2 !== \"Polygon\" &&\n      type2 !== \"MultiPolygon\")\n  ) {\n    throw new Error(\"features must be of the same type\");\n  }\n  if (type1 === \"Point\") throw new Error(\"Point geometry not supported\");\n\n  // features must be not equal\n  if (geojsonEquality(feature1 as any, feature2 as any, { precision: 6 }))\n    return false;\n\n  let overlap = 0;\n\n  switch (type1) {\n    case \"MultiPoint\":\n      for (var i = 0; i < (geom1 as MultiPoint).coordinates.length; i++) {\n        for (var j = 0; j < (geom2 as MultiPoint).coordinates.length; j++) {\n          var coord1 = geom1.coordinates[i];\n          var coord2 = geom2.coordinates[j];\n          if (coord1[0] === coord2[0] && coord1[1] === coord2[1]) {\n            return true;\n          }\n        }\n      }\n      return false;\n\n    case \"LineString\":\n    case \"MultiLineString\":\n      segmentEach(feature1, (segment1) => {\n        segmentEach(feature2, (segment2) => {\n          if (lineOverlap(segment1!, segment2!).features.length) overlap++;\n        });\n      });\n      break;\n\n    case \"Polygon\":\n    case \"MultiPolygon\":\n      segmentEach(feature1, (segment1) => {\n        segmentEach(feature2, (segment2) => {\n          if (lineIntersect(segment1!, segment2!).features.length) overlap++;\n        });\n      });\n      break;\n  }\n\n  return overlap > 0;\n}\n\nexport { booleanOverlap };\nexport default booleanOverlap;\n"], "mappings": ";AACA,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAuBhC,SAAS,eACP,UACA,UACS;AACT,QAAM,QAAQ,QAAQ,QAAQ;AAC9B,QAAM,QAAQ,QAAQ,QAAQ;AAC9B,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,MAAM;AAEpB,MACG,UAAU,gBAAgB,UAAU,iBACnC,UAAU,gBAAgB,UAAU,sBACpC,UAAU,gBACV,UAAU,sBACV,UAAU,aAAa,UAAU,mBACjC,UAAU,aACV,UAAU,gBACZ;AACA,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACrD;AACA,MAAI,UAAU,QAAS,OAAM,IAAI,MAAM,8BAA8B;AAGrE,MAAI,gBAAgB,UAAiB,UAAiB,EAAE,WAAW,EAAE,CAAC;AACpE,WAAO;AAET,MAAI,UAAU;AAEd,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,eAAS,IAAI,GAAG,IAAK,MAAqB,YAAY,QAAQ,KAAK;AACjE,iBAAS,IAAI,GAAG,IAAK,MAAqB,YAAY,QAAQ,KAAK;AACjE,cAAI,SAAS,MAAM,YAAY,CAAC;AAChC,cAAI,SAAS,MAAM,YAAY,CAAC;AAChC,cAAI,OAAO,CAAC,MAAM,OAAO,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG;AACtD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IAET,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,UAAU,CAAC,aAAa;AAClC,oBAAY,UAAU,CAAC,aAAa;AAClC,cAAI,YAAY,UAAW,QAAS,EAAE,SAAS,OAAQ;AAAA,QACzD,CAAC;AAAA,MACH,CAAC;AACD;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,UAAU,CAAC,aAAa;AAClC,oBAAY,UAAU,CAAC,aAAa;AAClC,cAAI,cAAc,UAAW,QAAS,EAAE,SAAS,OAAQ;AAAA,QAC3D,CAAC;AAAA,MACH,CAAC;AACD;AAAA,EACJ;AAEA,SAAO,UAAU;AACnB;AAGA,IAAO,+BAAQ;", "names": []}