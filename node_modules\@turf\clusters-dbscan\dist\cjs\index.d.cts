import { GeoJsonProperties, FeatureCollection, Point } from 'geojson';
import { Units } from '@turf/helpers';

/**
 * Point classification within the cluster.
 *
 * @typedef {"core" | "edge" | "noise"} Dbscan
 */
type Dbscan = "core" | "edge" | "noise";
/**
 * Properties assigned to each clustered point.
 *
 * @extends GeoJsonProperties
 * @typedef {object} DbscanProps
 * @property {Dbscan} [dbscan] type of point it has been classified as
 * @property {number} [cluster] associated clusterId
 */
type DbscanProps = GeoJsonProperties & {
    dbscan?: Dbscan;
    cluster?: number;
};
/**
 * Takes a set of {@link Point|points} and partition them into clusters according to {@link https://en.wikipedia.org/wiki/DBSCAN|DBSCAN's} data clustering algorithm.
 *
 * @function
 * @param {FeatureCollection<Point>} points to be clustered
 * @param {number} maxDistance Maximum Distance between any point of the cluster to generate the clusters (kilometers by default, see options)
 * @param {Object} [options={}] Optional parameters
 * @param {string} [options.units="kilometers"] in which `maxDistance` is expressed, can be degrees, radians, miles, or kilometers
 * @param {boolean} [options.mutate=false] Allows GeoJSON input to be mutated
 * @param {number} [options.minPoints=3] Minimum number of points to generate a single cluster,
 * points which do not meet this requirement will be classified as an 'edge' or 'noise'.
 * @returns {FeatureCollection<Point, DbscanProps>} Clustered Points with an additional two properties associated to each Feature:
 * - {number} cluster - the associated clusterId
 * - {string} dbscan - type of point it has been classified as ('core'|'edge'|'noise')
 * @example
 * // create random points with random z-values in their properties
 * var points = turf.randomPoint(100, {bbox: [0, 30, 20, 50]});
 * var maxDistance = 100;
 * var clustered = turf.clustersDbscan(points, maxDistance);
 *
 * //addToMap
 * var addToMap = [clustered];
 */
declare function clustersDbscan(points: FeatureCollection<Point>, maxDistance: number, options?: {
    units?: Units;
    minPoints?: number;
    mutate?: boolean;
}): FeatureCollection<Point, DbscanProps>;

export { type Dbscan, type DbscanProps, clustersDbscan, clustersDbscan as default };
