# @turf/geojson-rbush

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## rbush

### insert

[insert][1]

#### Parameters

*   `feature` **[Feature][2]** insert single GeoJSON Feature

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);
tree.insert(poly)
```

Returns **RBush** GeoJSON RBush

### load

[load][3]

#### Parameters

*   `features` **([FeatureCollection][4] | [Array][5]<[Feature][2]>)** load entire GeoJSON FeatureCollection

#### Examples

```javascript
var polys = turf.polygons([
    [[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]],
    [[[-93, 32], [-83, 32], [-83, 39], [-93, 39], [-93, 32]]]
]);
tree.load(polys);
```

Returns **RBush** GeoJSON RBush

### remove

[remove][6]

#### Parameters

*   `feature` **[Feature][2]** remove single GeoJSON Feature
*   `equals` **[Function][7]** Pass a custom equals function to compare by value for removal.

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);

tree.remove(poly);
```

Returns **RBush** GeoJSON RBush

### clear

[clear][6]

#### Examples

```javascript
tree.clear()
```

Returns **RBush** GeoJSON Rbush

### search

[search][8]

#### Parameters

*   `geojson` **([BBox][9] | [FeatureCollection][4] | [Feature][2])** search with GeoJSON

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);

tree.search(poly);
```

Returns **[FeatureCollection][4]** all features that intersects with the given GeoJSON.

### collides

[collides][10]

#### Parameters

*   `geojson` **([BBox][9] | [FeatureCollection][4] | [Feature][2])** collides with GeoJSON

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);

tree.collides(poly);
```

Returns **[boolean][11]** true if there are any items intersecting the given GeoJSON, otherwise false.

### all

[all][8]

#### Examples

```javascript
tree.all()
```

Returns **[FeatureCollection][4]** all the features in RBush

### toJSON

[toJSON][12]

#### Examples

```javascript
var exported = tree.toJSON()
```

Returns **any** export data as JSON object

### fromJSON

[fromJSON][12]

#### Parameters

*   `json` **any** import previously exported data

#### Examples

```javascript
var exported = {
  "children": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [110, 50]
      },
      "properties": {},
      "bbox": [110, 50, 110, 50]
    }
  ],
  "height": 1,
  "leaf": true,
  "minX": 110,
  "minY": 50,
  "maxX": 110,
  "maxY": 50
}
tree.fromJSON(exported)
```

Returns **RBush** GeoJSON RBush

## rbush

GeoJSON implementation of [RBush][13] spatial index.

### Parameters

*   `maxEntries` **[number][14]** defines the maximum number of entries in a tree node. 9 (used by default) is a
    reasonable choice for most applications. Higher value means faster insertion and slower search, and vice versa. (optional, default `9`)

### Examples

```javascript
var geojsonRbush = require('geojson-rbush').default;
var tree = geojsonRbush();
```

Returns **RBush** GeoJSON RBush

### insert

[insert][1]

#### Parameters

*   `feature` **[Feature][2]** insert single GeoJSON Feature

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);
tree.insert(poly)
```

Returns **RBush** GeoJSON RBush

### load

[load][3]

#### Parameters

*   `features` **([FeatureCollection][4] | [Array][5]<[Feature][2]>)** load entire GeoJSON FeatureCollection

#### Examples

```javascript
var polys = turf.polygons([
    [[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]],
    [[[-93, 32], [-83, 32], [-83, 39], [-93, 39], [-93, 32]]]
]);
tree.load(polys);
```

Returns **RBush** GeoJSON RBush

### remove

[remove][6]

#### Parameters

*   `feature` **[Feature][2]** remove single GeoJSON Feature
*   `equals` **[Function][7]** Pass a custom equals function to compare by value for removal.

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);

tree.remove(poly);
```

Returns **RBush** GeoJSON RBush

### clear

[clear][6]

#### Examples

```javascript
tree.clear()
```

Returns **RBush** GeoJSON Rbush

### search

[search][8]

#### Parameters

*   `geojson` **([BBox][9] | [FeatureCollection][4] | [Feature][2])** search with GeoJSON

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);

tree.search(poly);
```

Returns **[FeatureCollection][4]** all features that intersects with the given GeoJSON.

### collides

[collides][10]

#### Parameters

*   `geojson` **([BBox][9] | [FeatureCollection][4] | [Feature][2])** collides with GeoJSON

#### Examples

```javascript
var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);

tree.collides(poly);
```

Returns **[boolean][11]** true if there are any items intersecting the given GeoJSON, otherwise false.

### all

[all][8]

#### Examples

```javascript
tree.all()
```

Returns **[FeatureCollection][4]** all the features in RBush

### toJSON

[toJSON][12]

#### Examples

```javascript
var exported = tree.toJSON()
```

Returns **any** export data as JSON object

### fromJSON

[fromJSON][12]

#### Parameters

*   `json` **any** import previously exported data

#### Examples

```javascript
var exported = {
  "children": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [110, 50]
      },
      "properties": {},
      "bbox": [110, 50, 110, 50]
    }
  ],
  "height": 1,
  "leaf": true,
  "minX": 110,
  "minY": 50,
  "maxX": 110,
  "maxY": 50
}
tree.fromJSON(exported)
```

Returns **RBush** GeoJSON RBush

[1]: https://github.com/mourner/rbush#data-format

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://github.com/mourner/rbush#bulk-inserting-data

[4]: https://tools.ietf.org/html/rfc7946#section-3.3

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[6]: https://github.com/mourner/rbush#removing-data

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function

[8]: https://github.com/mourner/rbush#search

[9]: https://tools.ietf.org/html/rfc7946#section-5

[10]: https://github.com/mourner/rbush#collisions

[11]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[12]: https://github.com/mourner/rbush#export-and-import

[13]: https://github.com/mourner/rbush#rbush

[14]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/geojson-rbush
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
