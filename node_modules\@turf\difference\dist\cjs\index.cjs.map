{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-difference/dist/cjs/index.cjs", "../../index.ts"], "names": ["difference"], "mappings": "AAAA;ACCA,8FAA0B;AAC1B,wCAAsC;AACtC,kCAAyB;AAmCzB,SAASA,WAAAA,CACP,QAAA,EACwC;AACxC,EAAA,MAAM,MAAA,EAA8B,CAAC,CAAA;AAErC,EAAA,4BAAA,QAAS,EAAU,CAAC,IAAA,EAAA,GAAS;AAC3B,IAAA,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,WAA4B,CAAA;AAAA,EAC9C,CAAC,CAAA;AAED,EAAA,GAAA,CAAI,KAAA,CAAM,OAAA,EAAS,CAAA,EAAG;AACpB,IAAA,MAAM,IAAI,KAAA,CAAM,iCAAiC,CAAA;AAAA,EACnD;AAEA,EAAA,MAAM,WAAA,EAAa,QAAA,CAAS,QAAA,CAAS,CAAC,CAAA,CAAE,WAAA,GAAc,CAAC,CAAA;AAEvD,EAAA,MAAM,YAAA,EAAuB,QAAA,CAAA,UAAA,CAAW,KAAA,CAAM,CAAC,CAAA,EAAG,GAAG,KAAA,CAAM,KAAA,CAAM,CAAC,CAAC,CAAA;AACnE,EAAA,GAAA,CAAI,WAAA,CAAY,OAAA,IAAW,CAAA,EAAG,OAAO,IAAA;AACrC,EAAA,GAAA,CAAI,WAAA,CAAY,OAAA,IAAW,CAAA,EAAG,OAAO,8BAAA,WAAQ,CAAY,CAAC,CAAA,EAAG,UAAU,CAAA;AACvE,EAAA,OAAO,mCAAA,WAAa,EAAa,UAAU,CAAA;AAC7C;AAGA,IAAO,wBAAA,EAAQA,WAAAA;ADzCf;AACE;AACA;AACF,4EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-difference/dist/cjs/index.cjs", "sourcesContent": [null, "import { Polygon, MultiPolygon, Feature, FeatureCollection } from \"geojson\";\nimport * as polyclip from \"polyclip-ts\";\nimport { polygon, multiPolygon } from \"@turf/helpers\";\nimport { geomEach } from \"@turf/meta\";\n\n/**\n * Finds the difference between multiple {@link Polygon|polygons} by clipping the subsequent polygon from the first.\n *\n * @function\n * @param {FeatureCollection<Polygon|MultiPolygon>} features input Polygon features\n * @returns {Feature<Polygon|MultiPolygon>|null} a Polygon or MultiPolygon feature showing the area of `polygon1` excluding the area of `polygon2` (if empty returns `null`)\n * @example\n * var polygon1 = turf.polygon([[\n *   [128, -26],\n *   [141, -26],\n *   [141, -21],\n *   [128, -21],\n *   [128, -26]\n * ]], {\n *   \"fill\": \"#F00\",\n *   \"fill-opacity\": 0.1\n * });\n * var polygon2 = turf.polygon([[\n *   [126, -28],\n *   [140, -28],\n *   [140, -20],\n *   [126, -20],\n *   [126, -28]\n * ]], {\n *   \"fill\": \"#00F\",\n *   \"fill-opacity\": 0.1\n * });\n *\n * var difference = turf.difference(turf.featureCollection([polygon1, polygon2]));\n *\n * //addToMap\n * var addToMap = [polygon1, polygon2, difference];\n */\nfunction difference(\n  features: FeatureCollection<Polygon | MultiPolygon>\n): Feature<Polygon | MultiPolygon> | null {\n  const geoms: Array<polyclip.Geom> = [];\n\n  geomEach(features, (geom) => {\n    geoms.push(geom.coordinates as polyclip.Geom);\n  });\n\n  if (geoms.length < 2) {\n    throw new Error(\"Must have at least two features\");\n  }\n\n  const properties = features.features[0].properties || {};\n\n  const differenced = polyclip.difference(geoms[0], ...geoms.slice(1));\n  if (differenced.length === 0) return null;\n  if (differenced.length === 1) return polygon(differenced[0], properties);\n  return multiPolygon(differenced, properties);\n}\n\nexport { difference };\nexport default difference;\n"]}