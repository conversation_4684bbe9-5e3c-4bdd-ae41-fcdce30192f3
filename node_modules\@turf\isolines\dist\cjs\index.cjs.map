{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-isolines/dist/cjs/index.cjs", "../../index.ts", "../../lib/grid-to-matrix.js"], "names": ["isObject", "collectionOf"], "mappings": "AAAA,6EAAI,UAAU,EAAE,MAAM,CAAC,cAAc;AACrC,IAAI,oBAAoB,EAAE,MAAM,CAAC,qBAAqB;AACtD,IAAI,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,cAAc;AAClD,IAAI,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,oBAAoB;AACxD,IAAI,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK;AAC/J,IAAI,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG;AAC/B,EAAE,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChC,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AAClC,MAAM,eAAe,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,EAAE,GAAG,CAAC,mBAAmB;AACzB,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,mBAAmB,CAAC,CAAC,CAAC,EAAE;AAC7C,MAAM,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AACpC,QAAQ,eAAe,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI;AACJ,EAAE,OAAO,CAAC;AACV,CAAC;AACD;AACA;ACjBA,kCAAqB;AACrB,kCAA0B;AAC1B,4CAA6B;AAC7B,wCAA6D;AAE7D,kDAA4B;ADkB5B;AACA;AExBA;AACA;AACA;AAkCA,SAAS,YAAA,CAAa,IAAA,EAAM,OAAA,EAAS;AAEnC,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,IAAI,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,WAAA;AACrC,EAAA,IAAI,KAAA,EAAO,OAAA,CAAQ,IAAA;AACnB,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,KAAA;AAGpB,EAAA,qCAAA,IAAa,EAAM,OAAA,EAAS,2BAA2B,CAAA;AAEvD,EAAA,IAAI,aAAA,EAAe,kBAAA,CAAmB,IAAA,EAAM,IAAI,CAAA;AAEhD,EAAA,IAAI,OAAA,EAAS,CAAC,CAAA;AAGd,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,YAAA,CAAa,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC5C,IAAA,IAAI,SAAA,EAAW,YAAA,CAAa,CAAC,CAAA;AAC7B,IAAA,IAAI,IAAA,EAAM,CAAC,CAAA;AACX,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxC,MAAA,IAAI,MAAA,EAAQ,QAAA,CAAS,CAAC,CAAA;AAEtB,MAAA,GAAA,CAAI,KAAA,CAAM,UAAA,CAAW,SAAS,CAAA,EAAG,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,SAAS,CAAC,CAAA;AAAA,MAAA,KAChE,GAAA,CAAI,IAAA,CAAK,CAAC,CAAA;AAEf,MAAA,GAAA,CAAI,MAAA,IAAU,IAAA,EAAM,KAAA,CAAM,UAAA,CAAW,eAAA,EAAiB,CAAC,CAAA,EAAG,CAAC,CAAA;AAAA,IAC7D;AACA,IAAA,MAAA,CAAO,IAAA,CAAK,GAAG,CAAA;AAAA,EACjB;AAEA,EAAA,OAAO,MAAA;AACT;AAUA,SAAS,kBAAA,CAAmB,MAAA,EAAQ,IAAA,EAAM;AACxC,EAAA,IAAI,iBAAA,EAAmB,CAAC,CAAA;AAGxB,EAAA,+BAAA,MAAY,EAAQ,QAAA,CAAU,KAAA,EAAO;AACnC,IAAA,IAAI,IAAA,EAAM,kCAAA,KAAe,CAAA,CAAE,CAAC,CAAA;AAC5B,IAAA,GAAA,CAAI,CAAC,gBAAA,CAAiB,GAAG,CAAA,EAAG,gBAAA,CAAiB,GAAG,EAAA,EAAI,CAAC,CAAA;AACrD,IAAA,gBAAA,CAAiB,GAAG,CAAA,CAAE,IAAA,CAAK,KAAK,CAAA;AAAA,EAClC,CAAC,CAAA;AAGD,EAAA,IAAI,sBAAA,EAAwB,MAAA,CAAO,IAAA,CAAK,gBAAgB,CAAA,CAAE,GAAA,CAAI,QAAA,CAAU,GAAA,EAAK;AAC3E,IAAA,IAAI,IAAA,EAAM,gBAAA,CAAiB,GAAG,CAAA;AAC9B,IAAA,IAAI,sBAAA,EAAwB,GAAA,CAAI,IAAA,CAAK,QAAA,CAAU,CAAA,EAAG,CAAA,EAAG;AACnD,MAAA,OAAO,kCAAA,CAAW,CAAA,CAAE,CAAC,EAAA,EAAI,kCAAA,CAAW,CAAA,CAAE,CAAC,CAAA;AAAA,IACzC,CAAC,CAAA;AACD,IAAA,OAAO,qBAAA;AAAA,EACT,CAAC,CAAA;AAGD,EAAA,IAAI,YAAA,EAAc,qBAAA,CAAsB,IAAA,CAAK,QAAA,CAAU,CAAA,EAAG,CAAA,EAAG;AAC3D,IAAA,GAAA,CAAI,IAAA,EAAM,OAAO,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,IAAA,KAClD,OAAO,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,kCAAA,CAAU,CAAE,CAAC,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,EACpD,CAAC,CAAA;AAED,EAAA,OAAO,WAAA;AACT;AFjCA;AACA;AC3BA,SAAS,QAAA,CACP,SAAA,EACA,MAAA,EACA,OAAA,EAKA;AAEA,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAACA,+BAAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,MAAM,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,WAAA;AACvC,EAAA,MAAM,iBAAA,EAAmB,OAAA,CAAQ,iBAAA,GAAoB,CAAC,CAAA;AACtD,EAAA,MAAM,iBAAA,EAAmB,OAAA,CAAQ,iBAAA,GAAoB,CAAC,CAAA;AAGtD,EAAAC,qCAAAA,SAAa,EAAW,OAAA,EAAS,2BAA2B,CAAA;AAC5D,EAAA,GAAA,CAAI,CAAC,MAAA,EAAQ,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACjD,EAAA,GAAA,CAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,yBAAyB,CAAA;AACrE,EAAA,GAAA,CAAI,CAACD,+BAAAA,gBAAyB,CAAA;AAC5B,IAAA,MAAM,IAAI,KAAA,CAAM,oCAAoC,CAAA;AACtD,EAAA,GAAA,CAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,gBAAgB,CAAA;AACjC,IAAA,MAAM,IAAI,KAAA,CAAM,mCAAmC,CAAA;AAGrD,EAAA,MAAM,OAAA,EAAS,YAAA,CAAa,SAAA,EAAW,EAAE,SAAA,EAAsB,IAAA,EAAM,KAAK,CAAC,CAAA;AAC3E,EAAA,MAAM,gBAAA,EAAkB,cAAA;AAAA,IACtB,MAAA;AAAA,IACA,MAAA;AAAA,IACA,SAAA;AAAA,IACA,gBAAA;AAAA,IACA;AAAA,EACF,CAAA;AACA,EAAA,MAAM,eAAA,EAAiB,eAAA,CAAgB,eAAA,EAAiB,MAAA,EAAQ,SAAS,CAAA;AAEzE,EAAA,OAAO,wCAAA,cAAgC,CAAA;AACzC;AAiBA,SAAS,cAAA,CACP,MAAA,EACA,MAAA,EACA,SAAA,EACA,gBAAA,EACA,gBAAA,EAC4B;AAC5B,EAAA,MAAM,QAAA,EAAU,CAAC,CAAA;AACjB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,IAAA,MAAM,UAAA,EAAY,CAAC,MAAA,CAAO,CAAC,CAAA;AAE3B,IAAA,MAAM,WAAA,EAAa,cAAA,CAAA,cAAA,CAAA,CAAA,CAAA,EAAK,gBAAA,CAAA,EAAqB,gBAAA,CAAiB,CAAC,CAAA,CAAA;AAC/D,IAAA,UAAA,CAAW,SAAS,EAAA,EAAI,SAAA;AAGxB,IAAA,MAAM,QAAA,EAAU,sCAAA;AAAA,MACd,0CAAA,MAAY,EAAQ,SAAA,EAAW,EAAE,UAAA,EAAY,KAAA,EAAO,OAAA,EAAS,KAAK,CAAC,CAAA;AAAA,MACnE;AAAA,IACF,CAAA;AAEA,IAAA,OAAA,CAAQ,IAAA,CAAK,OAAO,CAAA;AAAA,EACtB;AACA,EAAA,OAAO,OAAA;AACT;AAWA,SAAS,eAAA,CACP,eAAA,EACA,MAAA,EACA,MAAA,EACA;AAEA,EAAA,MAAM,SAAA,EAAW,wBAAA,MAAW,CAAA;AAC5B,EAAA,MAAM,cAAA,EAAgB,QAAA,CAAS,CAAC,EAAA,EAAI,QAAA,CAAS,CAAC,CAAA;AAC9C,EAAA,MAAM,eAAA,EAAiB,QAAA,CAAS,CAAC,EAAA,EAAI,QAAA,CAAS,CAAC,CAAA;AAG/C,EAAA,MAAM,GAAA,EAAK,QAAA,CAAS,CAAC,CAAA;AACrB,EAAA,MAAM,GAAA,EAAK,QAAA,CAAS,CAAC,CAAA;AAGrB,EAAA,MAAM,YAAA,EAAc,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA;AACvC,EAAA,MAAM,aAAA,EAAe,MAAA,CAAO,OAAA,EAAS,CAAA;AAGrC,EAAA,MAAM,OAAA,EAAS,cAAA,EAAgB,WAAA;AAC/B,EAAA,MAAM,OAAA,EAAS,eAAA,EAAiB,YAAA;AAEhC,EAAA,MAAM,OAAA,EAAS,CAAC,KAAA,EAAA,GAAoB;AAClC,IAAA,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,EAAA,EAAI,OAAA,EAAS,EAAA;AAC/B,IAAA,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,EAAA,EAAI,OAAA,EAAS,EAAA;AAAA,EACjC,CAAA;AAGA,EAAA,eAAA,CAAgB,OAAA,CAAQ,CAAC,OAAA,EAAA,GAAY;AACnC,IAAA,6BAAA,OAAU,EAAS,MAAM,CAAA;AAAA,EAC3B,CAAC,CAAA;AACD,EAAA,OAAO,eAAA;AACT;AAGA,IAAO,sBAAA,EAAQ,QAAA;ADrCf;AACE;AACA;AACF,qEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-isolines/dist/cjs/index.cjs", "sourcesContent": [null, "import { bbox } from \"@turf/bbox\";\nimport { coordEach } from \"@turf/meta\";\nimport { collectionOf } from \"@turf/invariant\";\nimport { multiLineString, featureCollection, isObject } from \"@turf/helpers\";\n// @ts-expect-error Legacy JS library with no types defined\nimport { isoContours } from \"marchingsquares\";\nimport { gridToMatrix } from \"./lib/grid-to-matrix.js\";\nimport {\n  FeatureCollection,\n  Point,\n  MultiLineString,\n  Feature,\n  GeoJsonProperties,\n} from \"geojson\";\n\n/**\n * Takes a grid {@link FeatureCollection} of {@link Point} features with z-values and an array of\n * value breaks and generates [isolines](https://en.wikipedia.org/wiki/Contour_line).\n *\n * @function\n * @param {FeatureCollection<Point>} pointGrid input points\n * @param {Array<number>} breaks values of `zProperty` where to draw isolines\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.zProperty='elevation'] the property name in `points` from which z-values will be pulled\n * @param {Object} [options.commonProperties={}] GeoJSON properties passed to ALL isolines\n * @param {Array<Object>} [options.breaksProperties=[]] GeoJSON properties passed, in order, to the correspondent isoline;\n * the breaks array will define the order in which the isolines are created\n * @returns {FeatureCollection<MultiLineString>} a FeatureCollection of {@link MultiLineString} features representing isolines\n * @example\n * // create a grid of points with random z-values in their properties\n * var extent = [0, 30, 20, 50];\n * var cellWidth = 100;\n * var pointGrid = turf.pointGrid(extent, cellWidth, {units: 'miles'});\n *\n * for (var i = 0; i < pointGrid.features.length; i++) {\n *     pointGrid.features[i].properties.temperature = Math.random() * 10;\n * }\n * var breaks = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n *\n * var lines = turf.isolines(pointGrid, breaks, {zProperty: 'temperature'});\n *\n * //addToMap\n * var addToMap = [lines];\n */\nfunction isolines(\n  pointGrid: FeatureCollection<Point>,\n  breaks: number[],\n  options?: {\n    zProperty?: string;\n    commonProperties?: GeoJsonProperties;\n    breaksProperties?: GeoJsonProperties[];\n  }\n) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const zProperty = options.zProperty || \"elevation\";\n  const commonProperties = options.commonProperties || {};\n  const breaksProperties = options.breaksProperties || [];\n\n  // Input validation\n  collectionOf(pointGrid, \"Point\", \"Input must contain Points\");\n  if (!breaks) throw new Error(\"breaks is required\");\n  if (!Array.isArray(breaks)) throw new Error(\"breaks must be an Array\");\n  if (!isObject(commonProperties))\n    throw new Error(\"commonProperties must be an Object\");\n  if (!Array.isArray(breaksProperties))\n    throw new Error(\"breaksProperties must be an Array\");\n\n  // Isoline methods\n  const matrix = gridToMatrix(pointGrid, { zProperty: zProperty, flip: true });\n  const createdIsoLines = createIsoLines(\n    matrix,\n    breaks,\n    zProperty,\n    commonProperties,\n    breaksProperties\n  );\n  const scaledIsolines = rescaleIsolines(createdIsoLines, matrix, pointGrid);\n\n  return featureCollection(scaledIsolines);\n}\n\n/**\n * Creates the isolines lines (featuresCollection of MultiLineString features) from the 2D data grid\n *\n * Marchingsquares process the grid data as a 3D representation of a function on a 2D plane, therefore it\n * assumes the points (x-y coordinates) are one 'unit' distance. The result of the isolines function needs to be\n * rescaled, with turfjs, to the original area and proportions on the map\n *\n * @private\n * @param {Array<Array<number>>} matrix Grid Data\n * @param {Array<number>} breaks BreakProps\n * @param {string} zProperty name of the z-values property\n * @param {Object} [commonProperties={}] GeoJSON properties passed to ALL isolines\n * @param {Object} [breaksProperties=[]] GeoJSON properties passed to the correspondent isoline\n * @returns {Array<MultiLineString>} isolines\n */\nfunction createIsoLines(\n  matrix: number[][],\n  breaks: number[],\n  zProperty: string,\n  commonProperties: GeoJsonProperties,\n  breaksProperties: GeoJsonProperties[]\n): Feature<MultiLineString>[] {\n  const results = [];\n  for (let i = 0; i < breaks.length; i++) {\n    const threshold = +breaks[i]; // make sure it's a number\n\n    const properties = { ...commonProperties, ...breaksProperties[i] };\n    properties[zProperty] = threshold;\n    // Pass options to marchingsquares lib to reproduce historical turf\n    // behaviour.\n    const isoline = multiLineString(\n      isoContours(matrix, threshold, { linearRing: false, noFrame: true }),\n      properties\n    );\n\n    results.push(isoline);\n  }\n  return results;\n}\n\n/**\n * Translates and scales isolines\n *\n * @private\n * @param {Array<MultiLineString>} createdIsoLines to be rescaled\n * @param {Array<Array<number>>} matrix Grid Data\n * @param {Object} points Points by Latitude\n * @returns {Array<MultiLineString>} isolines\n */\nfunction rescaleIsolines(\n  createdIsoLines: Feature<MultiLineString>[],\n  matrix: number[][],\n  points: FeatureCollection<Point>\n) {\n  // get dimensions (on the map) of the original grid\n  const gridBbox = bbox(points); // [ minX, minY, maxX, maxY ]\n  const originalWidth = gridBbox[2] - gridBbox[0];\n  const originalHeigth = gridBbox[3] - gridBbox[1];\n\n  // get origin, which is the first point of the last row on the rectangular data on the map\n  const x0 = gridBbox[0];\n  const y0 = gridBbox[1];\n\n  // get number of cells per side\n  const matrixWidth = matrix[0].length - 1;\n  const matrixHeight = matrix.length - 1;\n\n  // calculate the scaling factor between matrix and rectangular grid on the map\n  const scaleX = originalWidth / matrixWidth;\n  const scaleY = originalHeigth / matrixHeight;\n\n  const resize = (point: number[]) => {\n    point[0] = point[0] * scaleX + x0;\n    point[1] = point[1] * scaleY + y0;\n  };\n\n  // resize and shift each point/line of the createdIsoLines\n  createdIsoLines.forEach((isoline) => {\n    coordEach(isoline, resize);\n  });\n  return createdIsoLines;\n}\n\nexport { isolines };\nexport default isolines;\n", "import { getCoords, collectionOf } from \"@turf/invariant\";\nimport { featureEach } from \"@turf/meta\";\nimport { isObject } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Point} grid and returns a correspondent matrix {Array<Array<number>>}\n * of the 'property' values\n *\n * @name gridToMatrix\n * @param {FeatureCollection<Point>} grid of points\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.zProperty='elevation'] the property name in `points` from which z-values will be pulled\n * @param {boolean} [options.flip=false] returns the matrix upside-down\n * @param {boolean} [options.flags=false] flags, adding a `matrixPosition` array field ([row, column]) to its properties,\n * the grid points with coordinates on the matrix\n * @returns {Array<Array<number>>} matrix of property values\n * @example\n *   var extent = [-70.823364, -33.553984, -70.473175, -33.302986];\n *   var cellSize = 3;\n *   var grid = turf.pointGrid(extent, cellSize);\n *   // add a random property to each point between 0 and 60\n *   for (var i = 0; i < grid.features.length; i++) {\n *     grid.features[i].properties.elevation = (Math.random() * 60);\n *   }\n *   gridToMatrix(grid);\n *   //= [\n *     [ 1, 13, 10,  9, 10, 13, 18],\n *     [34,  8,  5,  4,  5,  8, 13],\n *     [10,  5,  2,  1,  2,  5,  4],\n *     [ 0,  4, 56, 19,  1,  4,  9],\n *     [10,  5,  2,  1,  2,  5, 10],\n *     [57,  8,  5,  4,  5,  0, 57],\n *     [ 3, 13, 10,  9,  5, 13, 18],\n *     [18, 13, 10,  9, 78, 13, 18]\n *   ]\n */\nfunction gridToMatrix(grid, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var zProperty = options.zProperty || \"elevation\";\n  var flip = options.flip;\n  var flags = options.flags;\n\n  // validation\n  collectionOf(grid, \"Point\", \"input must contain Points\");\n\n  var pointsMatrix = sortPointsByLatLng(grid, flip);\n\n  var matrix = [];\n  // create property matrix from sorted points\n  // looping order matters here\n  for (var r = 0; r < pointsMatrix.length; r++) {\n    var pointRow = pointsMatrix[r];\n    var row = [];\n    for (var c = 0; c < pointRow.length; c++) {\n      var point = pointRow[c];\n      // Check if zProperty exist\n      if (point.properties[zProperty]) row.push(point.properties[zProperty]);\n      else row.push(0);\n      // add flags\n      if (flags === true) point.properties.matrixPosition = [r, c];\n    }\n    matrix.push(row);\n  }\n\n  return matrix;\n}\n\n/**\n * Sorts points by latitude and longitude, creating a 2-dimensional array of points\n *\n * @private\n * @param {FeatureCollection<Point>} points GeoJSON Point features\n * @param {boolean} [flip=false] returns the matrix upside-down\n * @returns {Array<Array<Point>>} points ordered by latitude and longitude\n */\nfunction sortPointsByLatLng(points, flip) {\n  var pointsByLatitude = {};\n\n  // divide points by rows with the same latitude\n  featureEach(points, function (point) {\n    var lat = getCoords(point)[1];\n    if (!pointsByLatitude[lat]) pointsByLatitude[lat] = [];\n    pointsByLatitude[lat].push(point);\n  });\n\n  // sort points (with the same latitude) by longitude\n  var orderedRowsByLatitude = Object.keys(pointsByLatitude).map(function (lat) {\n    var row = pointsByLatitude[lat];\n    var rowOrderedByLongitude = row.sort(function (a, b) {\n      return getCoords(a)[0] - getCoords(b)[0];\n    });\n    return rowOrderedByLongitude;\n  });\n\n  // sort rows (of points with the same latitude) by latitude\n  var pointMatrix = orderedRowsByLatitude.sort(function (a, b) {\n    if (flip) return getCoords(a[0])[1] - getCoords(b[0])[1];\n    else return getCoords(b[0])[1] - getCoords(a[0])[1];\n  });\n\n  return pointMatrix;\n}\n\nexport { gridToMatrix };\nexport default gridToMatrix;\n"]}