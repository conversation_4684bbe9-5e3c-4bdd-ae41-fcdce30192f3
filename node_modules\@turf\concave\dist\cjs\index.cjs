"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _distance = require('@turf/distance');
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
var _tin = require('@turf/tin');

// lib/turf-dissolve.ts
var _clone = require('@turf/clone');

var _invariant = require('@turf/invariant');


// lib/turf-line-dissolve.ts




function lineDissolve(geojson, options = {}) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) {
    throw new Error("options is invalid");
  }
  const mutate = options.mutate;
  if (_invariant.getType.call(void 0, geojson) !== "FeatureCollection") {
    throw new Error("geojson must be a FeatureCollection");
  }
  if (!geojson.features.length) {
    throw new Error("geojson is empty");
  }
  if (mutate === false || mutate === void 0) {
    geojson = _clone.clone.call(void 0, geojson);
  }
  const result = [];
  const lastLine = _meta.lineReduce.call(void 0, 
    geojson,
    (previousLine, currentLine) => {
      const merged = mergeLineStrings(previousLine, currentLine);
      if (merged) {
        return merged;
      } else {
        result.push(previousLine);
        return currentLine;
      }
    }
  );
  if (lastLine) {
    result.push(lastLine);
  }
  if (!result.length) {
    return null;
  } else if (result.length === 1) {
    return result[0];
  } else {
    return _helpers.multiLineString.call(void 0, 
      result.map((line) => {
        return line.coordinates;
      })
    );
  }
}
function coordId(coord) {
  return coord[0].toString() + "," + coord[1].toString();
}
function mergeLineStrings(a, b) {
  const coords1 = a.geometry.coordinates;
  const coords2 = b.geometry.coordinates;
  const s1 = coordId(coords1[0]);
  const e1 = coordId(coords1[coords1.length - 1]);
  const s2 = coordId(coords2[0]);
  const e2 = coordId(coords2[coords2.length - 1]);
  let coords;
  if (s1 === e2) {
    coords = coords2.concat(coords1.slice(1));
  } else if (s2 === e1) {
    coords = coords1.concat(coords2.slice(1));
  } else if (s1 === s2) {
    coords = coords1.slice(1).reverse().concat(coords2);
  } else if (e1 === e2) {
    coords = coords1.concat(coords2.reverse().slice(1));
  } else {
    return null;
  }
  return _helpers.lineString.call(void 0, coords);
}

// lib/turf-polygon-dissolve.ts




var _topojsonclient = require('topojson-client');
var _topojsonserver = require('topojson-server');
function polygonDissolve(geojson, options = {}) {
  if (_invariant.getType.call(void 0, geojson) !== "FeatureCollection") {
    throw new Error("geojson must be a FeatureCollection");
  }
  if (!geojson.features.length) {
    throw new Error("geojson is empty");
  }
  if (options.mutate === false || options.mutate === void 0) {
    geojson = _clone.clone.call(void 0, geojson);
  }
  const geoms = [];
  _meta.flattenEach.call(void 0, geojson, (feature2) => {
    geoms.push(feature2.geometry);
  });
  const topo = _topojsonserver.topology.call(void 0, { geoms: _helpers.geometryCollection.call(void 0, geoms).geometry });
  const merged = _topojsonclient.merge.call(void 0, topo, topo.objects.geoms.geometries);
  return merged;
}

// lib/turf-dissolve.ts
function dissolve(geojson, options = {}) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) {
    throw new Error("options is invalid");
  }
  const mutate = options.mutate;
  if (_invariant.getType.call(void 0, geojson) !== "FeatureCollection") {
    throw new Error("geojson must be a FeatureCollection");
  }
  if (!geojson.features.length) {
    throw new Error("geojson is empty");
  }
  if (mutate === false || mutate === void 0) {
    geojson = _clone.clone.call(void 0, geojson);
  }
  const type = getHomogenousType(geojson);
  if (!type) {
    throw new Error("geojson must be homogenous");
  }
  const data = geojson;
  switch (type) {
    case "LineString":
      return lineDissolve(data, options);
    case "Polygon":
      return polygonDissolve(data, options);
    default:
      throw new Error(type + " is not supported");
  }
}
function getHomogenousType(geojson) {
  const types = {};
  _meta.flattenEach.call(void 0, geojson, (feature2) => {
    types[feature2.geometry.type] = true;
  });
  const keys = Object.keys(types);
  if (keys.length === 1) {
    return keys[0];
  }
  return null;
}

// index.ts
function concave(points, options = {}) {
  const maxEdge = options.maxEdge || Infinity;
  const cleaned = removeDuplicates(points);
  const tinPolys = _tin.tin.call(void 0, cleaned);
  tinPolys.features = tinPolys.features.filter((triangle) => {
    const pt1 = triangle.geometry.coordinates[0][0];
    const pt2 = triangle.geometry.coordinates[0][1];
    const pt3 = triangle.geometry.coordinates[0][2];
    const dist1 = _distance.distance.call(void 0, pt1, pt2, options);
    const dist2 = _distance.distance.call(void 0, pt2, pt3, options);
    const dist3 = _distance.distance.call(void 0, pt1, pt3, options);
    return dist1 <= maxEdge && dist2 <= maxEdge && dist3 <= maxEdge;
  });
  if (tinPolys.features.length < 1) {
    return null;
  }
  const dissolved = dissolve(tinPolys);
  if (dissolved.coordinates.length === 1) {
    dissolved.coordinates = dissolved.coordinates[0];
    dissolved.type = "Polygon";
  }
  return _helpers.feature.call(void 0, dissolved);
}
function removeDuplicates(points) {
  const cleaned = [];
  const existing = {};
  _meta.featureEach.call(void 0, points, (pt) => {
    if (!pt.geometry) {
      return;
    }
    const key = pt.geometry.coordinates.join("-");
    if (!Object.prototype.hasOwnProperty.call(existing, key)) {
      cleaned.push(pt);
      existing[key] = true;
    }
  });
  return _helpers.featureCollection.call(void 0, cleaned);
}
var turf_concave_default = concave;



exports.concave = concave; exports.default = turf_concave_default;
//# sourceMappingURL=index.cjs.map