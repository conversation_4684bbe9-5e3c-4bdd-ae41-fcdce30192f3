{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-bezier-spline/dist/cjs/index.cjs", "../../index.ts", "../../lib/spline.ts"], "names": [], "mappings": "AAAA;ACCA,wCAA2B;AAC3B,4CAAwB;ADCxB;AACA;AE4BA,IAAM,OAAA,EAAN,MAAa;AAAA,EAWX,WAAA,CAAY,OAAA,EAAe;AACzB,IAAA,IAAA,CAAK,OAAA,EAAS,OAAA,CAAQ,OAAA,GAAU,CAAC,CAAA;AACjC,IAAA,IAAA,CAAK,SAAA,EAAW,OAAA,CAAQ,SAAA,GAAY,GAAA;AACpC,IAAA,IAAA,CAAK,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,IAAA;AACtC,IAAA,IAAA,CAAK,QAAA,EAAU,CAAC,CAAA;AAChB,IAAA,IAAA,CAAK,SAAA,EAAW,CAAC,CAAA;AACjB,IAAA,IAAA,CAAK,WAAA,EAAa,OAAA,CAAQ,WAAA,GAAc,EAAA;AACxC,IAAA,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,MAAA,CAAO,MAAA;AAC1B,IAAA,IAAA,CAAK,MAAA,EAAQ,CAAA;AAGb,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpC,MAAA,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA,CAAE,EAAA,EAAI,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA,CAAE,EAAA,GAAK,CAAA;AAAA,IACzC;AACA,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AACxC,MAAA,MAAM,GAAA,EAAK,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA;AACxB,MAAA,MAAM,GAAA,EAAK,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA;AAC5B,MAAA,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;AAAA,QAChB,CAAA,EAAA,CAAI,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAK,CAAA;AAAA,QACnB,CAAA,EAAA,CAAI,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAK,CAAA;AAAA,QACnB,CAAA,EAAA,CAAI,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAK;AAAA,MACrB,CAAC,CAAA;AAAA,IACH;AACA,IAAA,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,CAAC,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA,EAAG,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAC,CAAA;AACnD,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AAChD,MAAA,MAAM,GAAA,EACJ,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,EAAA,EAAI,IAAA,CAAK,OAAA,CAAQ,EAAA,EAAI,CAAC,CAAA,CAAE,CAAA,EAAA,EAAK,CAAA;AACvE,MAAA,MAAM,GAAA,EACJ,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,EAAA,EAAI,IAAA,CAAK,OAAA,CAAQ,EAAA,EAAI,CAAC,CAAA,CAAE,CAAA,EAAA,EAAK,CAAA;AACvE,MAAA,MAAM,GAAA,EACJ,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,EAAA,EAAI,IAAA,CAAK,OAAA,CAAQ,EAAA,EAAI,CAAC,CAAA,CAAE,CAAA,EAAA,EAAK,CAAA;AACvE,MAAA,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK;AAAA,QACjB;AAAA,UACE,CAAA,EAAA,CACG,EAAA,EAAM,IAAA,CAAK,SAAA,EAAA,EAAa,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAC5C,IAAA,CAAK,UAAA,EAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,EAAA,EAAI,EAAA,CAAA;AAAA,UACxC,CAAA,EAAA,CACG,EAAA,EAAM,IAAA,CAAK,SAAA,EAAA,EAAa,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAC5C,IAAA,CAAK,UAAA,EAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,EAAA,EAAI,EAAA,CAAA;AAAA,UACxC,CAAA,EAAA,CACG,EAAA,EAAM,IAAA,CAAK,SAAA,EAAA,EAAa,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAC5C,IAAA,CAAK,UAAA,EAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,EAAA,EAAI,EAAA;AAAA,QAC1C,CAAA;AAAA,QACA;AAAA,UACE,CAAA,EAAA,CACG,EAAA,EAAM,IAAA,CAAK,SAAA,EAAA,EAAa,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAC5C,IAAA,CAAK,UAAA,EAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAAI,EAAA,CAAA;AAAA,UAC5C,CAAA,EAAA,CACG,EAAA,EAAM,IAAA,CAAK,SAAA,EAAA,EAAa,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAC5C,IAAA,CAAK,UAAA,EAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAAI,EAAA,CAAA;AAAA,UAC5C,CAAA,EAAA,CACG,EAAA,EAAM,IAAA,CAAK,SAAA,EAAA,EAAa,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAC5C,IAAA,CAAK,UAAA,EAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,EAAA,EAAI,CAAC,CAAA,CAAE,EAAA,EAAI,EAAA;AAAA,QAC9C;AAAA,MACF,CAAC,CAAA;AAAA,IACH;AACA,IAAA,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK;AAAA,MACjB,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA;AAAA,MAC3B,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,OAAA,EAAS,CAAC;AAAA,IAC7B,CAAC,CAAA;AACD,IAAA,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,UAAU,CAAA;AAC5C,IAAA,OAAO,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIO,UAAA,CAAW,OAAA,EAAiB;AACjC,IAAA,MAAM,MAAA,EAAQ,CAAC,CAAA;AACf,IAAA,IAAI,SAAA,EAAW,IAAA,CAAK,GAAA,CAAI,CAAC,CAAA;AACzB,IAAA,KAAA,CAAM,IAAA,CAAK,CAAC,CAAA;AACZ,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,QAAA,EAAU,EAAA,GAAK,EAAA,EAAI;AAC1C,MAAA,MAAM,KAAA,EAAO,IAAA,CAAK,GAAA,CAAI,CAAC,CAAA;AACvB,MAAA,MAAM,KAAA,EAAO,IAAA,CAAK,IAAA;AAAA,QAAA,CACf,IAAA,CAAK,EAAA,EAAI,QAAA,CAAS,CAAA,EAAA,EAAA,CAAM,IAAA,CAAK,EAAA,EAAI,QAAA,CAAS,CAAA,EAAA,EAAA,CACxC,IAAA,CAAK,EAAA,EAAI,QAAA,CAAS,CAAA,EAAA,EAAA,CAAM,IAAA,CAAK,EAAA,EAAI,QAAA,CAAS,CAAA,EAAA,EAAA,CAC1C,IAAA,CAAK,EAAA,EAAI,QAAA,CAAS,CAAA,EAAA,EAAA,CAAM,IAAA,CAAK,EAAA,EAAI,QAAA,CAAS,CAAA;AAAA,MAC/C,CAAA;AACA,MAAA,GAAA,CAAI,KAAA,EAAO,OAAA,EAAS;AAClB,QAAA,KAAA,CAAM,IAAA,CAAK,CAAC,CAAA;AACZ,QAAA,SAAA,EAAW,IAAA;AAAA,MACb;AAAA,IACF;AACA,IAAA,OAAO,KAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKO,MAAA,CAAO,CAAA,EAAW;AACvB,IAAA,MAAM,GAAA,EAAK,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,EAAE,CAAA;AAC1B,IAAA,MAAM,GAAA,EAAK,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,EAAE,CAAA;AAC1B,IAAA,OAAO;AAAA,MACL,KAAA,EAAQ,IAAA,EAAM,IAAA,CAAK,KAAA,CAAM,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAG,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAC,EAAA,EAAK,IAAA;AAAA,MACtD,KAAA,EAAO,IAAA,CAAK,IAAA;AAAA,QAAA,CACT,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAA,CAAM,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAA,CACxB,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAA,CAAM,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAA,CAC1B,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA,EAAA,EAAA,CAAM,EAAA,CAAG,EAAA,EAAI,EAAA,CAAG,CAAA;AAAA,MAC/B;AAAA,IACF,CAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,GAAA,CAAI,IAAA,EAAc;AACvB,IAAA,IAAI,EAAA,EAAI,KAAA,EAAO,IAAA,CAAK,KAAA;AACpB,IAAA,GAAA,CAAI,EAAA,EAAI,CAAA,EAAG;AACT,MAAA,EAAA,EAAI,CAAA;AAAA,IACN;AACA,IAAA,GAAA,CAAI,EAAA,EAAI,IAAA,CAAK,QAAA,EAAU;AACrB,MAAA,EAAA,EAAI,IAAA,CAAK,SAAA,EAAW,CAAA;AAAA,IACtB;AAEA,IAAA,MAAM,GAAA,EAAK,EAAA,EAAI,IAAA,CAAK,QAAA;AACpB,IAAA,GAAA,CAAI,GAAA,GAAM,CAAA,EAAG;AACX,MAAA,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA;AAAA,IACpC;AAEA,IAAA,MAAM,EAAA,EAAI,IAAA,CAAK,KAAA,CAAA,CAAO,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS,CAAA,EAAA,EAAK,EAAE,CAAA;AAClD,IAAA,MAAM,GAAA,EAAA,CAAM,IAAA,CAAK,OAAA,EAAS,CAAA,EAAA,EAAK,GAAA,EAAK,CAAA;AACpC,IAAA,OAAO,MAAA;AAAA,MACL,EAAA;AAAA,MACA,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA;AAAA,MACb,IAAA,CAAK,QAAA,CAAS,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,MAClB,IAAA,CAAK,QAAA,CAAS,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,MACtB,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,CAAC;AAAA,IACnB,CAAA;AAAA,EACF;AACF,CAAA;AAEA,SAAS,MAAA,CAAO,CAAA,EAAW,EAAA,EAAW,EAAA,EAAW,EAAA,EAAW,EAAA,EAAW;AACrE,EAAA,MAAM,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA;AACb,EAAA,MAAM,IAAA,EAAM;AAAA,IACV,CAAA,EAAG,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA;AAAA,IACvD,CAAA,EAAG,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA;AAAA,IACvD,CAAA,EAAG,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC,EAAA,EAAI,EAAA,CAAG,EAAA,EAAI,CAAA,CAAE,CAAC;AAAA,EACzD,CAAA;AACA,EAAA,OAAO,GAAA;AACT;AACA,SAAS,CAAA,CAAE,CAAA,EAAW;AACpB,EAAA,MAAM,GAAA,EAAK,EAAA,EAAI,CAAA;AACf,EAAA,MAAM,GAAA,EAAK,GAAA,EAAK,CAAA;AAChB,EAAA,OAAO;AAAA,IACL,EAAA;AAAA,IACA,EAAA,EAAI,GAAA,EAAA,CAAM,EAAA,EAAI,CAAA,CAAA;AAAA,IACd,EAAA,EAAI,EAAA,EAAA,CAAK,EAAA,EAAI,CAAA,EAAA,EAAA,CAAM,EAAA,EAAI,CAAA,CAAA;AAAA,IAAA,CACtB,EAAA,EAAI,CAAA,EAAA,EAAA,CAAM,EAAA,EAAI,CAAA,EAAA,EAAA,CAAM,EAAA,EAAI,CAAA;AAAA,EAC3B,CAAA;AACF;AF9DA;AACA;ACnGA,SAAS,YAAA,CACP,IAAA,EACA,QAAA,EAII,CAAC,CAAA,EACmB;AAExB,EAAA,MAAM,WAAA,EAAa,OAAA,CAAQ,WAAA,GAAc,GAAA;AACzC,EAAA,MAAM,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,IAAA;AAEvC,EAAA,MAAM,OAAA,EAA6B,CAAC,CAAA;AACpC,EAAA,MAAM,OAAA,EAAS,gCAAA,IAAY,CAAA,CAAE,WAAA,CAAY,GAAA,CAAI,CAAC,EAAA,EAAA,GAAO;AACnD,IAAA,OAAO,EAAE,CAAA,EAAG,EAAA,CAAG,CAAC,CAAA,EAAG,CAAA,EAAG,EAAA,CAAG,CAAC,EAAE,CAAA;AAAA,EAC9B,CAAC,CAAA;AACD,EAAA,MAAM,OAAA,EAAS,IAAI,MAAA,CAAO;AAAA,IACxB,QAAA,EAAU,UAAA;AAAA,IACV,MAAA;AAAA,IACA;AAAA,EACF,CAAC,CAAA;AAED,EAAA,MAAM,UAAA,EAAY,CAAC,IAAA,EAAA,GAAiB;AAClC,IAAA,IAAI,IAAA,EAAM,MAAA,CAAO,GAAA,CAAI,IAAI,CAAA;AACzB,IAAA,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,KAAA,EAAO,GAAG,EAAA,EAAI,EAAA,IAAM,CAAA,EAAG;AACpC,MAAA,MAAA,CAAO,IAAA,CAAK,CAAC,GAAA,CAAI,CAAA,EAAG,GAAA,CAAI,CAAC,CAAC,CAAA;AAAA,IAC5B;AAAA,EACF,CAAA;AAEA,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,QAAA,EAAU,EAAA,GAAK,EAAA,EAAI;AAC5C,IAAA,SAAA,CAAU,CAAC,CAAA;AAAA,EACb;AACA,EAAA,SAAA,CAAU,MAAA,CAAO,QAAQ,CAAA;AAEzB,EAAA,OAAO,iCAAA,MAAW,EAAQ,OAAA,CAAQ,UAAU,CAAA;AAC9C;AAGA,IAAO,2BAAA,EAAQ,YAAA;ADuFf;AACE;AACA;AACF,kFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-bezier-spline/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, LineString, GeoJsonProperties } from \"geojson\";\nimport { lineString } from \"@turf/helpers\";\nimport { getGeom } from \"@turf/invariant\";\nimport { Spline } from \"./lib/spline.js\";\n\n/**\n * Takes a {@link LineString|line} and returns a curved version\n * by applying a [Bezier spline](http://en.wikipedia.org/wiki/B%C3%A9zier_spline)\n * algorithm.\n *\n * The bezier spline implementation is by [<PERSON><PERSON><PERSON>](http://leszek.rybicki.cc/).\n *\n * @function\n * @param {Feature<LineString>} line input LineString\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] Translate properties to output\n * @param {number} [options.resolution=10000] time in milliseconds between points\n * @param {number} [options.sharpness=0.85] a measure of how curvy the path should be between splines\n * @returns {Feature<LineString>} curved line\n * @example\n * var line = turf.lineString([\n *   [-76.091308, 18.427501],\n *   [-76.695556, 18.729501],\n *   [-76.552734, 19.40443],\n *   [-74.61914, 19.134789],\n *   [-73.652343, 20.07657],\n *   [-73.157958, 20.210656]\n * ]);\n *\n * var curved = turf.bezierSpline(line);\n *\n * //addToMap\n * var addToMap = [line, curved]\n * curved.properties = { stroke: '#0F0' };\n */\nfunction bezierSpline<P extends GeoJsonProperties = GeoJsonProperties>(\n  line: Feature<LineString> | LineString,\n  options: {\n    properties?: P;\n    resolution?: number;\n    sharpness?: number;\n  } = {}\n): Feature<LineString, P> {\n  // Optional params\n  const resolution = options.resolution || 10000;\n  const sharpness = options.sharpness || 0.85;\n\n  const coords: [number, number][] = [];\n  const points = getGeom(line).coordinates.map((pt) => {\n    return { x: pt[0], y: pt[1] };\n  });\n  const spline = new Spline({\n    duration: resolution,\n    points,\n    sharpness,\n  });\n\n  const pushCoord = (time: number) => {\n    var pos = spline.pos(time);\n    if (Math.floor(time / 100) % 2 === 0) {\n      coords.push([pos.x, pos.y]);\n    }\n  };\n\n  for (var i = 0; i < spline.duration; i += 10) {\n    pushCoord(i);\n  }\n  pushCoord(spline.duration);\n\n  return lineString(coords, options.properties);\n}\n\nexport { bezierSpline };\nexport default bezierSpline;\n", "interface Point {\n  x: number;\n  y: number;\n  z: number;\n}\n\n/**\n * BezierSpline\n * https://github.com/leszekr/bezier-spline-js\n *\n * @private\n * @copyright\n * Copyright (c) 2013 <PERSON><PERSON><PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nclass Spline {\n  public duration: number;\n  public points: Point[];\n  public sharpness: number;\n  public centers: Point[];\n  public controls: Array<[Point, Point]>;\n  public stepLength: number;\n  public length: number;\n  public delay: number;\n  public steps: number[];\n\n  constructor(options?: any) {\n    this.points = options.points || [];\n    this.duration = options.duration || 10000;\n    this.sharpness = options.sharpness || 0.85;\n    this.centers = [];\n    this.controls = [];\n    this.stepLength = options.stepLength || 60;\n    this.length = this.points.length;\n    this.delay = 0;\n\n    // this is to ensure compatibility with the 2d version\n    for (let i = 0; i < this.length; i++) {\n      this.points[i].z = this.points[i].z || 0;\n    }\n    for (let i = 0; i < this.length - 1; i++) {\n      const p1 = this.points[i];\n      const p2 = this.points[i + 1];\n      this.centers.push({\n        x: (p1.x + p2.x) / 2,\n        y: (p1.y + p2.y) / 2,\n        z: (p1.z + p2.z) / 2,\n      });\n    }\n    this.controls.push([this.points[0], this.points[0]]);\n    for (let i = 0; i < this.centers.length - 1; i++) {\n      const dx =\n        this.points[i + 1].x - (this.centers[i].x + this.centers[i + 1].x) / 2;\n      const dy =\n        this.points[i + 1].y - (this.centers[i].y + this.centers[i + 1].y) / 2;\n      const dz =\n        this.points[i + 1].z - (this.centers[i].y + this.centers[i + 1].z) / 2;\n      this.controls.push([\n        {\n          x:\n            (1.0 - this.sharpness) * this.points[i + 1].x +\n            this.sharpness * (this.centers[i].x + dx),\n          y:\n            (1.0 - this.sharpness) * this.points[i + 1].y +\n            this.sharpness * (this.centers[i].y + dy),\n          z:\n            (1.0 - this.sharpness) * this.points[i + 1].z +\n            this.sharpness * (this.centers[i].z + dz),\n        },\n        {\n          x:\n            (1.0 - this.sharpness) * this.points[i + 1].x +\n            this.sharpness * (this.centers[i + 1].x + dx),\n          y:\n            (1.0 - this.sharpness) * this.points[i + 1].y +\n            this.sharpness * (this.centers[i + 1].y + dy),\n          z:\n            (1.0 - this.sharpness) * this.points[i + 1].z +\n            this.sharpness * (this.centers[i + 1].z + dz),\n        },\n      ]);\n    }\n    this.controls.push([\n      this.points[this.length - 1],\n      this.points[this.length - 1],\n    ]);\n    this.steps = this.cacheSteps(this.stepLength);\n    return this;\n  }\n  /**\n   * Caches an array of equidistant (more or less) points on the curve.\n   */\n  public cacheSteps(mindist: number) {\n    const steps = [];\n    let laststep = this.pos(0);\n    steps.push(0);\n    for (let t = 0; t < this.duration; t += 10) {\n      const step = this.pos(t);\n      const dist = Math.sqrt(\n        (step.x - laststep.x) * (step.x - laststep.x) +\n          (step.y - laststep.y) * (step.y - laststep.y) +\n          (step.z - laststep.z) * (step.z - laststep.z)\n      );\n      if (dist > mindist) {\n        steps.push(t);\n        laststep = step;\n      }\n    }\n    return steps;\n  }\n\n  /**\n   * returns angle and speed in the given point in the curve\n   */\n  public vector(t: number) {\n    const p1 = this.pos(t + 10);\n    const p2 = this.pos(t - 10);\n    return {\n      angle: (180 * Math.atan2(p1.y - p2.y, p1.x - p2.x)) / 3.14,\n      speed: Math.sqrt(\n        (p2.x - p1.x) * (p2.x - p1.x) +\n          (p2.y - p1.y) * (p2.y - p1.y) +\n          (p2.z - p1.z) * (p2.z - p1.z)\n      ),\n    };\n  }\n\n  /**\n   * Gets the position of the point, given time.\n   *\n   * WARNING: The speed is not constant. The time it takes between control points is constant.\n   *\n   * For constant speed, use Spline.steps[i];\n   */\n  public pos(time: number) {\n    let t = time - this.delay;\n    if (t < 0) {\n      t = 0;\n    }\n    if (t > this.duration) {\n      t = this.duration - 1;\n    }\n    // t = t-this.delay;\n    const t2 = t / this.duration;\n    if (t2 >= 1) {\n      return this.points[this.length - 1];\n    }\n\n    const n = Math.floor((this.points.length - 1) * t2);\n    const t1 = (this.length - 1) * t2 - n;\n    return bezier(\n      t1,\n      this.points[n],\n      this.controls[n][1],\n      this.controls[n + 1][0],\n      this.points[n + 1]\n    );\n  }\n}\n\nfunction bezier(t: number, p1: Point, c1: Point, c2: Point, p2: Point) {\n  const b = B(t);\n  const pos = {\n    x: p2.x * b[0] + c2.x * b[1] + c1.x * b[2] + p1.x * b[3],\n    y: p2.y * b[0] + c2.y * b[1] + c1.y * b[2] + p1.y * b[3],\n    z: p2.z * b[0] + c2.z * b[1] + c1.z * b[2] + p1.z * b[3],\n  };\n  return pos;\n}\nfunction B(t: number) {\n  const t2 = t * t;\n  const t3 = t2 * t;\n  return [\n    t3,\n    3 * t2 * (1 - t),\n    3 * t * (1 - t) * (1 - t),\n    (1 - t) * (1 - t) * (1 - t),\n  ];\n}\n\nexport { Spline, Point };\nexport default Spline;\n"]}