"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _destination = require('@turf/destination');
var _helpers = require('@turf/helpers');
function circle(center, radius, options = {}) {
  const steps = options.steps || 64;
  const properties = options.properties ? options.properties : !Array.isArray(center) && center.type === "Feature" && center.properties ? center.properties : {};
  const coordinates = [];
  for (let i = 0; i < steps; i++) {
    coordinates.push(
      _destination.destination.call(void 0, center, radius, i * -360 / steps, options).geometry.coordinates
    );
  }
  coordinates.push(coordinates[0]);
  return _helpers.polygon.call(void 0, [coordinates], properties);
}
var turf_circle_default = circle;



exports.circle = circle; exports.default = turf_circle_default;
//# sourceMappingURL=index.cjs.map