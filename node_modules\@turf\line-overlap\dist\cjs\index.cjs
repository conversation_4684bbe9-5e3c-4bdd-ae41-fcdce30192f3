"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// index.ts
var _geojsonrbush = require('@turf/geojson-rbush');
var _linesegment = require('@turf/line-segment');
var _nearestpointonline = require('@turf/nearest-point-on-line');
var _booleanpointonline = require('@turf/boolean-point-on-line');
var _invariant = require('@turf/invariant');
var _meta = require('@turf/meta');
var _helpers = require('@turf/helpers');
var _fastdeepequal = require('fast-deep-equal'); var _fastdeepequal2 = _interopRequireDefault(_fastdeepequal);
function lineOverlap(line1, line2, options = {}) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  var tolerance = options.tolerance || 0;
  var features = [];
  var tree = _geojsonrbush.geojsonRbush.call(void 0, );
  const line = _linesegment.lineSegment.call(void 0, line1);
  tree.load(line);
  var overlapSegment;
  let additionalSegments = [];
  _meta.segmentEach.call(void 0, line2, function(segment) {
    var doesOverlaps = false;
    if (!segment) {
      return;
    }
    _meta.featureEach.call(void 0, tree.search(segment), function(match) {
      if (doesOverlaps === false) {
        var coordsSegment = _invariant.getCoords.call(void 0, segment).sort();
        var coordsMatch = _invariant.getCoords.call(void 0, match).sort();
        if (_fastdeepequal2.default.call(void 0, coordsSegment, coordsMatch)) {
          doesOverlaps = true;
          if (overlapSegment) {
            overlapSegment = concatSegment(overlapSegment, segment) || overlapSegment;
          } else overlapSegment = segment;
        } else if (tolerance === 0 ? _booleanpointonline.booleanPointOnLine.call(void 0, coordsSegment[0], match) && _booleanpointonline.booleanPointOnLine.call(void 0, coordsSegment[1], match) : _nearestpointonline.nearestPointOnLine.call(void 0, match, coordsSegment[0]).properties.dist <= tolerance && _nearestpointonline.nearestPointOnLine.call(void 0, match, coordsSegment[1]).properties.dist <= tolerance) {
          doesOverlaps = true;
          if (overlapSegment) {
            overlapSegment = concatSegment(overlapSegment, segment) || overlapSegment;
          } else overlapSegment = segment;
        } else if (tolerance === 0 ? _booleanpointonline.booleanPointOnLine.call(void 0, coordsMatch[0], segment) && _booleanpointonline.booleanPointOnLine.call(void 0, coordsMatch[1], segment) : _nearestpointonline.nearestPointOnLine.call(void 0, segment, coordsMatch[0]).properties.dist <= tolerance && _nearestpointonline.nearestPointOnLine.call(void 0, segment, coordsMatch[1]).properties.dist <= tolerance) {
          if (overlapSegment) {
            const combinedSegment = concatSegment(overlapSegment, match);
            if (combinedSegment) {
              overlapSegment = combinedSegment;
            } else {
              additionalSegments.push(match);
            }
          } else overlapSegment = match;
        }
      }
    });
    if (doesOverlaps === false && overlapSegment) {
      features.push(overlapSegment);
      if (additionalSegments.length) {
        features = features.concat(additionalSegments);
        additionalSegments = [];
      }
      overlapSegment = void 0;
    }
  });
  if (overlapSegment) features.push(overlapSegment);
  return _helpers.featureCollection.call(void 0, features);
}
function concatSegment(line, segment) {
  var coords = _invariant.getCoords.call(void 0, segment);
  var lineCoords = _invariant.getCoords.call(void 0, line);
  var start = lineCoords[0];
  var end = lineCoords[lineCoords.length - 1];
  var geom = line.geometry.coordinates;
  if (_fastdeepequal2.default.call(void 0, coords[0], start)) geom.unshift(coords[1]);
  else if (_fastdeepequal2.default.call(void 0, coords[0], end)) geom.push(coords[1]);
  else if (_fastdeepequal2.default.call(void 0, coords[1], start)) geom.unshift(coords[0]);
  else if (_fastdeepequal2.default.call(void 0, coords[1], end)) geom.push(coords[0]);
  else return;
  return line;
}
var turf_line_overlap_default = lineOverlap;



exports.default = turf_line_overlap_default; exports.lineOverlap = lineOverlap;
//# sourceMappingURL=index.cjs.map