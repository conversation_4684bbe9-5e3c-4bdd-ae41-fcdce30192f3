import { Coord, Units } from '@turf/helpers';

/**
 * Calculates the distance between two {@link Coord|coordinates} in degrees, radians, miles, or kilometers.
 * This uses the [Haversine formula](http://en.wikipedia.org/wiki/Haversine_formula) to account for global curvature.
 *
 * @function
 * @param {Coord} from origin coordinate
 * @param {Coord} to destination coordinate
 * @param {Object} [options={}] Optional parameters
 * @param {string} [options.units='kilometers'] can be degrees, radians, miles, or kilometers
 * @returns {number} distance between the two coordinates
 * @example
 * var from = turf.point([-75.343, 39.984]);
 * var to = turf.point([-75.534, 39.123]);
 * var options = {units: 'miles'};
 *
 * var distance = turf.distance(from, to, options);
 *
 * //addToMap
 * var addToMap = [from, to];
 * from.properties.distance = distance;
 * to.properties.distance = distance;
 */
declare function distance(from: Coord, to: Coord, options?: {
    units?: Units;
}): number;

export { distance as default, distance };
