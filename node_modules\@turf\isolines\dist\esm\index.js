var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};

// index.ts
import { bbox } from "@turf/bbox";
import { coordEach } from "@turf/meta";
import { collectionOf as collectionOf2 } from "@turf/invariant";
import { multiLineString, featureCollection, isObject as isObject2 } from "@turf/helpers";
import { isoContours } from "marchingsquares";

// lib/grid-to-matrix.js
import { getCoords, collectionOf } from "@turf/invariant";
import { featureEach } from "@turf/meta";
import { isObject } from "@turf/helpers";
function gridToMatrix(grid, options) {
  options = options || {};
  if (!isObject(options)) throw new Error("options is invalid");
  var zProperty = options.zProperty || "elevation";
  var flip = options.flip;
  var flags = options.flags;
  collectionOf(grid, "Point", "input must contain Points");
  var pointsMatrix = sortPointsByLatLng(grid, flip);
  var matrix = [];
  for (var r = 0; r < pointsMatrix.length; r++) {
    var pointRow = pointsMatrix[r];
    var row = [];
    for (var c = 0; c < pointRow.length; c++) {
      var point = pointRow[c];
      if (point.properties[zProperty]) row.push(point.properties[zProperty]);
      else row.push(0);
      if (flags === true) point.properties.matrixPosition = [r, c];
    }
    matrix.push(row);
  }
  return matrix;
}
function sortPointsByLatLng(points, flip) {
  var pointsByLatitude = {};
  featureEach(points, function(point) {
    var lat = getCoords(point)[1];
    if (!pointsByLatitude[lat]) pointsByLatitude[lat] = [];
    pointsByLatitude[lat].push(point);
  });
  var orderedRowsByLatitude = Object.keys(pointsByLatitude).map(function(lat) {
    var row = pointsByLatitude[lat];
    var rowOrderedByLongitude = row.sort(function(a, b) {
      return getCoords(a)[0] - getCoords(b)[0];
    });
    return rowOrderedByLongitude;
  });
  var pointMatrix = orderedRowsByLatitude.sort(function(a, b) {
    if (flip) return getCoords(a[0])[1] - getCoords(b[0])[1];
    else return getCoords(b[0])[1] - getCoords(a[0])[1];
  });
  return pointMatrix;
}

// index.ts
function isolines(pointGrid, breaks, options) {
  options = options || {};
  if (!isObject2(options)) throw new Error("options is invalid");
  const zProperty = options.zProperty || "elevation";
  const commonProperties = options.commonProperties || {};
  const breaksProperties = options.breaksProperties || [];
  collectionOf2(pointGrid, "Point", "Input must contain Points");
  if (!breaks) throw new Error("breaks is required");
  if (!Array.isArray(breaks)) throw new Error("breaks must be an Array");
  if (!isObject2(commonProperties))
    throw new Error("commonProperties must be an Object");
  if (!Array.isArray(breaksProperties))
    throw new Error("breaksProperties must be an Array");
  const matrix = gridToMatrix(pointGrid, { zProperty, flip: true });
  const createdIsoLines = createIsoLines(
    matrix,
    breaks,
    zProperty,
    commonProperties,
    breaksProperties
  );
  const scaledIsolines = rescaleIsolines(createdIsoLines, matrix, pointGrid);
  return featureCollection(scaledIsolines);
}
function createIsoLines(matrix, breaks, zProperty, commonProperties, breaksProperties) {
  const results = [];
  for (let i = 0; i < breaks.length; i++) {
    const threshold = +breaks[i];
    const properties = __spreadValues(__spreadValues({}, commonProperties), breaksProperties[i]);
    properties[zProperty] = threshold;
    const isoline = multiLineString(
      isoContours(matrix, threshold, { linearRing: false, noFrame: true }),
      properties
    );
    results.push(isoline);
  }
  return results;
}
function rescaleIsolines(createdIsoLines, matrix, points) {
  const gridBbox = bbox(points);
  const originalWidth = gridBbox[2] - gridBbox[0];
  const originalHeigth = gridBbox[3] - gridBbox[1];
  const x0 = gridBbox[0];
  const y0 = gridBbox[1];
  const matrixWidth = matrix[0].length - 1;
  const matrixHeight = matrix.length - 1;
  const scaleX = originalWidth / matrixWidth;
  const scaleY = originalHeigth / matrixHeight;
  const resize = (point) => {
    point[0] = point[0] * scaleX + x0;
    point[1] = point[1] * scaleY + y0;
  };
  createdIsoLines.forEach((isoline) => {
    coordEach(isoline, resize);
  });
  return createdIsoLines;
}
var turf_isolines_default = isolines;
export {
  turf_isolines_default as default,
  isolines
};
//# sourceMappingURL=index.js.map