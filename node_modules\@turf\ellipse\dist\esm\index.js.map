{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  degreesToRadians,\n  polygon,\n  isObject,\n  isN<PERSON>ber,\n  Coord,\n  Units,\n} from \"@turf/helpers\";\nimport { rhumbDestination } from \"@turf/rhumb-destination\";\nimport { transformRotate } from \"@turf/transform-rotate\";\nimport { getCoord } from \"@turf/invariant\";\nimport { GeoJsonProperties, Feature, Polygon } from \"geojson\";\n\n/**\n * Takes a {@link Point} and calculates the ellipse polygon given two semi-axes expressed in variable units and steps for precision.\n *\n * @param {Coord} center center point\n * @param {number} xSemiAxis semi (major) axis of the ellipse along the x-axis\n * @param {number} ySemiAxis semi (minor) axis of the ellipse along the y-axis\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.angle=0] angle of rotation in decimal degrees, positive clockwise\n * @param {Coord} [options.pivot=center] point around which any rotation will be performed\n * @param {number} [options.steps=64] number of steps\n * @param {string} [options.units='kilometers'] unit of measurement for axes\n * @param {Object} [options.properties={}] properties\n * @returns {Feature<Polygon>} ellipse polygon\n * @example\n * var center = [-75, 40];\n * var xSemiAxis = 5;\n * var ySemiAxis = 2;\n * var ellipse = turf.ellipse(center, xSemiAxis, ySemiAxis);\n *\n * //addToMap\n * var addToMap = [turf.point(center), ellipse]\n */\nfunction ellipse(\n  center: Coord,\n  xSemiAxis: number,\n  ySemiAxis: number,\n  options: {\n    steps?: number;\n    units?: Units;\n    angle?: number;\n    pivot?: Coord;\n    properties?: GeoJsonProperties;\n  }\n): Feature<Polygon> {\n  // Optional params\n  options = options || {};\n  const steps = options.steps || 64;\n  const units = options.units || \"kilometers\";\n  const angle = options.angle || 0;\n  const pivot = options.pivot || center;\n  const properties = options.properties || {};\n\n  // validation\n  if (!center) throw new Error(\"center is required\");\n  if (!xSemiAxis) throw new Error(\"xSemiAxis is required\");\n  if (!ySemiAxis) throw new Error(\"ySemiAxis is required\");\n  if (!isObject(options)) throw new Error(\"options must be an object\");\n  if (!isNumber(steps)) throw new Error(\"steps must be a number\");\n  if (!isNumber(angle)) throw new Error(\"angle must be a number\");\n\n  const centerCoords = getCoord(center);\n  if (units !== \"degrees\") {\n    const xDest = rhumbDestination(center, xSemiAxis, 90, { units });\n    const yDest = rhumbDestination(center, ySemiAxis, 0, { units });\n    xSemiAxis = getCoord(xDest)[0] - centerCoords[0];\n    ySemiAxis = getCoord(yDest)[1] - centerCoords[1];\n  }\n\n  const coordinates: number[][] = [];\n  for (let i = 0; i < steps; i += 1) {\n    const stepAngle = (i * -360) / steps;\n    let x =\n      (xSemiAxis * ySemiAxis) /\n      Math.sqrt(\n        Math.pow(ySemiAxis, 2) +\n          Math.pow(xSemiAxis, 2) * Math.pow(getTanDeg(stepAngle), 2)\n      );\n    let y =\n      (xSemiAxis * ySemiAxis) /\n      Math.sqrt(\n        Math.pow(xSemiAxis, 2) +\n          Math.pow(ySemiAxis, 2) / Math.pow(getTanDeg(stepAngle), 2)\n      );\n\n    if (stepAngle < -90 && stepAngle >= -270) x = -x;\n    if (stepAngle < -180 && stepAngle >= -360) y = -y;\n    if (units === \"degrees\") {\n      const angleRad = degreesToRadians(angle);\n      const newx = x * Math.cos(angleRad) + y * Math.sin(angleRad);\n      const newy = y * Math.cos(angleRad) - x * Math.sin(angleRad);\n      x = newx;\n      y = newy;\n    }\n\n    coordinates.push([x + centerCoords[0], y + centerCoords[1]]);\n  }\n  coordinates.push(coordinates[0]);\n  if (units === \"degrees\") {\n    return polygon([coordinates], properties);\n  } else {\n    return transformRotate(polygon([coordinates], properties), angle, {\n      pivot,\n    });\n  }\n}\n\n/**\n * Get Tan Degrees\n *\n * @private\n * @param {number} deg Degrees\n * @returns {number} Tan Degrees\n */\nfunction getTanDeg(deg: number) {\n  const rad = (deg * Math.PI) / 180;\n  return Math.tan(rad);\n}\n\nexport { ellipse };\nexport default ellipse;\n"], "mappings": ";AAAA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAGK;AACP,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,gBAAgB;AAyBzB,SAAS,QACP,QACA,WACA,WACA,SAOkB;AAElB,YAAU,WAAW,CAAC;AACtB,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,aAAa,QAAQ,cAAc,CAAC;AAG1C,MAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,oBAAoB;AACjD,MAAI,CAAC,UAAW,OAAM,IAAI,MAAM,uBAAuB;AACvD,MAAI,CAAC,UAAW,OAAM,IAAI,MAAM,uBAAuB;AACvD,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,2BAA2B;AACnE,MAAI,CAAC,SAAS,KAAK,EAAG,OAAM,IAAI,MAAM,wBAAwB;AAC9D,MAAI,CAAC,SAAS,KAAK,EAAG,OAAM,IAAI,MAAM,wBAAwB;AAE9D,QAAM,eAAe,SAAS,MAAM;AACpC,MAAI,UAAU,WAAW;AACvB,UAAM,QAAQ,iBAAiB,QAAQ,WAAW,IAAI,EAAE,MAAM,CAAC;AAC/D,UAAM,QAAQ,iBAAiB,QAAQ,WAAW,GAAG,EAAE,MAAM,CAAC;AAC9D,gBAAY,SAAS,KAAK,EAAE,CAAC,IAAI,aAAa,CAAC;AAC/C,gBAAY,SAAS,KAAK,EAAE,CAAC,IAAI,aAAa,CAAC;AAAA,EACjD;AAEA,QAAM,cAA0B,CAAC;AACjC,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AACjC,UAAM,YAAa,IAAI,OAAQ;AAC/B,QAAI,IACD,YAAY,YACb,KAAK;AAAA,MACH,KAAK,IAAI,WAAW,CAAC,IACnB,KAAK,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,UAAU,SAAS,GAAG,CAAC;AAAA,IAC7D;AACF,QAAI,IACD,YAAY,YACb,KAAK;AAAA,MACH,KAAK,IAAI,WAAW,CAAC,IACnB,KAAK,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,UAAU,SAAS,GAAG,CAAC;AAAA,IAC7D;AAEF,QAAI,YAAY,OAAO,aAAa,KAAM,KAAI,CAAC;AAC/C,QAAI,YAAY,QAAQ,aAAa,KAAM,KAAI,CAAC;AAChD,QAAI,UAAU,WAAW;AACvB,YAAM,WAAW,iBAAiB,KAAK;AACvC,YAAM,OAAO,IAAI,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK,IAAI,QAAQ;AAC3D,YAAM,OAAO,IAAI,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK,IAAI,QAAQ;AAC3D,UAAI;AACJ,UAAI;AAAA,IACN;AAEA,gBAAY,KAAK,CAAC,IAAI,aAAa,CAAC,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC;AAAA,EAC7D;AACA,cAAY,KAAK,YAAY,CAAC,CAAC;AAC/B,MAAI,UAAU,WAAW;AACvB,WAAO,QAAQ,CAAC,WAAW,GAAG,UAAU;AAAA,EAC1C,OAAO;AACL,WAAO,gBAAgB,QAAQ,CAAC,WAAW,GAAG,UAAU,GAAG,OAAO;AAAA,MAChE;AAAA,IACF,CAAC;AAAA,EACH;AACF;AASA,SAAS,UAAU,KAAa;AAC9B,QAAM,MAAO,MAAM,KAAK,KAAM;AAC9B,SAAO,KAAK,IAAI,GAAG;AACrB;AAGA,IAAO,uBAAQ;", "names": []}