{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-buffer/dist/cjs/index.cjs", "../../index.js"], "names": ["feature", "geometry", "buffered"], "mappings": "AAAA;ACAA,sCAAuB;AACvB,8EAAiB;AACjB,kCAAsC;AACtC,+BAAwC;AACxC;AACE;AACA;AACA;AACA;AACA;AAAA,wCACK;AAEP,IAAM,EAAE,QAAA,EAAU,aAAA,EAAe,cAAc,EAAA,EAAI,cAAA;AAyBnD,SAAS,MAAA,CAAO,OAAA,EAAS,MAAA,EAAQ,OAAA,EAAS;AAExC,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AAGtB,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,YAAA;AAC7B,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,CAAA;AAG7B,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AACnD,EAAA,GAAA,CAAI,OAAO,QAAA,IAAY,QAAA,EAAU,MAAM,IAAI,KAAA,CAAM,2BAA2B,CAAA;AAC5E,EAAA,GAAA,CAAI,OAAO,MAAA,IAAU,QAAA,EAAU,MAAM,IAAI,KAAA,CAAM,yBAAyB,CAAA;AAGxE,EAAA,GAAA,CAAI,OAAA,IAAW,KAAA,CAAA,EAAW,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC9D,EAAA,GAAA,CAAI,MAAA,GAAS,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,8BAA8B,CAAA;AAE9D,EAAA,IAAI,QAAA,EAAU,CAAC,CAAA;AACf,EAAA,OAAA,CAAQ,OAAA,CAAQ,IAAA,EAAM;AAAA,IACpB,KAAK,oBAAA;AACH,MAAA,4BAAA,OAAS,EAAS,QAAA,CAAU,QAAA,EAAU;AACpC,QAAA,IAAI,SAAA,EAAW,aAAA,CAAc,QAAA,EAAU,MAAA,EAAQ,KAAA,EAAO,KAAK,CAAA;AAC3D,QAAA,GAAA,CAAI,QAAA,EAAU,OAAA,CAAQ,IAAA,CAAK,QAAQ,CAAA;AAAA,MACrC,CAAC,CAAA;AACD,MAAA,OAAO,wCAAA,OAAyB,CAAA;AAAA,IAClC,KAAK,mBAAA;AACH,MAAA,+BAAA,OAAY,EAAS,QAAA,CAAUA,QAAAA,EAAS;AACtC,QAAA,IAAI,cAAA,EAAgB,aAAA,CAAcA,QAAAA,EAAS,MAAA,EAAQ,KAAA,EAAO,KAAK,CAAA;AAC/D,QAAA,GAAA,CAAI,aAAA,EAAe;AACjB,UAAA,+BAAA,aAAY,EAAe,QAAA,CAAU,QAAA,EAAU;AAC7C,YAAA,GAAA,CAAI,QAAA,EAAU,OAAA,CAAQ,IAAA,CAAK,QAAQ,CAAA;AAAA,UACrC,CAAC,CAAA;AAAA,QACH;AAAA,MACF,CAAC,CAAA;AACD,MAAA,OAAO,wCAAA,OAAyB,CAAA;AAAA,EACpC;AACA,EAAA,OAAO,aAAA,CAAc,OAAA,EAAS,MAAA,EAAQ,KAAA,EAAO,KAAK,CAAA;AACpD;AAYA,SAAS,aAAA,CAAc,OAAA,EAAS,MAAA,EAAQ,KAAA,EAAO,KAAA,EAAO;AACpD,EAAA,IAAI,WAAA,EAAa,OAAA,CAAQ,WAAA,GAAc,CAAC,CAAA;AACxC,EAAA,IAAI,SAAA,EAAW,OAAA,CAAQ,KAAA,IAAS,UAAA,EAAY,OAAA,CAAQ,SAAA,EAAW,OAAA;AAG/D,EAAA,GAAA,CAAI,QAAA,CAAS,KAAA,IAAS,oBAAA,EAAsB;AAC1C,IAAA,IAAI,QAAA,EAAU,CAAC,CAAA;AACf,IAAA,4BAAA,OAAS,EAAS,QAAA,CAAUC,SAAAA,EAAU;AACpC,MAAA,IAAIC,UAAAA,EAAW,aAAA,CAAcD,SAAAA,EAAU,MAAA,EAAQ,KAAA,EAAO,KAAK,CAAA;AAC3D,MAAA,GAAA,CAAIC,SAAAA,EAAU,OAAA,CAAQ,IAAA,CAAKA,SAAQ,CAAA;AAAA,IACrC,CAAC,CAAA;AACD,IAAA,OAAO,wCAAA,OAAyB,CAAA;AAAA,EAClC;AAGA,EAAA,IAAI,WAAA,EAAa,gBAAA,CAAiB,QAAQ,CAAA;AAC1C,EAAA,IAAI,UAAA,EAAY;AAAA,IACd,IAAA,EAAM,QAAA,CAAS,IAAA;AAAA,IACf,WAAA,EAAa,aAAA,CAAc,QAAA,CAAS,WAAA,EAAa,UAAU;AAAA,EAC7D,CAAA;AAGA,EAAA,IAAI,OAAA,EAAS,IAAI,aAAA,CAAc,CAAA;AAC/B,EAAA,IAAI,KAAA,EAAO,MAAA,CAAO,IAAA,CAAK,SAAS,CAAA;AAChC,EAAA,IAAI,SAAA,EAAW,sCAAA,sCAAgB,MAAgB,EAAQ,KAAK,CAAA,EAAG,QAAQ,CAAA;AACvE,EAAA,IAAI,SAAA,EAAW,QAAA,CAAS,QAAA,CAAS,IAAA,EAAM,QAAA,EAAU,KAAK,CAAA;AACtD,EAAA,IAAI,OAAA,EAAS,IAAI,aAAA,CAAc,CAAA;AAC/B,EAAA,SAAA,EAAW,MAAA,CAAO,KAAA,CAAM,QAAQ,CAAA;AAGhC,EAAA,GAAA,CAAI,WAAA,CAAY,QAAA,CAAS,WAAW,CAAA,EAAG,OAAO,KAAA,CAAA;AAG9C,EAAA,IAAI,OAAA,EAAS;AAAA,IACX,IAAA,EAAM,QAAA,CAAS,IAAA;AAAA,IACf,WAAA,EAAa,eAAA,CAAgB,QAAA,CAAS,WAAA,EAAa,UAAU;AAAA,EAC/D,CAAA;AAEA,EAAA,OAAO,8BAAA,MAAQ,EAAQ,UAAU,CAAA;AACnC;AASA,SAAS,WAAA,CAAY,MAAA,EAAQ;AAC3B,EAAA,GAAA,CAAI,KAAA,CAAM,OAAA,CAAQ,MAAA,CAAO,CAAC,CAAC,CAAA,EAAG,OAAO,WAAA,CAAY,MAAA,CAAO,CAAC,CAAC,CAAA;AAC1D,EAAA,OAAO,KAAA,CAAM,MAAA,CAAO,CAAC,CAAC,CAAA;AACxB;AAUA,SAAS,aAAA,CAAc,MAAA,EAAQ,IAAA,EAAM;AACnC,EAAA,GAAA,CAAI,OAAO,MAAA,CAAO,CAAC,EAAA,IAAM,QAAA,EAAU,OAAO,IAAA,CAAK,MAAM,CAAA;AACrD,EAAA,OAAO,MAAA,CAAO,GAAA,CAAI,QAAA,CAAU,KAAA,EAAO;AACjC,IAAA,OAAO,aAAA,CAAc,KAAA,EAAO,IAAI,CAAA;AAAA,EAClC,CAAC,CAAA;AACH;AAUA,SAAS,eAAA,CAAgB,MAAA,EAAQ,IAAA,EAAM;AACrC,EAAA,GAAA,CAAI,OAAO,MAAA,CAAO,CAAC,EAAA,IAAM,QAAA,EAAU,OAAO,IAAA,CAAK,MAAA,CAAO,MAAM,CAAA;AAC5D,EAAA,OAAO,MAAA,CAAO,GAAA,CAAI,QAAA,CAAU,KAAA,EAAO;AACjC,IAAA,OAAO,eAAA,CAAgB,KAAA,EAAO,IAAI,CAAA;AAAA,EACpC,CAAC,CAAA;AACH;AASA,SAAS,gBAAA,CAAiB,OAAA,EAAS;AACjC,EAAA,IAAI,OAAA,EAAS,4BAAA,OAAc,CAAA,CAAE,QAAA,CAAS,WAAA;AACtC,EAAA,IAAI,SAAA,EAAW,CAAC,CAAC,MAAA,CAAO,CAAC,CAAA,EAAG,CAAC,MAAA,CAAO,CAAC,CAAC,CAAA;AACtC,EAAA,OAAO,4CAAA,CAAwB,CAAE,MAAA,CAAO,QAAQ,CAAA,CAAE,KAAA,CAAM,oBAAW,CAAA;AACrE;AAGA,IAAO,oBAAA,EAAQ,MAAA;ADzFf;AACE;AACA;AACF,+DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-buffer/dist/cjs/index.cjs", "sourcesContent": [null, "import { center } from \"@turf/center\";\nimport jsts from \"@turf/jsts\";\nimport { geomEach, featureEach } from \"@turf/meta\";\nimport { geoAzimuthalEquidistant } from \"d3-geo\";\nimport {\n  feature,\n  featureCollection,\n  radiansToLength,\n  lengthToRadians,\n  earthRadius,\n} from \"@turf/helpers\";\n\nconst { <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSONReader, GeoJSONWriter } = jsts;\n\n/**\n * Calculates a buffer for input features for a given radius. Units supported are miles, kilometers, and degrees.\n *\n * When using a negative radius, the resulting geometry may be invalid if\n * it's too small compared to the radius magnitude. If the input is a\n * FeatureCollection, only valid members will be returned in the output\n * FeatureCollection - i.e., the output collection may have fewer members than\n * the input, or even be empty.\n *\n * @function\n * @param {FeatureCollection|Geometry|Feature<any>} geojson input to be buffered\n * @param {number} radius distance to draw the buffer (negative values are allowed)\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units=\"kilometers\"] any of the options supported by turf units\n * @param {number} [options.steps=8] number of steps\n * @returns {FeatureCollection|Feature<Polygon|MultiPolygon>|undefined} buffered features\n * @example\n * var point = turf.point([-90.548630, 14.616599]);\n * var buffered = turf.buffer(point, 500, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [point, buffered]\n */\nfunction buffer(geojson, radius, options) {\n  // Optional params\n  options = options || {};\n\n  // use user supplied options or default values\n  var units = options.units || \"kilometers\";\n  var steps = options.steps || 8;\n\n  // validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (typeof options !== \"object\") throw new Error(\"options must be an object\");\n  if (typeof steps !== \"number\") throw new Error(\"steps must be an number\");\n\n  // Allow negative buffers (\"erosion\") or zero-sized buffers (\"repair geometry\")\n  if (radius === undefined) throw new Error(\"radius is required\");\n  if (steps <= 0) throw new Error(\"steps must be greater than 0\");\n\n  var results = [];\n  switch (geojson.type) {\n    case \"GeometryCollection\":\n      geomEach(geojson, function (geometry) {\n        var buffered = bufferFeature(geometry, radius, units, steps);\n        if (buffered) results.push(buffered);\n      });\n      return featureCollection(results);\n    case \"FeatureCollection\":\n      featureEach(geojson, function (feature) {\n        var multiBuffered = bufferFeature(feature, radius, units, steps);\n        if (multiBuffered) {\n          featureEach(multiBuffered, function (buffered) {\n            if (buffered) results.push(buffered);\n          });\n        }\n      });\n      return featureCollection(results);\n  }\n  return bufferFeature(geojson, radius, units, steps);\n}\n\n/**\n * Buffer single Feature/Geometry\n *\n * @private\n * @param {Feature<any>} geojson input to be buffered\n * @param {number} radius distance to draw the buffer\n * @param {string} [units='kilometers'] any of the options supported by turf units\n * @param {number} [steps=8] number of steps\n * @returns {Feature<Polygon|MultiPolygon>} buffered feature\n */\nfunction bufferFeature(geojson, radius, units, steps) {\n  var properties = geojson.properties || {};\n  var geometry = geojson.type === \"Feature\" ? geojson.geometry : geojson;\n\n  // Geometry Types faster than jsts\n  if (geometry.type === \"GeometryCollection\") {\n    var results = [];\n    geomEach(geojson, function (geometry) {\n      var buffered = bufferFeature(geometry, radius, units, steps);\n      if (buffered) results.push(buffered);\n    });\n    return featureCollection(results);\n  }\n\n  // Project GeoJSON to Azimuthal Equidistant projection (convert to Meters)\n  var projection = defineProjection(geometry);\n  var projected = {\n    type: geometry.type,\n    coordinates: projectCoords(geometry.coordinates, projection),\n  };\n\n  // JSTS buffer operation\n  var reader = new GeoJSONReader();\n  var geom = reader.read(projected);\n  var distance = radiansToLength(lengthToRadians(radius, units), \"meters\");\n  var buffered = BufferOp.bufferOp(geom, distance, steps);\n  var writer = new GeoJSONWriter();\n  buffered = writer.write(buffered);\n\n  // Detect if empty geometries\n  if (coordsIsNaN(buffered.coordinates)) return undefined;\n\n  // Unproject coordinates (convert to Degrees)\n  var result = {\n    type: buffered.type,\n    coordinates: unprojectCoords(buffered.coordinates, projection),\n  };\n\n  return feature(result, properties);\n}\n\n/**\n * Coordinates isNaN\n *\n * @private\n * @param {Array<any>} coords GeoJSON Coordinates\n * @returns {boolean} if NaN exists\n */\nfunction coordsIsNaN(coords) {\n  if (Array.isArray(coords[0])) return coordsIsNaN(coords[0]);\n  return isNaN(coords[0]);\n}\n\n/**\n * Project coordinates to projection\n *\n * @private\n * @param {Array<any>} coords to project\n * @param {GeoProjection} proj D3 Geo Projection\n * @returns {Array<any>} projected coordinates\n */\nfunction projectCoords(coords, proj) {\n  if (typeof coords[0] !== \"object\") return proj(coords);\n  return coords.map(function (coord) {\n    return projectCoords(coord, proj);\n  });\n}\n\n/**\n * Un-Project coordinates to projection\n *\n * @private\n * @param {Array<any>} coords to un-project\n * @param {GeoProjection} proj D3 Geo Projection\n * @returns {Array<any>} un-projected coordinates\n */\nfunction unprojectCoords(coords, proj) {\n  if (typeof coords[0] !== \"object\") return proj.invert(coords);\n  return coords.map(function (coord) {\n    return unprojectCoords(coord, proj);\n  });\n}\n\n/**\n * Define Azimuthal Equidistant projection\n *\n * @private\n * @param {Geometry|Feature<any>} geojson Base projection on center of GeoJSON\n * @returns {GeoProjection} D3 Geo Azimuthal Equidistant Projection\n */\nfunction defineProjection(geojson) {\n  var coords = center(geojson).geometry.coordinates;\n  var rotation = [-coords[0], -coords[1]];\n  return geoAzimuthalEquidistant().rotate(rotation).scale(earthRadius);\n}\n\nexport { buffer };\nexport default buffer;\n"]}