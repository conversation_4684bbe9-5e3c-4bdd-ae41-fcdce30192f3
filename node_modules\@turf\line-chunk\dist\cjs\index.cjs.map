{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-chunk/dist/cjs/index.cjs", "../../index.js"], "names": [], "mappings": "AAAA;ACAA,sCAAuB;AACvB,wDAA+B;AAC/B,kCAA4B;AAC5B,wCAA4C;AAqB5C,SAAS,SAAA,CAAU,OAAA,EAAS,aAAA,EAAe,OAAA,EAAS;AAElD,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,KAAA;AACpB,EAAA,IAAI,QAAA,EAAU,OAAA,CAAQ,OAAA;AAGtB,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AACnD,EAAA,GAAA,CAAI,cAAA,GAAiB,CAAA;AACnB,IAAA,MAAM,IAAI,KAAA,CAAM,sCAAsC,CAAA;AAGxD,EAAA,IAAI,QAAA,EAAU,CAAC,CAAA;AAGf,EAAA,+BAAA,OAAY,EAAS,QAAA,CAAU,OAAA,EAAS;AAEtC,IAAA,GAAA,CAAI,OAAA;AACF,MAAA,OAAA,CAAQ,QAAA,CAAS,YAAA,EAAc,OAAA,CAAQ,QAAA,CAAS,WAAA,CAAY,OAAA,CAAQ,CAAA;AAEtE,IAAA,iBAAA,CAAkB,OAAA,EAAS,aAAA,EAAe,KAAA,EAAO,QAAA,CAAU,OAAA,EAAS;AAClE,MAAA,OAAA,CAAQ,IAAA,CAAK,OAAO,CAAA;AAAA,IACtB,CAAC,CAAA;AAAA,EACH,CAAC,CAAA;AACD,EAAA,OAAO,wCAAA,OAAyB,CAAA;AAClC;AAYA,SAAS,iBAAA,CAAkB,IAAA,EAAM,aAAA,EAAe,KAAA,EAAO,QAAA,EAAU;AAC/D,EAAA,IAAI,WAAA,EAAa,4BAAA,IAAO,EAAM,EAAE,MAAa,CAAC,CAAA;AAG9C,EAAA,GAAA,CAAI,WAAA,GAAc,aAAA,EAAe,OAAO,QAAA,CAAS,IAAI,CAAA;AAErD,EAAA,IAAI,iBAAA,EAAmB,WAAA,EAAa,aAAA;AAGpC,EAAA,GAAA,CAAI,CAAC,MAAA,CAAO,SAAA,CAAU,gBAAgB,CAAA,EAAG;AACvC,IAAA,iBAAA,EAAmB,IAAA,CAAK,KAAA,CAAM,gBAAgB,EAAA,EAAI,CAAA;AAAA,EACpD;AAEA,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,gBAAA,EAAkB,CAAA,EAAA,EAAK;AACzC,IAAA,IAAI,QAAA,EAAU,4CAAA;AAAA,MACZ,IAAA;AAAA,MACA,cAAA,EAAgB,CAAA;AAAA,MAChB,cAAA,EAAA,CAAiB,EAAA,EAAI,CAAA,CAAA;AAAA,MACrB,EAAE,MAAa;AAAA,IACjB,CAAA;AACA,IAAA,QAAA,CAAS,OAAA,EAAS,CAAC,CAAA;AAAA,EACrB;AACF;AAGA,IAAO,wBAAA,EAAQ,SAAA;AD9Cf;AACE;AACA;AACF,yEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-chunk/dist/cjs/index.cjs", "sourcesContent": [null, "import { length } from \"@turf/length\";\nimport { lineSliceAlong } from \"@turf/line-slice-along\";\nimport { flattenEach } from \"@turf/meta\";\nimport { featureCollection, isObject } from \"@turf/helpers\";\n\n/**\n * Divides a {@link LineString} into chunks of a specified length.\n * If the line is shorter than the segment length then the original line is returned.\n *\n * @function\n * @param {FeatureCollection|Geometry|Feature<LineString|MultiLineString>} geojson the lines to split\n * @param {number} segmentLength how long to make each segment\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] units can be degrees, radians, miles, or kilometers\n * @param {boolean} [options.reverse=false] reverses coordinates to start the first chunked segment at the end\n * @returns {FeatureCollection<LineString>} collection of line segments\n * @example\n * var line = turf.lineString([[-95, 40], [-93, 45], [-85, 50]]);\n *\n * var chunk = turf.lineChunk(line, 15, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [chunk];\n */\nfunction lineChunk(geojson, segmentLength, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var units = options.units;\n  var reverse = options.reverse;\n\n  // Validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (segmentLength <= 0)\n    throw new Error(\"segmentLength must be greater than 0\");\n\n  // Container\n  var results = [];\n\n  // Flatten each feature to simple LineString\n  flattenEach(geojson, function (feature) {\n    // reverses coordinates to start the first chunked segment at the end\n    if (reverse)\n      feature.geometry.coordinates = feature.geometry.coordinates.reverse();\n\n    sliceLineSegments(feature, segmentLength, units, function (segment) {\n      results.push(segment);\n    });\n  });\n  return featureCollection(results);\n}\n\n/**\n * Slice Line Segments\n *\n * @private\n * @param {Feature<LineString>} line GeoJSON LineString\n * @param {number} segmentLength how long to make each segment\n * @param {string}[units='kilometers'] units can be degrees, radians, miles, or kilometers\n * @param {Function} callback iterate over sliced line segments\n * @returns {void}\n */\nfunction sliceLineSegments(line, segmentLength, units, callback) {\n  var lineLength = length(line, { units: units });\n\n  // If the line is shorter than the segment length then the orginal line is returned.\n  if (lineLength <= segmentLength) return callback(line);\n\n  var numberOfSegments = lineLength / segmentLength;\n\n  // If numberOfSegments is integer, no need to plus 1\n  if (!Number.isInteger(numberOfSegments)) {\n    numberOfSegments = Math.floor(numberOfSegments) + 1;\n  }\n\n  for (var i = 0; i < numberOfSegments; i++) {\n    var outline = lineSliceAlong(\n      line,\n      segmentLength * i,\n      segmentLength * (i + 1),\n      { units: units }\n    );\n    callback(outline, i);\n  }\n}\n\nexport { lineChunk };\nexport default lineChunk;\n"]}