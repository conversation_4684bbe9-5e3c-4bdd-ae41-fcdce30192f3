# @turf/boolean-point-on-line

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanPointOnLine

Returns true if a point is on a line. Accepts a optional parameter to ignore the
start and end vertices of the linestring.

### Parameters

*   `pt` **[Coord][1]** GeoJSON Point
*   `line` **[Feature][2]<[LineString][3]>** GeoJSON LineString
*   `options` **[Object][4]** Optional parameters (optional, default `{}`)

    *   `options.ignoreEndVertices` **[boolean][5]** whether to ignore the start and end vertices. (optional, default `false`)
    *   `options.epsilon` **[number][6]?** Fractional number to compare with the cross product result. Useful for dealing with floating points such as lng/lat points

### Examples

```javascript
var pt = turf.point([0, 0]);
var line = turf.lineString([[-1, -1],[1, 1],[1.5, 2.2]]);
var isPointOnLine = turf.booleanPointOnLine(pt, line);
//=true
```

Returns **[boolean][5]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-point-on-line
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
