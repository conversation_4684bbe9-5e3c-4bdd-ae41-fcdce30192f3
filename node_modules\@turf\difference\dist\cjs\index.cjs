"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { newObj[key] = obj[key]; } } } newObj.default = obj; return newObj; } }// index.ts
var _polyclipts = require('polyclip-ts'); var polyclip = _interopRequireWildcard(_polyclipts);
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
function difference2(features) {
  const geoms = [];
  _meta.geomEach.call(void 0, features, (geom) => {
    geoms.push(geom.coordinates);
  });
  if (geoms.length < 2) {
    throw new Error("Must have at least two features");
  }
  const properties = features.features[0].properties || {};
  const differenced = polyclip.difference(geoms[0], ...geoms.slice(1));
  if (differenced.length === 0) return null;
  if (differenced.length === 1) return _helpers.polygon.call(void 0, differenced[0], properties);
  return _helpers.multiPolygon.call(void 0, differenced, properties);
}
var turf_difference_default = difference2;



exports.default = turf_difference_default; exports.difference = difference2;
//# sourceMappingURL=index.cjs.map