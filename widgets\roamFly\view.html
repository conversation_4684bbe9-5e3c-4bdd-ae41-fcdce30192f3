﻿<!DOCTYPE html>
<html class="no-js css-menubar" lang="zh-cn">

<head>
    <title>弹窗子页面</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 移动设备 viewport -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui">
    <meta name="author" content="火星科技 http://cesium.marsgis.cn ">
    <!-- 360浏览器默认使用Webkit内核 -->
    <meta name="renderer" content="webkit">
    <!-- Chrome浏览器添加桌面快捷方式（安卓） -->
    <link rel="icon" type="image/png" href="../../img/favicon/favicon.png">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Safari浏览器添加到主屏幕（IOS） -->
    <link rel="icon" sizes="192x192" href="img/favicon/apple-touch-icon.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="火星科技MarsGIS">
    <!-- Win8标题栏及ICON图标 -->
    <link rel="apple-touch-icon-precomposed" href="../../img/favicon/apple-touch-icon.png">
    <meta name="msapplication-TileImage" content="../../img/favicon/<EMAIL>">
    <meta name="msapplication-TileColor" content="#62a8ea">

    <!-- 第3方lib引入 -->
    <script type="text/javascript" src="../../lib/include-lib.js?time=20200102" libpath="../../lib/"
            include="jquery,font-awesome,bootstrap,jquery.range,admui-frame"></script>

    <link href="../../css/widget-win.css" rel="stylesheet" />
    <link href="view.css?time=20200102" rel="stylesheet" /> 
</head>
<body>
    <div class="mp_head2">
        <div class="btn-group" role="group">
            <button id="btnFlyStop" title="停止漫游" class="btn btn-primary  btn-sm">
                <span class="fa fa-play-circle-o"></span> 停止漫游
            </button>
            <button id="btnSelChars" title="查看剖面图" class="btn btn-primary  btn-sm">
                <span class="fa fa-area-chart"></span> 查看剖面图
            </button>
        </div>
    </div>



    <table class="mp_table">
        <tr>
            <td class="nametd">路线名称</td>
            <td id="td_name"> </td>
        </tr>
        <!--<tr>
            <td class="nametd">速度</td>
            <td>
                <input id="multiplier" type="number" min="-9999" max="9999"  value="1" class="mp_input" />
            </td>
        </tr>-->
        <tr>
            <td class="nametd">视角模式</td>
            <td>
                <select id="cameraType" class="mp_select" data-value="2">
                    <option value="">无</option>
                    <option value="gs">跟随视角</option>
                    <option value="dy">锁定第一视角</option>
                    <option value="sd">锁定上帝视角</option>
                </select>
            </td>
        </tr>
        <tr id="tr_followedX" title="距离漫游对象的水平距离，单位：米">
            <td class="nametd">视角距离</td>
            <td>
                <input id="followedX" type="number" min="0" max="99999"
                       value="1" class="mp_input" />
            </td>
        </tr>
        <tr id="tr_followedZ" title="距离漫游对象的高度，单位：米">
            <td class="nametd">视角高度</td>
            <td>
                <input id="followedZ" type="number" min="0" max="99999"
                       value="0" class="mp_input" />
            </td>
        </tr>

        <tr>
            <td class="nametd">总长度</td>
            <td id="td_alllength"> </td>
        </tr>
        <tr>
            <td class="nametd">已漫游长度</td>
            <td id="td_length"> </td>
        </tr>
        <tr>
            <td class="nametd">总时长</td>
            <td id="td_alltimes"> </td>
        </tr>
        <tr>
            <td class="nametd">已漫游时间</td>
            <td id="td_times"> </td>
        </tr>

        <tr>
            <td class="nametd">经度</td>
            <td id="td_jd"> </td>
        </tr>
        <tr>
            <td class="nametd">经度</td>
            <td id="td_wd"> </td>
        </tr>
        <tr>
            <td class="nametd">漫游高程</td>
            <td id="td_gd"> </td>
        </tr>
        <tr>
            <td class="nametd">地面高程</td>
            <td id="td_dmhb"> </td>
        </tr>
        <tr id="tr_ldgd">
            <td class="nametd">离地距离</td>
            <td id="td_ldgd"> </td>
        </tr>



        <tr>
            <td colspan="2" style="width: 100%;text-align:center;">
                <div class="progress">
                    <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="0"
                         aria-valuemin="0" aria-valuemax="100">
                        0%
                    </div>
                </div>
            </td>
        </tr>
    </table>

    <!--页面js-->
    <script src="view.js?time=20200102"></script>
</body>
</html>
