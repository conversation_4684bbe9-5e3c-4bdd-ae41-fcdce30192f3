{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { BBox, Feature, Point, GeoJsonProperties } from \"geojson\";\nimport { geomEach, coordEach } from \"@turf/meta\";\nimport { isNumber, point, Id } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Feature} or {@link FeatureCollection} and returns the mean center. Can be weighted.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] Translate GeoJSON Properties to Point\n * @param {Object} [options.bbox={}] Translate GeoJSON BBox to Point\n * @param {Object} [options.id={}] Translate GeoJSON Id to Point\n * @param {string} [options.weight] the property name used to weight the center\n * @returns {Feature<Point>} a Point feature at the mean center point of all input features\n * @example\n * var features = turf.featureCollection([\n *   turf.point([-97.522259, 35.4691], {value: 10}),\n *   turf.point([-97.502754, 35.463455], {value: 3}),\n *   turf.point([-97.508269, 35.463245], {value: 5})\n * ]);\n *\n * var options = {weight: \"value\"}\n * var mean = turf.centerMean(features, options);\n *\n * //addToMap\n * var addToMap = [features, mean]\n * mean.properties['marker-size'] = 'large';\n * mean.properties['marker-color'] = '#000';\n */\nfunction centerMean<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: any, // To-Do include Typescript AllGeoJSON\n  options: { properties?: P; bbox?: BBox; id?: Id; weight?: string } = {}\n): Feature<Point, P> {\n  let sumXs = 0;\n  let sumYs = 0;\n  let sumNs = 0;\n  geomEach(geojson, function (geom, featureIndex, properties) {\n    let weight = options.weight ? properties?.[options.weight] : undefined;\n    weight = weight === undefined || weight === null ? 1 : weight;\n    if (!isNumber(weight))\n      throw new Error(\n        \"weight value must be a number for feature index \" + featureIndex\n      );\n    weight = Number(weight);\n    if (weight > 0) {\n      coordEach(geom, function (coord) {\n        sumXs += coord[0] * weight;\n        sumYs += coord[1] * weight;\n        sumNs += weight;\n      });\n    }\n  });\n  return point([sumXs / sumNs, sumYs / sumNs], options.properties, options);\n}\n\nexport { centerMean };\nexport default centerMean;\n"], "mappings": ";AACA,SAAS,UAAU,iBAAiB;AACpC,SAAS,UAAU,aAAiB;AA4BpC,SAAS,WACP,SACA,UAAqE,CAAC,GACnD;AACnB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,WAAS,SAAS,SAAU,MAAM,cAAc,YAAY;AAC1D,QAAI,SAAS,QAAQ,SAAS,yCAAa,QAAQ,UAAU;AAC7D,aAAS,WAAW,UAAa,WAAW,OAAO,IAAI;AACvD,QAAI,CAAC,SAAS,MAAM;AAClB,YAAM,IAAI;AAAA,QACR,qDAAqD;AAAA,MACvD;AACF,aAAS,OAAO,MAAM;AACtB,QAAI,SAAS,GAAG;AACd,gBAAU,MAAM,SAAU,OAAO;AAC/B,iBAAS,MAAM,CAAC,IAAI;AACpB,iBAAS,MAAM,CAAC,IAAI;AACpB,iBAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO,MAAM,CAAC,QAAQ,OAAO,QAAQ,KAAK,GAAG,QAAQ,YAAY,OAAO;AAC1E;AAGA,IAAO,2BAAQ;", "names": []}