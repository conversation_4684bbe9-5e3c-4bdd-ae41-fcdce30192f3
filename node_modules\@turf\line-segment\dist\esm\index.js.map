{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  BBox,\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  Polygon,\n} from \"geojson\";\nimport { featureCollection, lineString } from \"@turf/helpers\";\nimport { getCoords } from \"@turf/invariant\";\nimport { flattenEach } from \"@turf/meta\";\n\n/**\n * Creates a {@link FeatureCollection} of 2-vertex {@link LineString} segments from a\n * {@link LineString|(Multi)LineString} or {@link Polygon|(Multi)Polygon}.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON Polygon or LineString\n * @returns {FeatureCollection<LineString>} 2-vertex line segments\n * @example\n * var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);\n * var segments = turf.lineSegment(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, segments]\n */\nfunction lineSegment<\n  G extends LineString | MultiLineString | Polygon | MultiPolygon,\n>(\n  geojson: Feature<G> | FeatureCollection<G> | G\n): FeatureCollection<LineString> {\n  if (!geojson) {\n    throw new Error(\"geojson is required\");\n  }\n\n  const results: Array<Feature<LineString>> = [];\n  flattenEach(geojson, (feature: Feature<any>) => {\n    lineSegmentFeature(feature, results);\n  });\n  return featureCollection(results);\n}\n\n/**\n * Line Segment\n *\n * @private\n * @param {Feature<LineString|Polygon>} geojson Line or polygon feature\n * @param {Array} results push to results\n * @returns {void}\n */\nfunction lineSegmentFeature(\n  geojson: Feature<LineString | Polygon>,\n  results: Array<Feature<LineString>>\n) {\n  let coords: number[][][] = [];\n  const geometry = geojson.geometry;\n  if (geometry !== null) {\n    switch (geometry.type) {\n      case \"Polygon\":\n        coords = getCoords(geometry);\n        break;\n      case \"LineString\":\n        coords = [getCoords(geometry)];\n    }\n    coords.forEach((coord) => {\n      const segments = createSegments(coord, geojson.properties);\n      segments.forEach((segment) => {\n        segment.id = results.length;\n        results.push(segment);\n      });\n    });\n  }\n}\n\n/**\n * Create Segments from LineString coordinates\n *\n * @private\n * @param {Array<Array<number>>} coords LineString coordinates\n * @param {*} properties GeoJSON properties\n * @returns {Array<Feature<LineString>>} line segments\n */\nfunction createSegments(coords: number[][], properties: any) {\n  const segments: Array<Feature<LineString>> = [];\n  coords.reduce((previousCoords, currentCoords) => {\n    const segment = lineString([previousCoords, currentCoords], properties);\n    segment.bbox = bbox(previousCoords, currentCoords);\n    segments.push(segment);\n    return currentCoords;\n  });\n  return segments;\n}\n\n/**\n * Create BBox between two coordinates (faster than @turf/bbox)\n *\n * @private\n * @param {Array<number>} coords1 Point coordinate\n * @param {Array<number>} coords2 Point coordinate\n * @returns {BBox} [west, south, east, north]\n */\nfunction bbox(coords1: number[], coords2: number[]): BBox {\n  const x1 = coords1[0];\n  const y1 = coords1[1];\n  const x2 = coords2[0];\n  const y2 = coords2[1];\n  const west = x1 < x2 ? x1 : x2;\n  const south = y1 < y2 ? y1 : y2;\n  const east = x1 > x2 ? x1 : x2;\n  const north = y1 > y2 ? y1 : y2;\n  return [west, south, east, north];\n}\n\nexport { lineSegment };\nexport default lineSegment;\n"], "mappings": ";AASA,SAAS,mBAAmB,kBAAkB;AAC9C,SAAS,iBAAiB;AAC1B,SAAS,mBAAmB;AAgB5B,SAAS,YAGP,SAC+B;AAC/B,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,QAAM,UAAsC,CAAC;AAC7C,cAAY,SAAS,CAAC,YAA0B;AAC9C,uBAAmB,SAAS,OAAO;AAAA,EACrC,CAAC;AACD,SAAO,kBAAkB,OAAO;AAClC;AAUA,SAAS,mBACP,SACA,SACA;AACA,MAAI,SAAuB,CAAC;AAC5B,QAAM,WAAW,QAAQ;AACzB,MAAI,aAAa,MAAM;AACrB,YAAQ,SAAS,MAAM;AAAA,MACrB,KAAK;AACH,iBAAS,UAAU,QAAQ;AAC3B;AAAA,MACF,KAAK;AACH,iBAAS,CAAC,UAAU,QAAQ,CAAC;AAAA,IACjC;AACA,WAAO,QAAQ,CAAC,UAAU;AACxB,YAAM,WAAW,eAAe,OAAO,QAAQ,UAAU;AACzD,eAAS,QAAQ,CAAC,YAAY;AAC5B,gBAAQ,KAAK,QAAQ;AACrB,gBAAQ,KAAK,OAAO;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAUA,SAAS,eAAe,QAAoB,YAAiB;AAC3D,QAAM,WAAuC,CAAC;AAC9C,SAAO,OAAO,CAAC,gBAAgB,kBAAkB;AAC/C,UAAM,UAAU,WAAW,CAAC,gBAAgB,aAAa,GAAG,UAAU;AACtE,YAAQ,OAAO,KAAK,gBAAgB,aAAa;AACjD,aAAS,KAAK,OAAO;AACrB,WAAO;AAAA,EACT,CAAC;AACD,SAAO;AACT;AAUA,SAAS,KAAK,SAAmB,SAAyB;AACxD,QAAM,KAAK,QAAQ,CAAC;AACpB,QAAM,KAAK,QAAQ,CAAC;AACpB,QAAM,KAAK,QAAQ,CAAC;AACpB,QAAM,KAAK,QAAQ,CAAC;AACpB,QAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,QAAM,QAAQ,KAAK,KAAK,KAAK;AAC7B,QAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,QAAM,QAAQ,KAAK,KAAK,KAAK;AAC7B,SAAO,CAAC,MAAM,OAAO,MAAM,KAAK;AAClC;AAGA,IAAO,4BAAQ;", "names": []}