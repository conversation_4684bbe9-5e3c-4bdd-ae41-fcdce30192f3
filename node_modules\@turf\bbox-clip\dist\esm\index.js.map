{"version": 3, "sources": ["../../index.ts", "../../lib/lineclip.ts"], "sourcesContent": ["import {\n  BBox,\n  Feature,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  GeoJsonProperties,\n  Polygon,\n} from \"geojson\";\n\nimport {\n  lineString,\n  multiLineString,\n  multiPolygon,\n  polygon,\n} from \"@turf/helpers\";\nimport { getGeom } from \"@turf/invariant\";\nimport { lineclip, polygonclip } from \"./lib/lineclip.js\";\n\n/**\n * Takes a {@link Feature} and a bbox and clips the feature to the bbox using\n * [lineclip](https://github.com/mapbox/lineclip).\n * May result in degenerate edges when clipping Polygons.\n *\n * @function\n * @param {Feature<LineString|MultiLineString|Polygon|MultiPolygon>} feature feature to clip to the bbox\n * @param {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @returns {Feature<LineString|MultiLineString|Polygon|MultiPolygon>} clipped Feature\n * @example\n * var bbox = [0, 0, 10, 10];\n * var poly = turf.polygon([[[2, 2], [8, 4], [12, 8], [3, 7], [2, 2]]]);\n *\n * var clipped = turf.bboxClip(poly, bbox);\n *\n * //addToMap\n * var addToMap = [bbox, poly, clipped]\n */\nfunction bboxClip<\n  G extends Polygon | MultiPolygon | LineString | MultiLineString,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(feature: Feature<G, P> | G, bbox: BBox) {\n  const geom = getGeom(feature);\n  const type = geom.type;\n  const properties = feature.type === \"Feature\" ? feature.properties : {};\n  let coords: any[] = geom.coordinates;\n\n  switch (type) {\n    case \"LineString\":\n    case \"MultiLineString\": {\n      const lines: any[] = [];\n      if (type === \"LineString\") {\n        coords = [coords];\n      }\n      coords.forEach((line) => {\n        lineclip(line, bbox, lines);\n      });\n      if (lines.length === 1) {\n        return lineString(lines[0], properties);\n      }\n      return multiLineString(lines, properties);\n    }\n    case \"Polygon\":\n      return polygon(clipPolygon(coords, bbox), properties);\n    case \"MultiPolygon\":\n      return multiPolygon(\n        coords.map((poly) => {\n          return clipPolygon(poly, bbox);\n        }),\n        properties\n      );\n    default:\n      throw new Error(\"geometry \" + type + \" not supported\");\n  }\n}\n\nfunction clipPolygon(rings: number[][][], bbox: BBox) {\n  const outRings = [];\n  for (const ring of rings) {\n    const clipped = polygonclip(ring, bbox);\n    if (clipped.length > 0) {\n      if (\n        clipped[0][0] !== clipped[clipped.length - 1][0] ||\n        clipped[0][1] !== clipped[clipped.length - 1][1]\n      ) {\n        clipped.push(clipped[0]);\n      }\n      if (clipped.length >= 4) {\n        outRings.push(clipped);\n      }\n    }\n  }\n  return outRings;\n}\n\nexport { bboxClip };\nexport default bboxClip;\n", "// <PERSON><PERSON> line clipping algorithm, adapted to efficiently\n// handle polylines rather than just segments\nimport { BBox } from \"geojson\";\n\nexport function lineclip(\n  points: number[][],\n  bbox: BBox,\n  result?: number[][][]\n): number[][][] {\n  var len = points.length,\n    codeA = bitCode(points[0], bbox),\n    part = [] as number[][],\n    i,\n    codeB,\n    lastCode;\n  let a: number[];\n  let b: number[];\n\n  if (!result) result = [];\n\n  for (i = 1; i < len; i++) {\n    a = points[i - 1];\n    b = points[i];\n    codeB = lastCode = bitCode(b, bbox);\n\n    while (true) {\n      if (!(codeA | codeB)) {\n        // accept\n        part.push(a);\n\n        if (codeB !== lastCode) {\n          // segment went outside\n          part.push(b);\n\n          if (i < len - 1) {\n            // start a new line\n            result.push(part);\n            part = [];\n          }\n        } else if (i === len - 1) {\n          part.push(b);\n        }\n        break;\n      } else if (codeA & codeB) {\n        // trivial reject\n        break;\n      } else if (codeA) {\n        // a outside, intersect with clip edge\n        a = intersect(a, b, codeA, bbox)!;\n        codeA = bitCode(a, bbox);\n      } else {\n        // b outside\n        b = intersect(a, b, codeB, bbox)!;\n        codeB = bitCode(b, bbox);\n      }\n    }\n\n    codeA = lastCode;\n  }\n\n  if (part.length) result.push(part);\n\n  return result;\n}\n\n// Sutherland-Hodgeman polygon clipping algorithm\n\nexport function polygonclip(points: number[][], bbox: BBox): number[][] {\n  var result: number[][], edge, prev, prevInside, i, p, inside;\n\n  // clip against each side of the clip rectangle\n  for (edge = 1; edge <= 8; edge *= 2) {\n    result = [];\n    prev = points[points.length - 1];\n    prevInside = !(bitCode(prev, bbox) & edge);\n\n    for (i = 0; i < points.length; i++) {\n      p = points[i];\n      inside = !(bitCode(p, bbox) & edge);\n\n      // if segment goes through the clip window, add an intersection\n      if (inside !== prevInside) result.push(intersect(prev, p, edge, bbox)!);\n\n      if (inside) result.push(p); // add a point if it's inside\n\n      prev = p;\n      prevInside = inside;\n    }\n\n    points = result;\n\n    if (!points.length) break;\n  }\n\n  return result!;\n}\n\n// intersect a segment against one of the 4 lines that make up the bbox\n\nfunction intersect(\n  a: number[],\n  b: number[],\n  edge: number,\n  bbox: BBox\n): number[] | null {\n  return edge & 8\n    ? [a[0] + ((b[0] - a[0]) * (bbox[3] - a[1])) / (b[1] - a[1]), bbox[3]] // top\n    : edge & 4\n      ? [a[0] + ((b[0] - a[0]) * (bbox[1] - a[1])) / (b[1] - a[1]), bbox[1]] // bottom\n      : edge & 2\n        ? [bbox[2], a[1] + ((b[1] - a[1]) * (bbox[2] - a[0])) / (b[0] - a[0])] // right\n        : edge & 1\n          ? [bbox[0], a[1] + ((b[1] - a[1]) * (bbox[0] - a[0])) / (b[0] - a[0])] // left\n          : null;\n}\n\n// bit code reflects the point position relative to the bbox:\n\n//         left  mid  right\n//    top  1001  1000  1010\n//    mid  0001  0000  0010\n// bottom  0101  0100  0110\n\nfunction bitCode(p: number[], bbox: BBox) {\n  var code = 0;\n\n  if (p[0] < bbox[0]) code |= 1;\n  // left\n  else if (p[0] > bbox[2]) code |= 2; // right\n\n  if (p[1] < bbox[1]) code |= 4;\n  // bottom\n  else if (p[1] > bbox[3]) code |= 8; // top\n\n  return code;\n}\n"], "mappings": ";AAUA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,eAAe;;;ACZjB,SAAS,SACd,QACA,MACA,QACc;AACd,MAAI,MAAM,OAAO,QACf,QAAQ,QAAQ,OAAO,CAAC,GAAG,IAAI,GAC/B,OAAO,CAAC,GACR,GACA,OACA;AACF,MAAI;AACJ,MAAI;AAEJ,MAAI,CAAC,OAAQ,UAAS,CAAC;AAEvB,OAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,QAAI,OAAO,IAAI,CAAC;AAChB,QAAI,OAAO,CAAC;AACZ,YAAQ,WAAW,QAAQ,GAAG,IAAI;AAElC,WAAO,MAAM;AACX,UAAI,EAAE,QAAQ,QAAQ;AAEpB,aAAK,KAAK,CAAC;AAEX,YAAI,UAAU,UAAU;AAEtB,eAAK,KAAK,CAAC;AAEX,cAAI,IAAI,MAAM,GAAG;AAEf,mBAAO,KAAK,IAAI;AAChB,mBAAO,CAAC;AAAA,UACV;AAAA,QACF,WAAW,MAAM,MAAM,GAAG;AACxB,eAAK,KAAK,CAAC;AAAA,QACb;AACA;AAAA,MACF,WAAW,QAAQ,OAAO;AAExB;AAAA,MACF,WAAW,OAAO;AAEhB,YAAI,UAAU,GAAG,GAAG,OAAO,IAAI;AAC/B,gBAAQ,QAAQ,GAAG,IAAI;AAAA,MACzB,OAAO;AAEL,YAAI,UAAU,GAAG,GAAG,OAAO,IAAI;AAC/B,gBAAQ,QAAQ,GAAG,IAAI;AAAA,MACzB;AAAA,IACF;AAEA,YAAQ;AAAA,EACV;AAEA,MAAI,KAAK,OAAQ,QAAO,KAAK,IAAI;AAEjC,SAAO;AACT;AAIO,SAAS,YAAY,QAAoB,MAAwB;AACtE,MAAI,QAAoB,MAAM,MAAM,YAAY,GAAG,GAAG;AAGtD,OAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG;AACnC,aAAS,CAAC;AACV,WAAO,OAAO,OAAO,SAAS,CAAC;AAC/B,iBAAa,EAAE,QAAQ,MAAM,IAAI,IAAI;AAErC,SAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,UAAI,OAAO,CAAC;AACZ,eAAS,EAAE,QAAQ,GAAG,IAAI,IAAI;AAG9B,UAAI,WAAW,WAAY,QAAO,KAAK,UAAU,MAAM,GAAG,MAAM,IAAI,CAAE;AAEtE,UAAI,OAAQ,QAAO,KAAK,CAAC;AAEzB,aAAO;AACP,mBAAa;AAAA,IACf;AAEA,aAAS;AAET,QAAI,CAAC,OAAO,OAAQ;AAAA,EACtB;AAEA,SAAO;AACT;AAIA,SAAS,UACP,GACA,GACA,MACA,MACiB;AACjB,SAAO,OAAO,IACV,CAAC,EAAE,CAAC,KAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,MAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,IACnE,OAAO,IACL,CAAC,EAAE,CAAC,KAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,MAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,IACnE,OAAO,IACL,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,MAAO,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IACnE,OAAO,IACL,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,MAAO,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IACnE;AACZ;AASA,SAAS,QAAQ,GAAa,MAAY;AACxC,MAAI,OAAO;AAEX,MAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAG,SAAQ;AAAA,WAEnB,EAAE,CAAC,IAAI,KAAK,CAAC,EAAG,SAAQ;AAEjC,MAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAG,SAAQ;AAAA,WAEnB,EAAE,CAAC,IAAI,KAAK,CAAC,EAAG,SAAQ;AAEjC,SAAO;AACT;;;ADlGA,SAAS,SAGP,SAA4B,MAAY;AACxC,QAAM,OAAO,QAAQ,OAAO;AAC5B,QAAM,OAAO,KAAK;AAClB,QAAM,aAAa,QAAQ,SAAS,YAAY,QAAQ,aAAa,CAAC;AACtE,MAAI,SAAgB,KAAK;AAEzB,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK,mBAAmB;AACtB,YAAM,QAAe,CAAC;AACtB,UAAI,SAAS,cAAc;AACzB,iBAAS,CAAC,MAAM;AAAA,MAClB;AACA,aAAO,QAAQ,CAAC,SAAS;AACvB,iBAAS,MAAM,MAAM,KAAK;AAAA,MAC5B,CAAC;AACD,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO,WAAW,MAAM,CAAC,GAAG,UAAU;AAAA,MACxC;AACA,aAAO,gBAAgB,OAAO,UAAU;AAAA,IAC1C;AAAA,IACA,KAAK;AACH,aAAO,QAAQ,YAAY,QAAQ,IAAI,GAAG,UAAU;AAAA,IACtD,KAAK;AACH,aAAO;AAAA,QACL,OAAO,IAAI,CAAC,SAAS;AACnB,iBAAO,YAAY,MAAM,IAAI;AAAA,QAC/B,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AACE,YAAM,IAAI,MAAM,cAAc,OAAO,gBAAgB;AAAA,EACzD;AACF;AAEA,SAAS,YAAY,OAAqB,MAAY;AACpD,QAAM,WAAW,CAAC;AAClB,aAAW,QAAQ,OAAO;AACxB,UAAM,UAAU,YAAY,MAAM,IAAI;AACtC,QAAI,QAAQ,SAAS,GAAG;AACtB,UACE,QAAQ,CAAC,EAAE,CAAC,MAAM,QAAQ,QAAQ,SAAS,CAAC,EAAE,CAAC,KAC/C,QAAQ,CAAC,EAAE,CAAC,MAAM,QAAQ,QAAQ,SAAS,CAAC,EAAE,CAAC,GAC/C;AACA,gBAAQ,KAAK,QAAQ,CAAC,CAAC;AAAA,MACzB;AACA,UAAI,QAAQ,UAAU,GAAG;AACvB,iBAAS,KAAK,OAAO;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAO,yBAAQ;", "names": []}