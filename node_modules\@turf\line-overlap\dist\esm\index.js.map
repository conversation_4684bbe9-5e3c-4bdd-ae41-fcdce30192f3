{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { geojsonRbush as rbush } from \"@turf/geojson-rbush\";\nimport { lineSegment } from \"@turf/line-segment\";\nimport { nearestPointOnLine } from \"@turf/nearest-point-on-line\";\nimport { booleanPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { getCoords } from \"@turf/invariant\";\nimport { featureEach, segmentEach } from \"@turf/meta\";\nimport {\n  FeatureCollection,\n  Feature,\n  LineString,\n  MultiLineString,\n  Polygon,\n  MultiPolygon,\n  GeoJsonProperties,\n} from \"geojson\";\nimport { featureCollection, isObject } from \"@turf/helpers\";\nimport equal from \"fast-deep-equal\";\n\n/**\n * Takes any LineString or Polygon and returns the overlapping lines between both features.\n *\n * @function\n * @param {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} line1 any LineString or Polygon\n * @param {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} line2 any LineString or Polygon\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.tolerance=0] Tolerance distance to match overlapping line segments (in kilometers)\n * @returns {FeatureCollection<LineString>} lines(s) that are overlapping between both features\n * @example\n * var line1 = turf.lineString([[115, -35], [125, -30], [135, -30], [145, -35]]);\n * var line2 = turf.lineString([[115, -25], [125, -30], [135, -30], [145, -25]]);\n *\n * var overlapping = turf.lineOverlap(line1, line2);\n *\n * //addToMap\n * var addToMap = [line1, line2, overlapping]\n */\nfunction lineOverlap<\n  G1 extends LineString | MultiLineString | Polygon | MultiPolygon,\n  G2 extends LineString | MultiLineString | Polygon | MultiPolygon,\n>(\n  line1: Feature<G1> | G1,\n  line2: Feature<G2> | G2,\n  options: { tolerance?: number } = {}\n): FeatureCollection<LineString> {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var tolerance = options.tolerance || 0;\n\n  // Containers\n  var features: Feature<LineString, GeoJsonProperties>[] = [];\n\n  // Create Spatial Index\n  var tree = rbush<LineString>();\n\n  // To-Do -- HACK way to support typescript\n  const line: any = lineSegment(line1);\n  tree.load(line);\n  var overlapSegment: Feature<LineString> | undefined;\n  let additionalSegments: Feature<LineString>[] = [];\n\n  // Line Intersection\n\n  // Iterate over line segments\n  segmentEach(line2, function (segment) {\n    var doesOverlaps = false;\n\n    if (!segment) {\n      return;\n    }\n\n    // Iterate over each segments which falls within the same bounds\n    featureEach(tree.search(segment), function (match) {\n      if (doesOverlaps === false) {\n        var coordsSegment = getCoords(segment).sort();\n        var coordsMatch: any = getCoords(match).sort();\n\n        // Segment overlaps feature\n        if (equal(coordsSegment, coordsMatch)) {\n          doesOverlaps = true;\n          // Overlaps already exists - only append last coordinate of segment\n          if (overlapSegment) {\n            overlapSegment =\n              concatSegment(overlapSegment, segment) || overlapSegment;\n          } else overlapSegment = segment;\n          // Match segments which don't share nodes (Issue #901)\n        } else if (\n          tolerance === 0\n            ? booleanPointOnLine(coordsSegment[0], match) &&\n              booleanPointOnLine(coordsSegment[1], match)\n            : nearestPointOnLine(match, coordsSegment[0]).properties.dist! <=\n                tolerance &&\n              nearestPointOnLine(match, coordsSegment[1]).properties.dist! <=\n                tolerance\n        ) {\n          doesOverlaps = true;\n          if (overlapSegment) {\n            overlapSegment =\n              concatSegment(overlapSegment, segment) || overlapSegment;\n          } else overlapSegment = segment;\n        } else if (\n          tolerance === 0\n            ? booleanPointOnLine(coordsMatch[0], segment) &&\n              booleanPointOnLine(coordsMatch[1], segment)\n            : nearestPointOnLine(segment, coordsMatch[0]).properties.dist! <=\n                tolerance &&\n              nearestPointOnLine(segment, coordsMatch[1]).properties.dist! <=\n                tolerance\n        ) {\n          // Do not define (doesOverlap = true) since more matches can occur within the same segment\n          // doesOverlaps = true;\n          if (overlapSegment) {\n            const combinedSegment = concatSegment(overlapSegment, match);\n            if (combinedSegment) {\n              overlapSegment = combinedSegment;\n            } else {\n              additionalSegments.push(match);\n            }\n          } else overlapSegment = match;\n        }\n      }\n    });\n\n    // Segment doesn't overlap - add overlaps to results & reset\n    if (doesOverlaps === false && overlapSegment) {\n      features.push(overlapSegment);\n      if (additionalSegments.length) {\n        features = features.concat(additionalSegments);\n        additionalSegments = [];\n      }\n      overlapSegment = undefined;\n    }\n  });\n  // Add last segment if exists\n  if (overlapSegment) features.push(overlapSegment);\n\n  return featureCollection(features);\n}\n\n/**\n * Concat Segment\n *\n * @private\n * @param {Feature<LineString>} line LineString\n * @param {Feature<LineString>} segment 2-vertex LineString\n * @returns {Feature<LineString>} concat linestring\n */\nfunction concatSegment(\n  line: Feature<LineString>,\n  segment: Feature<LineString>\n) {\n  var coords = getCoords(segment);\n  var lineCoords = getCoords(line);\n  var start = lineCoords[0];\n  var end = lineCoords[lineCoords.length - 1];\n  var geom = line.geometry.coordinates;\n\n  if (equal(coords[0], start)) geom.unshift(coords[1]);\n  else if (equal(coords[0], end)) geom.push(coords[1]);\n  else if (equal(coords[1], start)) geom.unshift(coords[0]);\n  else if (equal(coords[1], end)) geom.push(coords[0]);\n  else return; // If the overlap leaves the segment unchanged, return undefined so that this can be identified.\n\n  // Otherwise return the mutated line.\n  return line;\n}\n\nexport { lineOverlap };\nexport default lineOverlap;\n"], "mappings": ";AAAA,SAAS,gBAAgB,aAAa;AACtC,SAAS,mBAAmB;AAC5B,SAAS,0BAA0B;AACnC,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AAC1B,SAAS,aAAa,mBAAmB;AAUzC,SAAS,mBAAmB,gBAAgB;AAC5C,OAAO,WAAW;AAoBlB,SAAS,YAIP,OACA,OACA,UAAkC,CAAC,GACJ;AAE/B,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,MAAI,YAAY,QAAQ,aAAa;AAGrC,MAAI,WAAqD,CAAC;AAG1D,MAAI,OAAO,MAAkB;AAG7B,QAAM,OAAY,YAAY,KAAK;AACnC,OAAK,KAAK,IAAI;AACd,MAAI;AACJ,MAAI,qBAA4C,CAAC;AAKjD,cAAY,OAAO,SAAU,SAAS;AACpC,QAAI,eAAe;AAEnB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAGA,gBAAY,KAAK,OAAO,OAAO,GAAG,SAAU,OAAO;AACjD,UAAI,iBAAiB,OAAO;AAC1B,YAAI,gBAAgB,UAAU,OAAO,EAAE,KAAK;AAC5C,YAAI,cAAmB,UAAU,KAAK,EAAE,KAAK;AAG7C,YAAI,MAAM,eAAe,WAAW,GAAG;AACrC,yBAAe;AAEf,cAAI,gBAAgB;AAClB,6BACE,cAAc,gBAAgB,OAAO,KAAK;AAAA,UAC9C,MAAO,kBAAiB;AAAA,QAE1B,WACE,cAAc,IACV,mBAAmB,cAAc,CAAC,GAAG,KAAK,KAC1C,mBAAmB,cAAc,CAAC,GAAG,KAAK,IAC1C,mBAAmB,OAAO,cAAc,CAAC,CAAC,EAAE,WAAW,QACrD,aACF,mBAAmB,OAAO,cAAc,CAAC,CAAC,EAAE,WAAW,QACrD,WACN;AACA,yBAAe;AACf,cAAI,gBAAgB;AAClB,6BACE,cAAc,gBAAgB,OAAO,KAAK;AAAA,UAC9C,MAAO,kBAAiB;AAAA,QAC1B,WACE,cAAc,IACV,mBAAmB,YAAY,CAAC,GAAG,OAAO,KAC1C,mBAAmB,YAAY,CAAC,GAAG,OAAO,IAC1C,mBAAmB,SAAS,YAAY,CAAC,CAAC,EAAE,WAAW,QACrD,aACF,mBAAmB,SAAS,YAAY,CAAC,CAAC,EAAE,WAAW,QACrD,WACN;AAGA,cAAI,gBAAgB;AAClB,kBAAM,kBAAkB,cAAc,gBAAgB,KAAK;AAC3D,gBAAI,iBAAiB;AACnB,+BAAiB;AAAA,YACnB,OAAO;AACL,iCAAmB,KAAK,KAAK;AAAA,YAC/B;AAAA,UACF,MAAO,kBAAiB;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,CAAC;AAGD,QAAI,iBAAiB,SAAS,gBAAgB;AAC5C,eAAS,KAAK,cAAc;AAC5B,UAAI,mBAAmB,QAAQ;AAC7B,mBAAW,SAAS,OAAO,kBAAkB;AAC7C,6BAAqB,CAAC;AAAA,MACxB;AACA,uBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AAED,MAAI,eAAgB,UAAS,KAAK,cAAc;AAEhD,SAAO,kBAAkB,QAAQ;AACnC;AAUA,SAAS,cACP,MACA,SACA;AACA,MAAI,SAAS,UAAU,OAAO;AAC9B,MAAI,aAAa,UAAU,IAAI;AAC/B,MAAI,QAAQ,WAAW,CAAC;AACxB,MAAI,MAAM,WAAW,WAAW,SAAS,CAAC;AAC1C,MAAI,OAAO,KAAK,SAAS;AAEzB,MAAI,MAAM,OAAO,CAAC,GAAG,KAAK,EAAG,MAAK,QAAQ,OAAO,CAAC,CAAC;AAAA,WAC1C,MAAM,OAAO,CAAC,GAAG,GAAG,EAAG,MAAK,KAAK,OAAO,CAAC,CAAC;AAAA,WAC1C,MAAM,OAAO,CAAC,GAAG,KAAK,EAAG,MAAK,QAAQ,OAAO,CAAC,CAAC;AAAA,WAC/C,MAAM,OAAO,CAAC,GAAG,GAAG,EAAG,MAAK,KAAK,OAAO,CAAC,CAAC;AAAA,MAC9C;AAGL,SAAO;AACT;AAGA,IAAO,4BAAQ;", "names": []}