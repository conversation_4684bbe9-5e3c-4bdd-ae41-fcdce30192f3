{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, Geometry, LineString, Point } from \"geojson\";\nimport { booleanPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Boolean-touches true if none of the points common to both geometries\n * intersect the interiors of both geometries.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n * var point = turf.point([1, 1]);\n *\n * turf.booleanTouches(point, line);\n * //=true\n */\nfunction booleanTouches(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  var geom1 = getGeom(feature1);\n  var geom2 = getGeom(feature2);\n  var type1 = geom1.type;\n  var type2 = geom2.type;\n\n  switch (type1) {\n    case \"Point\":\n      switch (type2) {\n        case \"LineString\":\n          return isPointOnLineEnd(geom1, geom2);\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n            if (\n              isPointOnLineEnd(geom1, {\n                type: \"LineString\",\n                coordinates: geom2.coordinates[ii],\n              })\n            )\n              foundTouchingPoint = true;\n          }\n          return foundTouchingPoint;\n        case \"Polygon\":\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (\n              booleanPointOnLine(geom1, {\n                type: \"LineString\",\n                coordinates: geom2.coordinates[i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPolygon\":\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates[i].length; ii++) {\n              if (\n                booleanPointOnLine(geom1, {\n                  type: \"LineString\",\n                  coordinates: geom2.coordinates[i][ii],\n                })\n              )\n                return true;\n            }\n          }\n          return false;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"LineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                isPointOnLineEnd(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  geom2\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointOnLine(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreEndVertices: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  isPointOnLineEnd(\n                    { type: \"Point\", coordinates: geom1.coordinates[i] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[ii] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[ii] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i] },\n                    {\n                      type: \"LineString\",\n                      coordinates: geom2.coordinates[ii][0],\n                    }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"Polygon\", coordinates: geom2.coordinates[ii] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"Point\":\n          return isPointOnLineEnd(geom2, geom1);\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                isPointOnLineEnd(\n                  { type: \"Point\", coordinates: geom2.coordinates[i] },\n                  geom1\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointOnLine(\n                { type: \"Point\", coordinates: geom2.coordinates[i] },\n                geom1,\n                { ignoreEndVertices: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var endMatch = false;\n          if (\n            isPointOnLineEnd(\n              { type: \"Point\", coordinates: geom1.coordinates[0] },\n              geom2\n            )\n          )\n            endMatch = true;\n          if (\n            isPointOnLineEnd(\n              {\n                type: \"Point\",\n                coordinates: geom1.coordinates[geom1.coordinates.length - 1],\n              },\n              geom2\n            )\n          )\n            endMatch = true;\n          if (endMatch === false) return false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              booleanPointOnLine(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreEndVertices: true }\n              )\n            )\n              return false;\n          }\n          return endMatch;\n        case \"MultiLineString\":\n          var endMatch = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (\n              isPointOnLineEnd(\n                { type: \"Point\", coordinates: geom1.coordinates[0] },\n                { type: \"LineString\", coordinates: geom2.coordinates[i] }\n              )\n            )\n              endMatch = true;\n            if (\n              isPointOnLineEnd(\n                {\n                  type: \"Point\",\n                  coordinates: geom1.coordinates[geom1.coordinates.length - 1],\n                },\n                { type: \"LineString\", coordinates: geom2.coordinates[i] }\n              )\n            )\n              endMatch = true;\n            for (var ii = 0; ii < geom1.coordinates[i].length; ii++) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[ii] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[i] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return endMatch;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i] },\n                    {\n                      type: \"LineString\",\n                      coordinates: geom2.coordinates[ii][0],\n                    }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiLineString\":\n      switch (type2) {\n        case \"Point\":\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              isPointOnLineEnd(geom2, {\n                type: \"LineString\",\n                coordinates: geom1.coordinates[i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  isPointOnLineEnd(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[ii] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[ii] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var endMatch = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              isPointOnLineEnd(\n                { type: \"Point\", coordinates: geom1.coordinates[i][0] },\n                geom2\n              )\n            )\n              endMatch = true;\n            if (\n              isPointOnLineEnd(\n                {\n                  type: \"Point\",\n                  coordinates:\n                    geom1.coordinates[i][geom1.coordinates[i].length - 1],\n                },\n                geom2\n              )\n            )\n              endMatch = true;\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[i] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return endMatch;\n        case \"MultiLineString\":\n          var endMatch = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (\n                isPointOnLineEnd(\n                  { type: \"Point\", coordinates: geom1.coordinates[i][0] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[ii] }\n                )\n              )\n                endMatch = true;\n              if (\n                isPointOnLineEnd(\n                  {\n                    type: \"Point\",\n                    coordinates:\n                      geom1.coordinates[i][geom1.coordinates[i].length - 1],\n                  },\n                  { type: \"LineString\", coordinates: geom2.coordinates[ii] }\n                )\n              )\n                endMatch = true;\n              for (var iii = 0; iii < geom1.coordinates[i].length; iii++) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i][iii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[ii] },\n                    { ignoreEndVertices: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n          return endMatch;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom1.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i][ii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[i][ii] },\n                  geom2,\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom1.coordinates.length; ii++) {\n              for (var iii = 0; iii < geom1.coordinates[ii].length; iii++) {\n                if (!foundTouchingPoint) {\n                  if (\n                    booleanPointOnLine(\n                      {\n                        type: \"Point\",\n                        coordinates: geom1.coordinates[ii][iii],\n                      },\n                      {\n                        type: \"LineString\",\n                        coordinates: geom2.coordinates[0][i],\n                      }\n                    )\n                  )\n                    foundTouchingPoint = true;\n                }\n                if (\n                  booleanPointInPolygon(\n                    { type: \"Point\", coordinates: geom1.coordinates[ii][iii] },\n                    { type: \"Polygon\", coordinates: [geom2.coordinates[0][i]] },\n                    { ignoreBoundary: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"Point\":\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              booleanPointOnLine(geom2, {\n                type: \"LineString\",\n                coordinates: geom1.coordinates[i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom2.coordinates[i] },\n                geom1,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom2.coordinates[i] },\n                geom1,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates[i].length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom2.coordinates[i][ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[0] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom2.coordinates[i][ii] },\n                  geom1,\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[0][i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[0][i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom1.coordinates[0].length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[0][ii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[0][i] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[0][ii] },\n                  { type: \"Polygon\", coordinates: geom2.coordinates[0][i] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPolygon\":\n      switch (type2) {\n        case \"Point\":\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            if (\n              booleanPointOnLine(geom2, {\n                type: \"LineString\",\n                coordinates: geom1.coordinates[0][i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[0][i] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"Polygon\", coordinates: geom1.coordinates[0][i] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[0][i] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"Polygon\", coordinates: geom1.coordinates[0][i] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              for (var iii = 0; iii < geom2.coordinates[ii].length; iii++) {\n                if (!foundTouchingPoint) {\n                  if (\n                    booleanPointOnLine(\n                      {\n                        type: \"Point\",\n                        coordinates: geom2.coordinates[ii][iii],\n                      },\n                      {\n                        type: \"LineString\",\n                        coordinates: geom1.coordinates[i][0],\n                      }\n                    )\n                  )\n                    foundTouchingPoint = true;\n                }\n                if (\n                  booleanPointInPolygon(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii][iii] },\n                    { type: \"Polygon\", coordinates: [geom1.coordinates[i][0]] },\n                    { ignoreBoundary: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n\n          return foundTouchingPoint;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom1.coordinates[0][i].length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[0][i][ii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[0][i][ii] },\n                  geom2,\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom2.coordinates[0].length; ii++) {\n              for (var iii = 0; iii < geom1.coordinates[0].length; iii++) {\n                if (!foundTouchingPoint) {\n                  if (\n                    booleanPointOnLine(\n                      {\n                        type: \"Point\",\n                        coordinates: geom1.coordinates[0][i][iii],\n                      },\n                      {\n                        type: \"LineString\",\n                        coordinates: geom2.coordinates[0][ii],\n                      }\n                    )\n                  )\n                    foundTouchingPoint = true;\n                }\n                if (\n                  booleanPointInPolygon(\n                    {\n                      type: \"Point\",\n                      coordinates: geom1.coordinates[0][i][iii],\n                    },\n                    { type: \"Polygon\", coordinates: geom2.coordinates[0][ii] },\n                    { ignoreBoundary: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction isPointOnLineEnd(point: Point, line: LineString) {\n  if (compareCoords(line.coordinates[0], point.coordinates)) return true;\n  if (\n    compareCoords(\n      line.coordinates[line.coordinates.length - 1],\n      point.coordinates\n    )\n  )\n    return true;\n  return false;\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true/false if coord pairs match\n */\nfunction compareCoords(pair1: number[], pair2: number[]) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\nexport { booleanTouches };\nexport default booleanTouches;\n"], "mappings": ";AACA,SAAS,0BAA0B;AACnC,SAAS,6BAA6B;AACtC,SAAS,eAAe;AAiBxB,SAAS,eACP,UACA,UACS;AACT,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,QAAQ,MAAM;AAClB,MAAI,QAAQ,MAAM;AAElB,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,iBAAiB,OAAO,KAAK;AAAA,QACtC,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,gBACE,iBAAiB,OAAO;AAAA,cACtB,MAAM;AAAA,cACN,aAAa,MAAM,YAAY,EAAE;AAAA,YACnC,CAAC;AAED,mCAAqB;AAAA,UACzB;AACA,iBAAO;AAAA,QACT,KAAK;AACH,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBACE,mBAAmB,OAAO;AAAA,cACxB,MAAM;AAAA,cACN,aAAa,MAAM,YAAY,CAAC;AAAA,YAClC,CAAC;AAED,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM;AACvD,kBACE,mBAAmB,OAAO;AAAA,gBACxB,MAAM;AAAA,gBACN,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE;AAAA,cACtC,CAAC;AAED,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBAAI,CAAC,oBAAoB;AACvB,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD;AAAA,cACF;AAEA,qCAAqB;AAAA,YACzB;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,mBAAmB,KAAK;AAAA,YAC5B;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,kBACnD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBAC3D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACzD,EAAE,mBAAmB,KAAK;AAAA,cAC5B;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBAAI,CAAC,oBAAoB;AACvB,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cAC1D;AAEA,qCAAqB;AAAA,YACzB;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,gBAAgB,KAAK;AAAA,YACzB;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,kBACnD;AAAA,oBACE,MAAM;AAAA,oBACN,aAAa,MAAM,YAAY,EAAE,EAAE,CAAC;AAAA,kBACtC;AAAA,gBACF;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD,EAAE,MAAM,WAAW,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACtD,EAAE,gBAAgB,KAAK;AAAA,cACzB;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,iBAAiB,OAAO,KAAK;AAAA,QACtC,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBAAI,CAAC,oBAAoB;AACvB,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD;AAAA,cACF;AAEA,qCAAqB;AAAA,YACzB;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,mBAAmB,KAAK;AAAA,YAC5B;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,WAAW;AACf,cACE;AAAA,YACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,YACnD;AAAA,UACF;AAEA,uBAAW;AACb,cACE;AAAA,YACE;AAAA,cACE,MAAM;AAAA,cACN,aAAa,MAAM,YAAY,MAAM,YAAY,SAAS,CAAC;AAAA,YAC7D;AAAA,YACA;AAAA,UACF;AAEA,uBAAW;AACb,cAAI,aAAa,MAAO,QAAO;AAC/B,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,mBAAmB,KAAK;AAAA,YAC5B;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,WAAW;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,YAC1D;AAEA,yBAAW;AACb,gBACE;AAAA,cACE;AAAA,gBACE,MAAM;AAAA,gBACN,aAAa,MAAM,YAAY,MAAM,YAAY,SAAS,CAAC;AAAA,cAC7D;AAAA,cACA,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,YAC1D;AAEA,yBAAW;AACb,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM;AACvD,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACpD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACxD,EAAE,mBAAmB,KAAK;AAAA,cAC5B;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBAAI,CAAC,oBAAoB;AACvB,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cAC1D;AAEA,qCAAqB;AAAA,YACzB;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,gBAAgB,KAAK;AAAA,YACzB;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,kBACnD;AAAA,oBACE,MAAM;AAAA,oBACN,aAAa,MAAM,YAAY,EAAE,EAAE,CAAC;AAAA,kBACtC;AAAA,gBACF;AAEA,uCAAqB;AAAA,cACzB;AAAA,YACF;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,gBAAgB,KAAK;AAAA,YACzB;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBACE,iBAAiB,OAAO;AAAA,cACtB,MAAM;AAAA,cACN,aAAa,MAAM,YAAY,CAAC;AAAA,YAClC,CAAC;AAED,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,kBACpD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBAC3D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACpD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACzD,EAAE,mBAAmB,KAAK;AAAA,cAC5B;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,WAAW;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,cACtD;AAAA,YACF;AAEA,yBAAW;AACb,gBACE;AAAA,cACE;AAAA,gBACE,MAAM;AAAA,gBACN,aACE,MAAM,YAAY,CAAC,EAAE,MAAM,YAAY,CAAC,EAAE,SAAS,CAAC;AAAA,cACxD;AAAA,cACA;AAAA,YACF;AAEA,yBAAW;AACb,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACpD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACxD,EAAE,mBAAmB,KAAK;AAAA,cAC5B;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,WAAW;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBACtD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,cAC3D;AAEA,2BAAW;AACb,kBACE;AAAA,gBACE;AAAA,kBACE,MAAM;AAAA,kBACN,aACE,MAAM,YAAY,CAAC,EAAE,MAAM,YAAY,CAAC,EAAE,SAAS,CAAC;AAAA,gBACxD;AAAA,gBACA,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,cAC3D;AAEA,2BAAW;AACb,uBAAS,MAAM,GAAG,MAAM,MAAM,YAAY,CAAC,EAAE,QAAQ,OAAO;AAC1D,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,GAAG,EAAE;AAAA,kBACxD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,kBACzD,EAAE,mBAAmB,KAAK;AAAA,gBAC5B;AAEA,yBAAO;AAAA,cACX;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE;AAAA,kBACvD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBAC1D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE;AAAA,gBACvD;AAAA,gBACA,EAAE,gBAAgB,KAAK;AAAA,cACzB;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,uBAAS,MAAM,GAAG,MAAM,MAAM,YAAY,EAAE,EAAE,QAAQ,OAAO;AAC3D,oBAAI,CAAC,oBAAoB;AACvB,sBACE;AAAA,oBACE;AAAA,sBACE,MAAM;AAAA,sBACN,aAAa,MAAM,YAAY,EAAE,EAAE,GAAG;AAAA,oBACxC;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC;AAAA,oBACrC;AAAA,kBACF;AAEA,yCAAqB;AAAA,gBACzB;AACA,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE,GAAG,EAAE;AAAA,kBACzD,EAAE,MAAM,WAAW,aAAa,CAAC,MAAM,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;AAAA,kBAC1D,EAAE,gBAAgB,KAAK;AAAA,gBACzB;AAEA,yBAAO;AAAA,cACX;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBACE,mBAAmB,OAAO;AAAA,cACxB,MAAM;AAAA,cACN,aAAa,MAAM,YAAY,CAAC;AAAA,YAClC,CAAC;AAED,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBAAI,CAAC,oBAAoB;AACvB,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cAC1D;AAEA,qCAAqB;AAAA,YACzB;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,gBAAgB,KAAK;AAAA,YACzB;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,gBAAI,CAAC,oBAAoB;AACvB,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBACnD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cAC1D;AAEA,qCAAqB;AAAA,YACzB;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cACnD;AAAA,cACA,EAAE,gBAAgB,KAAK;AAAA,YACzB;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM;AACvD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE;AAAA,kBACvD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBAC1D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE;AAAA,gBACvD;AAAA,gBACA,EAAE,gBAAgB,KAAK;AAAA,cACzB;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,gBAAI,CAAC,oBAAoB;AACvB,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBACtD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,cAC1D;AAEA,qCAAqB;AAAA,YACzB;AACA,gBACE;AAAA,cACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,cACtD;AAAA,cACA,EAAE,gBAAgB,KAAK;AAAA,YACzB;AAEA,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM;AACvD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE;AAAA,kBACvD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBAC7D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE;AAAA,gBACvD,EAAE,MAAM,WAAW,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBACxD,EAAE,gBAAgB,KAAK;AAAA,cACzB;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF,KAAK;AACH,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,gBACE,mBAAmB,OAAO;AAAA,cACxB,MAAM;AAAA,cACN,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC;AAAA,YACrC,CAAC;AAED,qBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,kBACpD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBAC7D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACpD,EAAE,MAAM,WAAW,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBACxD,EAAE,gBAAgB,KAAK;AAAA,cACzB;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,kBACpD,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBAC7D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE;AAAA,gBACpD,EAAE,MAAM,WAAW,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,gBACxD,EAAE,gBAAgB,KAAK;AAAA,cACzB;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AACjD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,QAAQ,MAAM;AACpD,uBAAS,MAAM,GAAG,MAAM,MAAM,YAAY,EAAE,EAAE,QAAQ,OAAO;AAC3D,oBAAI,CAAC,oBAAoB;AACvB,sBACE;AAAA,oBACE;AAAA,sBACE,MAAM;AAAA,sBACN,aAAa,MAAM,YAAY,EAAE,EAAE,GAAG;AAAA,oBACxC;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC;AAAA,oBACrC;AAAA,kBACF;AAEA,yCAAqB;AAAA,gBACzB;AACA,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,EAAE,EAAE,GAAG,EAAE;AAAA,kBACzD,EAAE,MAAM,WAAW,aAAa,CAAC,MAAM,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;AAAA,kBAC1D,EAAE,gBAAgB,KAAK;AAAA,gBACzB;AAEA,yBAAO;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,QAAQ,MAAM;AAC1D,kBAAI,CAAC,oBAAoB;AACvB,oBACE;AAAA,kBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;AAAA,kBAC1D,EAAE,MAAM,cAAc,aAAa,MAAM,YAAY,CAAC,EAAE;AAAA,gBAC1D;AAEA,uCAAqB;AAAA,cACzB;AACA,kBACE;AAAA,gBACE,EAAE,MAAM,SAAS,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;AAAA,gBAC1D;AAAA,gBACA,EAAE,gBAAgB,KAAK;AAAA,cACzB;AAEA,uBAAO;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT,KAAK;AACH,cAAI,qBAAqB;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,QAAQ,KAAK;AACpD,qBAAS,KAAK,GAAG,KAAK,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM;AACvD,uBAAS,MAAM,GAAG,MAAM,MAAM,YAAY,CAAC,EAAE,QAAQ,OAAO;AAC1D,oBAAI,CAAC,oBAAoB;AACvB,sBACE;AAAA,oBACE;AAAA,sBACE,MAAM;AAAA,sBACN,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG;AAAA,oBAC1C;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE;AAAA,oBACtC;AAAA,kBACF;AAEA,yCAAqB;AAAA,gBACzB;AACA,oBACE;AAAA,kBACE;AAAA,oBACE,MAAM;AAAA,oBACN,aAAa,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG;AAAA,kBAC1C;AAAA,kBACA,EAAE,MAAM,WAAW,aAAa,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE;AAAA,kBACzD,EAAE,gBAAgB,KAAK;AAAA,gBACzB;AAEA,yBAAO;AAAA,cACX;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACE,gBAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,MACnE;AAAA,IACF;AACE,YAAM,IAAI,MAAM,cAAc,QAAQ,yBAAyB;AAAA,EACnE;AACF;AAEA,SAAS,iBAAiB,OAAc,MAAkB;AACxD,MAAI,cAAc,KAAK,YAAY,CAAC,GAAG,MAAM,WAAW,EAAG,QAAO;AAClE,MACE;AAAA,IACE,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC;AAAA,IAC5C,MAAM;AAAA,EACR;AAEA,WAAO;AACT,SAAO;AACT;AAUA,SAAS,cAAc,OAAiB,OAAiB;AACvD,SAAO,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC;AACtD;AAGA,IAAO,+BAAQ;", "names": []}