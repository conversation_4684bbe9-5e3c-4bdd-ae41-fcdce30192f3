{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, Geometry, LineString, Position } from \"geojson\";\nimport { cleanCoords } from \"@turf/clean-coords\";\nimport { lineSegment } from \"@turf/line-segment\";\nimport { rhumbBearing } from \"@turf/rhumb-bearing\";\nimport { bearingToAzimuth } from \"@turf/helpers\";\n\n/**\n * <PERSON><PERSON><PERSON>-<PERSON><PERSON>l returns True if each segment of `line1` is parallel to the correspondent segment of `line2`\n *\n * @function\n * @param {Geometry|Feature<LineString>} line1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<LineString>} line2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false if the lines are parallel\n * @example\n * var line1 = turf.lineString([[0, 0], [0, 1]]);\n * var line2 = turf.lineString([[1, 0], [1, 1]]);\n *\n * turf.booleanParallel(line1, line2);\n * //=true\n */\nfunction booleanParallel(\n  line1: Feature<LineString> | LineString,\n  line2: Feature<LineString> | LineString\n): boolean {\n  // validation\n  if (!line1) throw new Error(\"line1 is required\");\n  if (!line2) throw new Error(\"line2 is required\");\n  var type1 = getType(line1, \"line1\");\n  if (type1 !== \"LineString\") throw new Error(\"line1 must be a LineString\");\n  var type2 = getType(line2, \"line2\");\n  if (type2 !== \"LineString\") throw new Error(\"line2 must be a LineString\");\n\n  var segments1 = lineSegment(cleanCoords(line1)).features;\n  var segments2 = lineSegment(cleanCoords(line2)).features;\n\n  for (var i = 0; i < segments1.length; i++) {\n    var segment1 = segments1[i].geometry.coordinates;\n    if (!segments2[i]) break;\n    var segment2 = segments2[i].geometry.coordinates;\n    if (!isParallel(segment1, segment2)) return false;\n  }\n  return true;\n}\n\n/**\n * Compares slopes and return result\n *\n * @private\n * @param {Geometry|Feature<LineString>} segment1 Geometry or Feature\n * @param {Geometry|Feature<LineString>} segment2 Geometry or Feature\n * @returns {boolean} if slopes are equal\n */\nfunction isParallel(segment1: Position[], segment2: Position[]) {\n  var slope1 = bearingToAzimuth(rhumbBearing(segment1[0], segment1[1]));\n  var slope2 = bearingToAzimuth(rhumbBearing(segment2[0], segment2[1]));\n  return slope1 === slope2 || (slope2 - slope1) % 180 === 0;\n}\n\n/**\n * Returns Feature's type\n *\n * @private\n * @param {Geometry|Feature<any>} geojson Geometry or Feature\n * @param {string} name of the variable\n * @returns {string} Feature's type\n */\nfunction getType(geojson: Geometry | Feature<any>, name: string) {\n  if ((geojson as Feature).geometry && (geojson as Feature).geometry.type)\n    return (geojson as Feature).geometry.type;\n  if (geojson.type) return geojson.type; // if GeoJSON geometry\n  throw new Error(\"Invalid GeoJSON object for \" + name);\n}\n\nexport { booleanParallel };\nexport default booleanParallel;\n"], "mappings": ";AACA,SAAS,mBAAmB;AAC5B,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AAgBjC,SAAS,gBACP,OACA,OACS;AAET,MAAI,CAAC,MAAO,OAAM,IAAI,MAAM,mBAAmB;AAC/C,MAAI,CAAC,MAAO,OAAM,IAAI,MAAM,mBAAmB;AAC/C,MAAI,QAAQ,QAAQ,OAAO,OAAO;AAClC,MAAI,UAAU,aAAc,OAAM,IAAI,MAAM,4BAA4B;AACxE,MAAI,QAAQ,QAAQ,OAAO,OAAO;AAClC,MAAI,UAAU,aAAc,OAAM,IAAI,MAAM,4BAA4B;AAExE,MAAI,YAAY,YAAY,YAAY,KAAK,CAAC,EAAE;AAChD,MAAI,YAAY,YAAY,YAAY,KAAK,CAAC,EAAE;AAEhD,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,WAAW,UAAU,CAAC,EAAE,SAAS;AACrC,QAAI,CAAC,UAAU,CAAC,EAAG;AACnB,QAAI,WAAW,UAAU,CAAC,EAAE,SAAS;AACrC,QAAI,CAAC,WAAW,UAAU,QAAQ,EAAG,QAAO;AAAA,EAC9C;AACA,SAAO;AACT;AAUA,SAAS,WAAW,UAAsB,UAAsB;AAC9D,MAAI,SAAS,iBAAiB,aAAa,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACpE,MAAI,SAAS,iBAAiB,aAAa,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACpE,SAAO,WAAW,WAAW,SAAS,UAAU,QAAQ;AAC1D;AAUA,SAAS,QAAQ,SAAkC,MAAc;AAC/D,MAAK,QAAoB,YAAa,QAAoB,SAAS;AACjE,WAAQ,QAAoB,SAAS;AACvC,MAAI,QAAQ,KAAM,QAAO,QAAQ;AACjC,QAAM,IAAI,MAAM,gCAAgC,IAAI;AACtD;AAGA,IAAO,gCAAQ;", "names": []}