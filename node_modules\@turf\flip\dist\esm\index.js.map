{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { coordEach } from \"@turf/meta\";\nimport { isObject, AllGeoJSON } from \"@turf/helpers\";\nimport { clone } from \"@turf/clone\";\n\n/**\n * Takes input features and flips all of their coordinates from `[x, y]` to `[y, x]`.\n *\n * @function\n * @param {GeoJSON} geojson input features\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.mutate=false] allows GeoJSON input to be mutated (significant performance increase if true)\n * @returns {GeoJSON} a feature or set of features of the same type as `input` with flipped coordinates\n * @example\n * var serbia = turf.point([20.566406, 43.421008]);\n *\n * var saudiArabia = turf.flip(serbia);\n *\n * //addToMap\n * var addToMap = [serbia, saudiArabia];\n */\nfunction flip<T extends AllGeoJSON>(\n  geojson: T,\n  options?: {\n    mutate?: boolean;\n  }\n): T {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const mutate = options.mutate ?? false;\n\n  if (!geojson) throw new Error(\"geojson is required\");\n  // ensure that we don't modify features in-place and changes to the\n  // output do not change the previous feature, including changes to nested\n  // properties.\n  if (mutate === false || mutate === undefined) geojson = clone(geojson);\n\n  coordEach(geojson, function (coord) {\n    var x = coord[0];\n    var y = coord[1];\n    coord[0] = y;\n    coord[1] = x;\n  });\n  return geojson;\n}\n\nexport { flip };\nexport default flip;\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAC1B,SAAS,gBAA4B;AACrC,SAAS,aAAa;AAkBtB,SAAS,KACP,SACA,SAGG;AAzBL;AA2BE,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,QAAM,UAAS,aAAQ,WAAR,YAAkB;AAEjC,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AAInD,MAAI,WAAW,SAAS,WAAW,OAAW,WAAU,MAAM,OAAO;AAErE,YAAU,SAAS,SAAU,OAAO;AAClC,QAAI,IAAI,MAAM,CAAC;AACf,QAAI,IAAI,MAAM,CAAC;AACf,UAAM,CAAC,IAAI;AACX,UAAM,CAAC,IAAI;AAAA,EACb,CAAC;AACD,SAAO;AACT;AAGA,IAAO,oBAAQ;", "names": []}