{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-disjoint/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACQA,uEAAsC;AACtC,qDAA8B;AAC9B,kCAA4B;AAC5B,sDAA8B;AAkB9B,SAAS,eAAA,CACP,QAAA,EACA,QAAA,EACA;AAAA,EACE,wBAAA,EAA0B;AAC5B,EAAA,EAEI,EAAE,uBAAA,EAAyB,KAAK,CAAA,EAC3B;AACT,EAAA,IAAI,KAAA,EAAO,IAAA;AACX,EAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,IAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,MAAA,GAAA,CAAI,KAAA,IAAS,KAAA,EAAO;AAClB,QAAA,OAAO,KAAA;AAAA,MACT;AACA,MAAA,KAAA,EAAO,QAAA;AAAA,QACL,QAAA,CAAS,QAAA;AAAA,QACT,QAAA,CAAS,QAAA;AAAA,QACT;AAAA,MACF,CAAA;AAAA,IACF,CAAC,CAAA;AAAA,EACH,CAAC,CAAA;AACD,EAAA,OAAO,IAAA;AACT;AAWA,SAAS,QAAA,CAAS,KAAA,EAAY,KAAA,EAAY,uBAAA,EAAkC;AAC1E,EAAA,OAAA,CAAQ,KAAA,CAAM,IAAA,EAAM;AAAA,IAClB,KAAK,OAAA;AACH,MAAA,OAAA,CAAQ,KAAA,CAAM,IAAA,EAAM;AAAA,QAClB,KAAK,OAAA;AACH,UAAA,OAAO,CAAC,aAAA,CAAc,KAAA,CAAM,WAAA,EAAa,KAAA,CAAM,WAAW,CAAA;AAAA,QAC5D,KAAK,YAAA;AACH,UAAA,OAAO,CAAC,aAAA,CAAc,KAAA,EAAO,KAAK,CAAA;AAAA,QACpC,KAAK,SAAA;AACH,UAAA,OAAO,CAAC,0DAAA,KAAsB,EAAO,KAAK,CAAA;AAAA,MAC9C;AAEA,MAAA,KAAA;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,CAAM,IAAA,EAAM;AAAA,QAClB,KAAK,OAAA;AACH,UAAA,OAAO,CAAC,aAAA,CAAc,KAAA,EAAO,KAAK,CAAA;AAAA,QACpC,KAAK,YAAA;AACH,UAAA,OAAO,CAAC,YAAA,CAAa,KAAA,EAAO,KAAA,EAAO,uBAAuB,CAAA;AAAA,QAC5D,KAAK,SAAA;AACH,UAAA,OAAO,CAAC,YAAA,CAAa,KAAA,EAAO,KAAA,EAAO,uBAAuB,CAAA;AAAA,MAC9D;AAEA,MAAA,KAAA;AAAA,IACF,KAAK,SAAA;AACH,MAAA,OAAA,CAAQ,KAAA,CAAM,IAAA,EAAM;AAAA,QAClB,KAAK,OAAA;AACH,UAAA,OAAO,CAAC,0DAAA,KAAsB,EAAO,KAAK,CAAA;AAAA,QAC5C,KAAK,YAAA;AACH,UAAA,OAAO,CAAC,YAAA,CAAa,KAAA,EAAO,KAAA,EAAO,uBAAuB,CAAA;AAAA,QAC5D,KAAK,SAAA;AACH,UAAA,OAAO,CAAC,YAAA,CAAa,KAAA,EAAO,KAAA,EAAO,uBAAuB,CAAA;AAAA,MAC9D;AAAA,EACJ;AACA,EAAA,OAAO,KAAA;AACT;AAGA,SAAS,aAAA,CAAc,UAAA,EAAwB,EAAA,EAAW;AACxD,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AAC1D,IAAA,GAAA,CACE,oBAAA;AAAA,MACE,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA;AAAA,MACxB,UAAA,CAAW,WAAA,CAAY,EAAA,EAAI,CAAC,CAAA;AAAA,MAC5B,EAAA,CAAG;AAAA,IACL,CAAA,EACA;AACA,MAAA,OAAO,IAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,KAAA;AACT;AAEA,SAAS,YAAA,CACP,WAAA,EACA,WAAA,EACA,uBAAA,EACA;AACA,EAAA,MAAM,iBAAA,EAAmB,0CAAA,WAAc,EAAa,WAAA,EAAa;AAAA,IAC/D;AAAA,EACF,CAAC,CAAA;AACD,EAAA,GAAA,CAAI,gBAAA,CAAiB,QAAA,CAAS,OAAA,EAAS,CAAA,EAAG;AACxC,IAAA,OAAO,IAAA;AAAA,EACT;AACA,EAAA,OAAO,KAAA;AACT;AAEA,SAAS,YAAA,CACP,OAAA,EACA,UAAA,EACA,uBAAA,EACA;AACA,EAAA,IAAA,CAAA,MAAW,MAAA,GAAS,UAAA,CAAW,WAAA,EAAa;AAC1C,IAAA,GAAA,CAAI,0DAAA,KAAsB,EAAO,OAAO,CAAA,EAAG;AACzC,MAAA,OAAO,IAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,MAAM,iBAAA,EAAmB,0CAAA,UAAc,EAAY,0CAAA,OAAqB,CAAA,EAAG;AAAA,IACzE;AAAA,EACF,CAAC,CAAA;AACD,EAAA,GAAA,CAAI,gBAAA,CAAiB,QAAA,CAAS,OAAA,EAAS,CAAA,EAAG;AACxC,IAAA,OAAO,IAAA;AAAA,EACT;AACA,EAAA,OAAO,KAAA;AACT;AAaA,SAAS,YAAA,CACP,QAAA,EACA,QAAA,EACA,uBAAA,EACA;AACA,EAAA,IAAA,CAAA,MAAW,OAAA,GAAU,QAAA,CAAS,WAAA,CAAY,CAAC,CAAA,EAAG;AAC5C,IAAA,GAAA,CAAI,0DAAA,MAAsB,EAAQ,QAAQ,CAAA,EAAG;AAC3C,MAAA,OAAO,IAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,IAAA,CAAA,MAAW,OAAA,GAAU,QAAA,CAAS,WAAA,CAAY,CAAC,CAAA,EAAG;AAC5C,IAAA,GAAA,CAAI,0DAAA,MAAsB,EAAQ,QAAQ,CAAA,EAAG;AAC3C,MAAA,OAAO,IAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,MAAM,iBAAA,EAAmB,0CAAA;AAAA,IACvB,0CAAA,QAAsB,CAAA;AAAA,IACtB,0CAAA,QAAsB,CAAA;AAAA,IACtB,EAAE,wBAAwB;AAAA,EAC5B,CAAA;AACA,EAAA,GAAA,CAAI,gBAAA,CAAiB,QAAA,CAAS,OAAA,EAAS,CAAA,EAAG;AACxC,IAAA,OAAO,IAAA;AAAA,EACT;AACA,EAAA,OAAO,KAAA;AACT;AAEA,SAAS,oBAAA,CACP,gBAAA,EACA,cAAA,EACA,EAAA,EACA;AACA,EAAA,MAAM,IAAA,EAAM,EAAA,CAAG,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AACtC,EAAA,MAAM,IAAA,EAAM,EAAA,CAAG,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AACtC,EAAA,MAAM,IAAA,EAAM,cAAA,CAAe,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AAClD,EAAA,MAAM,IAAA,EAAM,cAAA,CAAe,CAAC,EAAA,EAAI,gBAAA,CAAiB,CAAC,CAAA;AAClD,EAAA,MAAM,MAAA,EAAQ,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,GAAA;AAChC,EAAA,GAAA,CAAI,MAAA,IAAU,CAAA,EAAG;AACf,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA,EAAG;AAClC,IAAA,GAAA,CAAI,IAAA,EAAM,CAAA,EAAG;AACX,MAAA,OAAO,gBAAA,CAAiB,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,cAAA,CAAe,CAAC,CAAA;AAAA,IAClE,EAAA,KAAO;AACL,MAAA,OAAO,cAAA,CAAe,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,gBAAA,CAAiB,CAAC,CAAA;AAAA,IAClE;AAAA,EACF,EAAA,KAAA,GAAA,CAAW,IAAA,EAAM,CAAA,EAAG;AAClB,IAAA,OAAO,gBAAA,CAAiB,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,cAAA,CAAe,CAAC,CAAA;AAAA,EAClE,EAAA,KAAO;AACL,IAAA,OAAO,cAAA,CAAe,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,GAAK,gBAAA,CAAiB,CAAC,CAAA;AAAA,EAClE;AACF;AAUA,SAAS,aAAA,CAAc,KAAA,EAAiB,KAAA,EAAiB;AACvD,EAAA,OAAO,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,EAAA,GAAK,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,CAAA;AACtD;AAGA,IAAO,8BAAA,EAAQ,eAAA;ADvFf;AACE;AACA;AACF,2FAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-disjoint/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  Feature,\n  Geometry,\n  LineString,\n  Point,\n  Polygon,\n  Position,\n} from \"geojson\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { flattenEach } from \"@turf/meta\";\nimport { polygonToLine } from \"@turf/polygon-to-line\";\n\n/**\n * Boolean-disjoint returns (TRUE) if the intersection of the two geometries is an empty set.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.ignoreSelfIntersections=true] ignore self-intersections on input features\n * @returns {boolean} true if the intersection is an empty set, false otherwise\n * @example\n * var point = turf.point([2, 2]);\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n *\n * turf.booleanDisjoint(line, point);\n * //=true\n */\nfunction booleanDisjoint(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry,\n  {\n    ignoreSelfIntersections = true,\n  }: {\n    ignoreSelfIntersections?: boolean;\n  } = { ignoreSelfIntersections: true }\n): boolean {\n  let bool = true;\n  flattenEach(feature1, (flatten1) => {\n    flattenEach(feature2, (flatten2) => {\n      if (bool === false) {\n        return false;\n      }\n      bool = disjoint(\n        flatten1.geometry,\n        flatten2.geometry,\n        ignoreSelfIntersections\n      );\n    });\n  });\n  return bool;\n}\n\n/**\n * Disjoint operation for simple Geometries (Point/LineString/Polygon)\n *\n * @private\n * @param {Geometry<any>} geom1 GeoJSON Geometry\n * @param {Geometry<any>} geom2 GeoJSON Geometry\n * @param {boolean} ignoreSelfIntersections ignore self-intersections on input features\n * @returns {boolean} true if disjoint, false otherwise\n */\nfunction disjoint(geom1: any, geom2: any, ignoreSelfIntersections: boolean) {\n  switch (geom1.type) {\n    case \"Point\":\n      switch (geom2.type) {\n        case \"Point\":\n          return !compareCoords(geom1.coordinates, geom2.coordinates);\n        case \"LineString\":\n          return !isPointOnLine(geom2, geom1);\n        case \"Polygon\":\n          return !booleanPointInPolygon(geom1, geom2);\n      }\n      /* istanbul ignore next */\n      break;\n    case \"LineString\":\n      switch (geom2.type) {\n        case \"Point\":\n          return !isPointOnLine(geom1, geom2);\n        case \"LineString\":\n          return !isLineOnLine(geom1, geom2, ignoreSelfIntersections);\n        case \"Polygon\":\n          return !isLineInPoly(geom2, geom1, ignoreSelfIntersections);\n      }\n      /* istanbul ignore next */\n      break;\n    case \"Polygon\":\n      switch (geom2.type) {\n        case \"Point\":\n          return !booleanPointInPolygon(geom2, geom1);\n        case \"LineString\":\n          return !isLineInPoly(geom1, geom2, ignoreSelfIntersections);\n        case \"Polygon\":\n          return !isPolyInPoly(geom2, geom1, ignoreSelfIntersections);\n      }\n  }\n  return false;\n}\n\n// http://stackoverflow.com/a/11908158/1979085\nfunction isPointOnLine(lineString: LineString, pt: Point) {\n  for (let i = 0; i < lineString.coordinates.length - 1; i++) {\n    if (\n      isPointOnLineSegment(\n        lineString.coordinates[i],\n        lineString.coordinates[i + 1],\n        pt.coordinates\n      )\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isLineOnLine(\n  lineString1: LineString,\n  lineString2: LineString,\n  ignoreSelfIntersections: boolean\n) {\n  const doLinesIntersect = lineIntersect(lineString1, lineString2, {\n    ignoreSelfIntersections,\n  });\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\nfunction isLineInPoly(\n  polygon: Polygon,\n  lineString: LineString,\n  ignoreSelfIntersections: boolean\n) {\n  for (const coord of lineString.coordinates) {\n    if (booleanPointInPolygon(coord, polygon)) {\n      return true;\n    }\n  }\n  const doLinesIntersect = lineIntersect(lineString, polygonToLine(polygon), {\n    ignoreSelfIntersections,\n  });\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Is Polygon (geom1) in Polygon (geom2)\n * Only takes into account outer rings\n * See http://stackoverflow.com/a/4833823/1979085\n *\n * @private\n * @param {Geometry|Feature<Polygon>} feature1 Polygon1\n * @param {Geometry|Feature<Polygon>} feature2 Polygon2\n * @param {boolean} ignoreSelfIntersections ignore self-intersections on input features\n * @returns {boolean} true if geom1 is in geom2, false otherwise\n */\nfunction isPolyInPoly(\n  feature1: Polygon,\n  feature2: Polygon,\n  ignoreSelfIntersections: boolean\n) {\n  for (const coord1 of feature1.coordinates[0]) {\n    if (booleanPointInPolygon(coord1, feature2)) {\n      return true;\n    }\n  }\n  for (const coord2 of feature2.coordinates[0]) {\n    if (booleanPointInPolygon(coord2, feature1)) {\n      return true;\n    }\n  }\n  const doLinesIntersect = lineIntersect(\n    polygonToLine(feature1),\n    polygonToLine(feature2),\n    { ignoreSelfIntersections }\n  );\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\nfunction isPointOnLineSegment(\n  lineSegmentStart: Position,\n  lineSegmentEnd: Position,\n  pt: Position\n) {\n  const dxc = pt[0] - lineSegmentStart[0];\n  const dyc = pt[1] - lineSegmentStart[1];\n  const dxl = lineSegmentEnd[0] - lineSegmentStart[0];\n  const dyl = lineSegmentEnd[1] - lineSegmentStart[1];\n  const cross = dxc * dyl - dyc * dxl;\n  if (cross !== 0) {\n    return false;\n  }\n  if (Math.abs(dxl) >= Math.abs(dyl)) {\n    if (dxl > 0) {\n      return lineSegmentStart[0] <= pt[0] && pt[0] <= lineSegmentEnd[0];\n    } else {\n      return lineSegmentEnd[0] <= pt[0] && pt[0] <= lineSegmentStart[0];\n    }\n  } else if (dyl > 0) {\n    return lineSegmentStart[1] <= pt[1] && pt[1] <= lineSegmentEnd[1];\n  } else {\n    return lineSegmentEnd[1] <= pt[1] && pt[1] <= lineSegmentStart[1];\n  }\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true if coord pairs match, false otherwise\n */\nfunction compareCoords(pair1: Position, pair2: Position) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\nexport { booleanDisjoint };\nexport default booleanDisjoint;\n"]}