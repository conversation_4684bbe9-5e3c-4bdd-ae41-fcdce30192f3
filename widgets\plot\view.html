﻿<!DOCTYPE html>
<html class="no-js css-menubar" lang="zh-cn">

<head>
    <title>弹窗子页面</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 移动设备 viewport -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui">
    <meta name="author" content="火星科技 http://cesium.marsgis.cn ">
    <!-- 360浏览器默认使用Webkit内核 -->
    <meta name="renderer" content="webkit">
    <!-- Chrome浏览器添加桌面快捷方式（安卓） -->
    <link rel="icon" type="image/png" href="../../img/favicon/favicon.png">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Safari浏览器添加到主屏幕（IOS） -->
    <link rel="icon" sizes="192x192" href="img/favicon/apple-touch-icon.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="火星科技MarsGIS">
    <!-- Win8标题栏及ICON图标 -->
    <link rel="apple-touch-icon-precomposed" href="../../img/favicon/apple-touch-icon.png">
    <meta name="msapplication-TileImage" content="../../img/favicon/<EMAIL>">
    <meta name="msapplication-TileColor" content="#62a8ea">

    <!-- 第3方lib引入 -->
    <script type="text/javascript" src="../../lib/include-lib.js?time=20200102" libpath="../../lib/"
            include="jquery,jquery.minicolors,font-awesome,bootstrap,bootstrap-checkbox,bootstrap-table,haoutil,admui-frame"></script>

    <link href="../../css/widget-win.css" rel="stylesheet" />
    <link href="css/plot.css?time=20200102" rel="stylesheet" />
</head>
<body>
    <div class="mp_box">
        <div class="mp_head">
            <ul>
                <!--class="active"-->
                <li id="btn_plot_openfile"><i class="fa fa-folder-open-o" title="打开文件"></i></li>
                <li id="btn_plot_openfile2"><i class="fa fa-folder-open" title="叠加文件"></i></li>
                <li id="btn_plot_savefile"><i class="fa fa-save" title="保存文件"></i></li>
                <li class="ml10">|</li>
                <li id="btn_plot_delall"><i class="fa fa-trash" title="清空标绘"></i></li>
                <li id="btn_plot_isedit"><i class="fa fa-unlock" title="是否可编辑"></i></li>
                <li id="btn_plot_end"><i class="fa fa-share-alt " title="结束绘制，等同双击结束"></i></li>
            </ul>
            <input id="input_plot_file" type="file" accept=".json" style="display:none;" />
        </div>
        <div class="clear"></div>
        <div class="mp_tab_card">
            <ul class="mp_tab_con">
                <li class="cur">
                    <select id="sel_plot_list" class="mp_select m10" data-value="1">
                        <!--
                        <option value="1">常用标号</option>
                        <option value="2">点注记</option>
                        <option value="3">线面标号</option>
                        <option value="4">其他</option>-->
                    </select>
                    <div class="mp_mark">
                        <ul id="plotlist">
                            <!--<li class="markon"><i class="fa fa-map-marker"></i></li>
                            <li><i class="fa fa-map-marker"></i></li>
                            <li><i class="fa fa-map-marker"></i></li>
                            <li><i class="fa fa-map-marker"></i></li>
                            <li> <i> <img src="../../img/marker/men1.png" style="max-width: 50px;" /></i></li>-->
                        </ul>
                    </div>
                </li>
 
                <li>
                    <div class="mp_tree" style="overflow: hidden;"> 
                        <!--预设路线 列表面板-->
                        <table id="table"></table> 
                    </div>

                </li>

            </ul>
            <ul class="mp_tab_tit">
                <li id="tab_plot" class="cur">标号</li> 
                <li id="tab_attr" >列表</li>
            </ul>
        </div>
    </div>


    <script src="js/vew.common.js?time=20200102"></script>
    <script src="js/vew.work.js?time=20200102"></script>
</body>
</html>