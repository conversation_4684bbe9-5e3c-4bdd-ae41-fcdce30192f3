{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-convex/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wCAAoC;AACpC,kCAA0B;AAC1B,gGAAuB;AA8BvB,SAAS,MAAA,CACP,OAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACuB;AAE5B,EAAA,OAAA,CAAQ,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,QAAA;AAGzC,EAAA,MAAM,OAAA,EAAqB,CAAC,CAAA;AAG5B,EAAA,6BAAA,OAAU,EAAS,CAAC,KAAA,EAAA,GAAU;AAC5B,IAAA,MAAA,CAAO,IAAA,CAAK,CAAC,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAC,CAAC,CAAA;AAAA,EAClC,CAAC,CAAA;AACD,EAAA,GAAA,CAAI,CAAC,MAAA,CAAO,MAAA,EAAQ;AAClB,IAAA,OAAO,IAAA;AAAA,EACT;AAEA,EAAA,MAAM,WAAA,EAAa,kCAAA,MAAW,EAAQ,OAAA,CAAQ,SAAS,CAAA;AAGvD,EAAA,GAAA,CAAI,UAAA,CAAW,OAAA,EAAS,CAAA,EAAG;AACzB,IAAA,OAAO,8BAAA,CAAS,UAAU,CAAC,CAAA;AAAA,EAC7B;AACA,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,oBAAA,EAAQ,MAAA;AD5Cf;AACE;AACA;AACF,+DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-convex/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, GeoJsonProperties, Polygon } from \"geojson\";\nimport { AllGeoJSON, polygon } from \"@turf/helpers\";\nimport { coordEach } from \"@turf/meta\";\nimport concaveman from \"concaveman\";\n\n/**\n * Takes a {@link Feature} or a {@link FeatureCollection} and returns a convex hull {@link Polygon}.\n *\n * Internally this uses\n * the [convex-hull](https://github.com/mikolal<PERSON>enko/convex-hull) module that implements a\n * [monotone chain hull](http://en.wikibooks.org/wiki/Algorithm_Implementation/Geometry/Convex_hull/Monotone_chain).\n *\n * @function\n * @param {GeoJSON} geojson input Feature or FeatureCollection\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.concavity=Infinity] 1 - thin shape. Infinity - convex hull.\n * @param {Object} [options.properties={}] Translate Properties to Feature\n * @returns {Feature<Polygon>} a convex hull\n * @example\n * var points = turf.featureCollection([\n *   turf.point([10.195312, 43.755225]),\n *   turf.point([10.404052, 43.8424511]),\n *   turf.point([10.579833, 43.659924]),\n *   turf.point([10.360107, 43.516688]),\n *   turf.point([10.14038, 43.588348]),\n *   turf.point([10.195312, 43.755225])\n * ]);\n *\n * var hull = turf.convex(points);\n *\n * //addToMap\n * var addToMap = [points, hull]\n */\nfunction convex<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: AllGeoJSON,\n  options: {\n    concavity?: number;\n    properties?: P;\n  } = {}\n): Feature<Polygon, P> | null {\n  // Default parameters\n  options.concavity = options.concavity || Infinity;\n\n  // Container\n  const points: number[][] = [];\n\n  // Convert all points to flat 2D coordinate Array\n  coordEach(geojson, (coord) => {\n    points.push([coord[0], coord[1]]);\n  });\n  if (!points.length) {\n    return null;\n  }\n\n  const convexHull = concaveman(points, options.concavity);\n\n  // Convex hull should have at least 3 different vertices in order to create a valid polygon\n  if (convexHull.length > 3) {\n    return polygon([convexHull]);\n  }\n  return null;\n}\n\nexport { convex };\nexport default convex;\n"]}