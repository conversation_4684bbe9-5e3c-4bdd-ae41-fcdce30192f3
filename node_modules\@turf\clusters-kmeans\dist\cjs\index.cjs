"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// index.ts
var _clone = require('@turf/clone');
var _meta = require('@turf/meta');
var _skmeans = require('skmeans'); var _skmeans2 = _interopRequireDefault(_skmeans);
function clustersKmeans(points, options = {}) {
  var count = points.features.length;
  options.numberOfClusters = options.numberOfClusters || Math.round(Math.sqrt(count / 2));
  if (options.numberOfClusters > count) options.numberOfClusters = count;
  if (options.mutate !== true) points = _clone.clone.call(void 0, points);
  var data = _meta.coordAll.call(void 0, points);
  var initialCentroids = data.slice(0, options.numberOfClusters);
  var skmeansResult = _skmeans2.default.call(void 0, data, options.numberOfClusters, initialCentroids);
  var centroids = {};
  skmeansResult.centroids.forEach(function(coord, idx) {
    centroids[idx] = coord;
  });
  _meta.featureEach.call(void 0, points, function(point, index) {
    var clusterId = skmeansResult.idxs[index];
    point.properties.cluster = clusterId;
    point.properties.centroid = centroids[clusterId];
  });
  return points;
}
var turf_clusters_kmeans_default = clustersKmeans;



exports.clustersKmeans = clustersKmeans; exports.default = turf_clusters_kmeans_default;
//# sourceMappingURL=index.cjs.map