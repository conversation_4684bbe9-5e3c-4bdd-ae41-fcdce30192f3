/**
 * 测量工具 - 基于原生Cesium实现
 * 移植自widgets/measure功能
 */
class MeasureTool {
    constructor(viewer) {
        this.viewer = viewer;
        this.activeDrawing = null;
        this.measureEntities = [];
        this.handler = null;
        this.isActive = false;
        
        this.initHandler();
    }

    initHandler() {
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    }

    // 激活测量工具
    activate() {
        this.isActive = true;
        this.viewer.cesiumWidget.canvas.style.cursor = 'crosshair';
    }

    // 停用测量工具
    deactivate() {
        this.isActive = false;
        this.stopDrawing();
        this.viewer.cesiumWidget.canvas.style.cursor = '';
    }

    // 停止当前绘制
    stopDrawing() {
        if (this.handler) {
            this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
            this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        }
        this.activeDrawing = null;
    }

    // 距离测量
    measureDistance(options = {}) {
        this.stopDrawing();
        this.activate();
        
        const points = [];
        const dynamicPositions = new Cesium.CallbackProperty(() => points, false);
        
        // 创建动态线
        const polylineEntity = this.viewer.entities.add({
            polyline: {
                positions: dynamicPositions,
                clampToGround: options.clampToGround || false,
                width: 3,
                material: Cesium.Color.YELLOW,
                depthFailMaterial: Cesium.Color.YELLOW
            }
        });

        this.activeDrawing = polylineEntity;
        this.measureEntities.push(polylineEntity);

        // 鼠标事件处理
        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                points.push(position);
                
                // 添加点标记
                this.addPointMarker(position, points.length);
                
                // 计算并显示距离
                if (points.length > 1) {
                    const distance = this.calculateDistance(points);
                    this.addDistanceLabel(position, distance, options.unit);
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        // 鼠标移动
        this.handler.setInputAction((event) => {
            if (points.length > 0) {
                const position = this.viewer.camera.pickEllipsoid(event.endPosition, this.viewer.scene.globe.ellipsoid);
                if (position) {
                    if (points.length === 1) {
                        points.push(position);
                    } else {
                        points[points.length - 1] = position;
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        // 右键结束
        this.handler.setInputAction(() => {
            this.stopDrawing();
            this.deactivate();
            if (options.callback) {
                const totalDistance = this.calculateDistance(points);
                options.callback(totalDistance);
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    // 面积测量
    measureArea(options = {}) {
        this.stopDrawing();
        this.activate();
        
        const points = [];
        const dynamicPositions = new Cesium.CallbackProperty(() => points, false);
        
        // 创建动态多边形
        const polygonEntity = this.viewer.entities.add({
            polygon: {
                hierarchy: dynamicPositions,
                material: Cesium.Color.BLUE.withAlpha(0.3),
                outline: true,
                outlineColor: Cesium.Color.BLUE,
                height: 0,
                extrudedHeight: options.extrudedHeight || 0
            }
        });

        this.activeDrawing = polygonEntity;
        this.measureEntities.push(polygonEntity);

        // 鼠标事件处理
        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                points.push(position);
                this.addPointMarker(position, points.length);
                
                // 计算并显示面积
                if (points.length > 2) {
                    const area = this.calculateArea(points);
                    this.addAreaLabel(this.getPolygonCenter(points), area, options.unit);
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        // 鼠标移动
        this.handler.setInputAction((event) => {
            if (points.length > 0) {
                const position = this.viewer.camera.pickEllipsoid(event.endPosition, this.viewer.scene.globe.ellipsoid);
                if (position) {
                    if (points.length === 1) {
                        points.push(position);
                    } else {
                        points[points.length - 1] = position;
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        // 右键结束
        this.handler.setInputAction(() => {
            this.stopDrawing();
            this.deactivate();
            if (options.callback) {
                const totalArea = this.calculateArea(points);
                options.callback(totalArea);
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    // 高度测量
    measureHeight(options = {}) {
        this.stopDrawing();
        this.activate();
        
        let startPoint = null;
        
        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                if (!startPoint) {
                    startPoint = position;
                    this.addPointMarker(position, 1, '起点');
                } else {
                    const endPoint = position;
                    this.addPointMarker(position, 2, '终点');
                    
                    // 计算高度差
                    const height = this.calculateHeightDifference(startPoint, endPoint);
                    this.addHeightLabel(endPoint, height);
                    
                    // 添加连接线
                    this.viewer.entities.add({
                        polyline: {
                            positions: [startPoint, endPoint],
                            width: 3,
                            material: Cesium.Color.RED,
                            arcType: Cesium.ArcType.NONE
                        }
                    });
                    
                    this.stopDrawing();
                    this.deactivate();
                    
                    if (options.callback) {
                        options.callback(height);
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    // 计算距离
    calculateDistance(positions) {
        let totalDistance = 0;
        for (let i = 1; i < positions.length; i++) {
            const distance = Cesium.Cartesian3.distance(positions[i - 1], positions[i]);
            totalDistance += distance;
        }
        return totalDistance;
    }

    // 计算面积
    calculateArea(positions) {
        if (positions.length < 3) return 0;
        
        // 转换为地理坐标
        const coords = positions.map(pos => {
            const cartographic = Cesium.Cartographic.fromCartesian(pos);
            return [Cesium.Math.toDegrees(cartographic.longitude), Cesium.Math.toDegrees(cartographic.latitude)];
        });
        
        // 使用turf.js计算面积
        if (window.turf) {
            const polygon = turf.polygon([coords.concat([coords[0]])]);
            return turf.area(polygon);
        }
        
        // 简单的球面面积计算
        return this.calculateSphericalArea(positions);
    }

    // 球面面积计算
    calculateSphericalArea(positions) {
        const EARTH_RADIUS = 6371000; // 地球半径（米）
        let area = 0;
        
        if (positions.length < 3) return 0;
        
        for (let i = 0; i < positions.length; i++) {
            const j = (i + 1) % positions.length;
            const coord1 = Cesium.Cartographic.fromCartesian(positions[i]);
            const coord2 = Cesium.Cartographic.fromCartesian(positions[j]);
            
            area += (coord2.longitude - coord1.longitude) * 
                   (2 + Math.sin(coord1.latitude) + Math.sin(coord2.latitude));
        }
        
        area = Math.abs(area) * EARTH_RADIUS * EARTH_RADIUS / 2;
        return area;
    }

    // 计算高度差
    calculateHeightDifference(pos1, pos2) {
        const cart1 = Cesium.Cartographic.fromCartesian(pos1);
        const cart2 = Cesium.Cartographic.fromCartesian(pos2);
        return Math.abs(cart2.height - cart1.height);
    }

    // 获取多边形中心点
    getPolygonCenter(positions) {
        let x = 0, y = 0, z = 0;
        positions.forEach(pos => {
            x += pos.x;
            y += pos.y;
            z += pos.z;
        });
        return new Cesium.Cartesian3(x / positions.length, y / positions.length, z / positions.length);
    }

    // 添加点标记
    addPointMarker(position, index, label = null) {
        const entity = this.viewer.entities.add({
            position: position,
            point: {
                pixelSize: 8,
                color: Cesium.Color.YELLOW,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            },
            label: {
                text: label || index.toString(),
                font: '12pt sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new Cesium.Cartesian2(0, -40)
            }
        });
        this.measureEntities.push(entity);
        return entity;
    }

    // 添加距离标签
    addDistanceLabel(position, distance, unit = 'auto') {
        const text = this.formatDistance(distance, unit);
        const entity = this.viewer.entities.add({
            position: position,
            label: {
                text: text,
                font: '14pt sans-serif',
                fillColor: Cesium.Color.YELLOW,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new Cesium.Cartesian2(0, -20)
            }
        });
        this.measureEntities.push(entity);
        return entity;
    }

    // 添加面积标签
    addAreaLabel(position, area, unit = 'auto') {
        const text = this.formatArea(area, unit);
        const entity = this.viewer.entities.add({
            position: position,
            label: {
                text: text,
                font: '14pt sans-serif',
                fillColor: Cesium.Color.BLUE,
                outlineColor: Cesium.Color.WHITE,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE
            }
        });
        this.measureEntities.push(entity);
        return entity;
    }

    // 添加高度标签
    addHeightLabel(position, height) {
        const text = this.formatDistance(height, 'm');
        const entity = this.viewer.entities.add({
            position: position,
            label: {
                text: `高度差: ${text}`,
                font: '14pt sans-serif',
                fillColor: Cesium.Color.RED,
                outlineColor: Cesium.Color.WHITE,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE
            }
        });
        this.measureEntities.push(entity);
        return entity;
    }

    // 格式化距离
    formatDistance(distance, unit = 'auto') {
        if (unit === 'auto') {
            if (distance < 1000) {
                return `${distance.toFixed(2)} 米`;
            } else {
                return `${(distance / 1000).toFixed(2)} 公里`;
            }
        } else if (unit === 'm') {
            return `${distance.toFixed(2)} 米`;
        } else if (unit === 'km') {
            return `${(distance / 1000).toFixed(2)} 公里`;
        }
        return `${distance.toFixed(2)} 米`;
    }

    // 格式化面积
    formatArea(area, unit = 'auto') {
        if (unit === 'auto') {
            if (area < 10000) {
                return `${area.toFixed(2)} 平方米`;
            } else {
                return `${(area / 1000000).toFixed(2)} 平方公里`;
            }
        } else if (unit === 'm') {
            return `${area.toFixed(2)} 平方米`;
        } else if (unit === 'km') {
            return `${(area / 1000000).toFixed(2)} 平方公里`;
        }
        return `${area.toFixed(2)} 平方米`;
    }

    // 清除所有测量结果
    clearAll() {
        this.measureEntities.forEach(entity => {
            this.viewer.entities.remove(entity);
        });
        this.measureEntities = [];
        this.stopDrawing();
        this.deactivate();
    }

    // 销毁
    destroy() {
        this.clearAll();
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }
    }
}

// 导出
window.MeasureTool = MeasureTool;
