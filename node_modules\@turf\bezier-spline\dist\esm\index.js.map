{"version": 3, "sources": ["../../index.ts", "../../lib/spline.ts"], "sourcesContent": ["import { Feature, LineString, GeoJsonProperties } from \"geojson\";\nimport { lineString } from \"@turf/helpers\";\nimport { getGeom } from \"@turf/invariant\";\nimport { Spline } from \"./lib/spline.js\";\n\n/**\n * Takes a {@link LineString|line} and returns a curved version\n * by applying a [Bezier spline](http://en.wikipedia.org/wiki/B%C3%A9zier_spline)\n * algorithm.\n *\n * The bezier spline implementation is by [<PERSON><PERSON><PERSON>](http://leszek.rybicki.cc/).\n *\n * @function\n * @param {Feature<LineString>} line input LineString\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] Translate properties to output\n * @param {number} [options.resolution=10000] time in milliseconds between points\n * @param {number} [options.sharpness=0.85] a measure of how curvy the path should be between splines\n * @returns {Feature<LineString>} curved line\n * @example\n * var line = turf.lineString([\n *   [-76.091308, 18.427501],\n *   [-76.695556, 18.729501],\n *   [-76.552734, 19.40443],\n *   [-74.61914, 19.134789],\n *   [-73.652343, 20.07657],\n *   [-73.157958, 20.210656]\n * ]);\n *\n * var curved = turf.bezierSpline(line);\n *\n * //addToMap\n * var addToMap = [line, curved]\n * curved.properties = { stroke: '#0F0' };\n */\nfunction bezierSpline<P extends GeoJsonProperties = GeoJsonProperties>(\n  line: Feature<LineString> | LineString,\n  options: {\n    properties?: P;\n    resolution?: number;\n    sharpness?: number;\n  } = {}\n): Feature<LineString, P> {\n  // Optional params\n  const resolution = options.resolution || 10000;\n  const sharpness = options.sharpness || 0.85;\n\n  const coords: [number, number][] = [];\n  const points = getGeom(line).coordinates.map((pt) => {\n    return { x: pt[0], y: pt[1] };\n  });\n  const spline = new Spline({\n    duration: resolution,\n    points,\n    sharpness,\n  });\n\n  const pushCoord = (time: number) => {\n    var pos = spline.pos(time);\n    if (Math.floor(time / 100) % 2 === 0) {\n      coords.push([pos.x, pos.y]);\n    }\n  };\n\n  for (var i = 0; i < spline.duration; i += 10) {\n    pushCoord(i);\n  }\n  pushCoord(spline.duration);\n\n  return lineString(coords, options.properties);\n}\n\nexport { bezierSpline };\nexport default bezierSpline;\n", "interface Point {\n  x: number;\n  y: number;\n  z: number;\n}\n\n/**\n * BezierSpline\n * https://github.com/leszekr/bezier-spline-js\n *\n * @private\n * @copyright\n * Copyright (c) 2013 <PERSON><PERSON><PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nclass Spline {\n  public duration: number;\n  public points: Point[];\n  public sharpness: number;\n  public centers: Point[];\n  public controls: Array<[Point, Point]>;\n  public stepLength: number;\n  public length: number;\n  public delay: number;\n  public steps: number[];\n\n  constructor(options?: any) {\n    this.points = options.points || [];\n    this.duration = options.duration || 10000;\n    this.sharpness = options.sharpness || 0.85;\n    this.centers = [];\n    this.controls = [];\n    this.stepLength = options.stepLength || 60;\n    this.length = this.points.length;\n    this.delay = 0;\n\n    // this is to ensure compatibility with the 2d version\n    for (let i = 0; i < this.length; i++) {\n      this.points[i].z = this.points[i].z || 0;\n    }\n    for (let i = 0; i < this.length - 1; i++) {\n      const p1 = this.points[i];\n      const p2 = this.points[i + 1];\n      this.centers.push({\n        x: (p1.x + p2.x) / 2,\n        y: (p1.y + p2.y) / 2,\n        z: (p1.z + p2.z) / 2,\n      });\n    }\n    this.controls.push([this.points[0], this.points[0]]);\n    for (let i = 0; i < this.centers.length - 1; i++) {\n      const dx =\n        this.points[i + 1].x - (this.centers[i].x + this.centers[i + 1].x) / 2;\n      const dy =\n        this.points[i + 1].y - (this.centers[i].y + this.centers[i + 1].y) / 2;\n      const dz =\n        this.points[i + 1].z - (this.centers[i].y + this.centers[i + 1].z) / 2;\n      this.controls.push([\n        {\n          x:\n            (1.0 - this.sharpness) * this.points[i + 1].x +\n            this.sharpness * (this.centers[i].x + dx),\n          y:\n            (1.0 - this.sharpness) * this.points[i + 1].y +\n            this.sharpness * (this.centers[i].y + dy),\n          z:\n            (1.0 - this.sharpness) * this.points[i + 1].z +\n            this.sharpness * (this.centers[i].z + dz),\n        },\n        {\n          x:\n            (1.0 - this.sharpness) * this.points[i + 1].x +\n            this.sharpness * (this.centers[i + 1].x + dx),\n          y:\n            (1.0 - this.sharpness) * this.points[i + 1].y +\n            this.sharpness * (this.centers[i + 1].y + dy),\n          z:\n            (1.0 - this.sharpness) * this.points[i + 1].z +\n            this.sharpness * (this.centers[i + 1].z + dz),\n        },\n      ]);\n    }\n    this.controls.push([\n      this.points[this.length - 1],\n      this.points[this.length - 1],\n    ]);\n    this.steps = this.cacheSteps(this.stepLength);\n    return this;\n  }\n  /**\n   * Caches an array of equidistant (more or less) points on the curve.\n   */\n  public cacheSteps(mindist: number) {\n    const steps = [];\n    let laststep = this.pos(0);\n    steps.push(0);\n    for (let t = 0; t < this.duration; t += 10) {\n      const step = this.pos(t);\n      const dist = Math.sqrt(\n        (step.x - laststep.x) * (step.x - laststep.x) +\n          (step.y - laststep.y) * (step.y - laststep.y) +\n          (step.z - laststep.z) * (step.z - laststep.z)\n      );\n      if (dist > mindist) {\n        steps.push(t);\n        laststep = step;\n      }\n    }\n    return steps;\n  }\n\n  /**\n   * returns angle and speed in the given point in the curve\n   */\n  public vector(t: number) {\n    const p1 = this.pos(t + 10);\n    const p2 = this.pos(t - 10);\n    return {\n      angle: (180 * Math.atan2(p1.y - p2.y, p1.x - p2.x)) / 3.14,\n      speed: Math.sqrt(\n        (p2.x - p1.x) * (p2.x - p1.x) +\n          (p2.y - p1.y) * (p2.y - p1.y) +\n          (p2.z - p1.z) * (p2.z - p1.z)\n      ),\n    };\n  }\n\n  /**\n   * Gets the position of the point, given time.\n   *\n   * WARNING: The speed is not constant. The time it takes between control points is constant.\n   *\n   * For constant speed, use Spline.steps[i];\n   */\n  public pos(time: number) {\n    let t = time - this.delay;\n    if (t < 0) {\n      t = 0;\n    }\n    if (t > this.duration) {\n      t = this.duration - 1;\n    }\n    // t = t-this.delay;\n    const t2 = t / this.duration;\n    if (t2 >= 1) {\n      return this.points[this.length - 1];\n    }\n\n    const n = Math.floor((this.points.length - 1) * t2);\n    const t1 = (this.length - 1) * t2 - n;\n    return bezier(\n      t1,\n      this.points[n],\n      this.controls[n][1],\n      this.controls[n + 1][0],\n      this.points[n + 1]\n    );\n  }\n}\n\nfunction bezier(t: number, p1: Point, c1: Point, c2: Point, p2: Point) {\n  const b = B(t);\n  const pos = {\n    x: p2.x * b[0] + c2.x * b[1] + c1.x * b[2] + p1.x * b[3],\n    y: p2.y * b[0] + c2.y * b[1] + c1.y * b[2] + p1.y * b[3],\n    z: p2.z * b[0] + c2.z * b[1] + c1.z * b[2] + p1.z * b[3],\n  };\n  return pos;\n}\nfunction B(t: number) {\n  const t2 = t * t;\n  const t3 = t2 * t;\n  return [\n    t3,\n    3 * t2 * (1 - t),\n    3 * t * (1 - t) * (1 - t),\n    (1 - t) * (1 - t) * (1 - t),\n  ];\n}\n\nexport { Spline, Point };\nexport default Spline;\n"], "mappings": ";AACA,SAAS,kBAAkB;AAC3B,SAAS,eAAe;;;AC8BxB,IAAM,SAAN,MAAa;AAAA,EAWX,YAAY,SAAe;AACzB,SAAK,SAAS,QAAQ,UAAU,CAAC;AACjC,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,YAAY,QAAQ,aAAa;AACtC,SAAK,UAAU,CAAC;AAChB,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa,QAAQ,cAAc;AACxC,SAAK,SAAS,KAAK,OAAO;AAC1B,SAAK,QAAQ;AAGb,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAK,OAAO,CAAC,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,KAAK;AAAA,IACzC;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,YAAM,KAAK,KAAK,OAAO,CAAC;AACxB,YAAM,KAAK,KAAK,OAAO,IAAI,CAAC;AAC5B,WAAK,QAAQ,KAAK;AAAA,QAChB,IAAI,GAAG,IAAI,GAAG,KAAK;AAAA,QACnB,IAAI,GAAG,IAAI,GAAG,KAAK;AAAA,QACnB,IAAI,GAAG,IAAI,GAAG,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AACA,SAAK,SAAS,KAAK,CAAC,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC,CAAC,CAAC;AACnD,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,SAAS,GAAG,KAAK;AAChD,YAAM,KACJ,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,EAAE,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,KAAK;AACvE,YAAM,KACJ,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,EAAE,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,KAAK;AACvE,YAAM,KACJ,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,EAAE,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,KAAK;AACvE,WAAK,SAAS,KAAK;AAAA,QACjB;AAAA,UACE,IACG,IAAM,KAAK,aAAa,KAAK,OAAO,IAAI,CAAC,EAAE,IAC5C,KAAK,aAAa,KAAK,QAAQ,CAAC,EAAE,IAAI;AAAA,UACxC,IACG,IAAM,KAAK,aAAa,KAAK,OAAO,IAAI,CAAC,EAAE,IAC5C,KAAK,aAAa,KAAK,QAAQ,CAAC,EAAE,IAAI;AAAA,UACxC,IACG,IAAM,KAAK,aAAa,KAAK,OAAO,IAAI,CAAC,EAAE,IAC5C,KAAK,aAAa,KAAK,QAAQ,CAAC,EAAE,IAAI;AAAA,QAC1C;AAAA,QACA;AAAA,UACE,IACG,IAAM,KAAK,aAAa,KAAK,OAAO,IAAI,CAAC,EAAE,IAC5C,KAAK,aAAa,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI;AAAA,UAC5C,IACG,IAAM,KAAK,aAAa,KAAK,OAAO,IAAI,CAAC,EAAE,IAC5C,KAAK,aAAa,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI;AAAA,UAC5C,IACG,IAAM,KAAK,aAAa,KAAK,OAAO,IAAI,CAAC,EAAE,IAC5C,KAAK,aAAa,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI;AAAA,QAC9C;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,SAAS,KAAK;AAAA,MACjB,KAAK,OAAO,KAAK,SAAS,CAAC;AAAA,MAC3B,KAAK,OAAO,KAAK,SAAS,CAAC;AAAA,IAC7B,CAAC;AACD,SAAK,QAAQ,KAAK,WAAW,KAAK,UAAU;AAC5C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIO,WAAW,SAAiB;AACjC,UAAM,QAAQ,CAAC;AACf,QAAI,WAAW,KAAK,IAAI,CAAC;AACzB,UAAM,KAAK,CAAC;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,KAAK,IAAI;AAC1C,YAAM,OAAO,KAAK,IAAI,CAAC;AACvB,YAAM,OAAO,KAAK;AAAA,SACf,KAAK,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,MACxC,KAAK,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,MAC1C,KAAK,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS;AAAA,MAC/C;AACA,UAAI,OAAO,SAAS;AAClB,cAAM,KAAK,CAAC;AACZ,mBAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKO,OAAO,GAAW;AACvB,UAAM,KAAK,KAAK,IAAI,IAAI,EAAE;AAC1B,UAAM,KAAK,KAAK,IAAI,IAAI,EAAE;AAC1B,WAAO;AAAA,MACL,OAAQ,MAAM,KAAK,MAAM,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,IAAK;AAAA,MACtD,OAAO,KAAK;AAAA,SACT,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MACxB,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MAC1B,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,IAAI,MAAc;AACvB,QAAI,IAAI,OAAO,KAAK;AACpB,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AACA,QAAI,IAAI,KAAK,UAAU;AACrB,UAAI,KAAK,WAAW;AAAA,IACtB;AAEA,UAAM,KAAK,IAAI,KAAK;AACpB,QAAI,MAAM,GAAG;AACX,aAAO,KAAK,OAAO,KAAK,SAAS,CAAC;AAAA,IACpC;AAEA,UAAM,IAAI,KAAK,OAAO,KAAK,OAAO,SAAS,KAAK,EAAE;AAClD,UAAM,MAAM,KAAK,SAAS,KAAK,KAAK;AACpC,WAAO;AAAA,MACL;AAAA,MACA,KAAK,OAAO,CAAC;AAAA,MACb,KAAK,SAAS,CAAC,EAAE,CAAC;AAAA,MAClB,KAAK,SAAS,IAAI,CAAC,EAAE,CAAC;AAAA,MACtB,KAAK,OAAO,IAAI,CAAC;AAAA,IACnB;AAAA,EACF;AACF;AAEA,SAAS,OAAO,GAAW,IAAW,IAAW,IAAW,IAAW;AACrE,QAAM,IAAI,EAAE,CAAC;AACb,QAAM,MAAM;AAAA,IACV,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;AAAA,IACvD,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;AAAA,IACvD,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;AAAA,EACzD;AACA,SAAO;AACT;AACA,SAAS,EAAE,GAAW;AACpB,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,KAAK;AAChB,SAAO;AAAA,IACL;AAAA,IACA,IAAI,MAAM,IAAI;AAAA,IACd,IAAI,KAAK,IAAI,MAAM,IAAI;AAAA,KACtB,IAAI,MAAM,IAAI,MAAM,IAAI;AAAA,EAC3B;AACF;;;ADhKA,SAAS,aACP,MACA,UAII,CAAC,GACmB;AAExB,QAAM,aAAa,QAAQ,cAAc;AACzC,QAAM,YAAY,QAAQ,aAAa;AAEvC,QAAM,SAA6B,CAAC;AACpC,QAAM,SAAS,QAAQ,IAAI,EAAE,YAAY,IAAI,CAAC,OAAO;AACnD,WAAO,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,EAC9B,CAAC;AACD,QAAM,SAAS,IAAI,OAAO;AAAA,IACxB,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,YAAY,CAAC,SAAiB;AAClC,QAAI,MAAM,OAAO,IAAI,IAAI;AACzB,QAAI,KAAK,MAAM,OAAO,GAAG,IAAI,MAAM,GAAG;AACpC,aAAO,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAAA,IAC5B;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,OAAO,UAAU,KAAK,IAAI;AAC5C,cAAU,CAAC;AAAA,EACb;AACA,YAAU,OAAO,QAAQ;AAEzB,SAAO,WAAW,QAAQ,QAAQ,UAAU;AAC9C;AAGA,IAAO,6BAAQ;", "names": []}