{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-equal/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wDAAgC;AAChC,iDAA4B;AAC5B,4CAAwB;AAsBxB,SAAS,YAAA,CACP,QAAA,EACA,QAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACI;AACT,EAAA,IAAI,UAAA,EAAY,OAAA,CAAQ,SAAA;AAExB,EAAA,UAAA,EACE,UAAA,IAAc,KAAA,EAAA,GAAa,UAAA,IAAc,KAAA,GAAQ,KAAA,CAAM,SAAS,EAAA,EAC5D,EAAA,EACA,SAAA;AAEN,EAAA,GAAA,CAAI,OAAO,UAAA,IAAc,SAAA,GAAY,CAAA,CAAE,UAAA,GAAa,CAAA,CAAA,EAAI;AACtD,IAAA,MAAM,IAAI,KAAA,CAAM,qCAAqC,CAAA;AAAA,EACvD;AAEA,EAAA,MAAM,MAAA,EAAQ,gCAAA,QAAgB,CAAA,CAAE,IAAA;AAChC,EAAA,MAAM,MAAA,EAAQ,gCAAA,QAAgB,CAAA,CAAE,IAAA;AAChC,EAAA,GAAA,CAAI,MAAA,IAAU,KAAA,EAAO,OAAO,KAAA;AAE5B,EAAA,OAAO,gDAAA,sCAAgB,QAAoB,CAAA,EAAG,sCAAA,QAAoB,CAAA,EAAG;AAAA,IACnE;AAAA,EACF,CAAC,CAAA;AACH;AAGA,IAAO,2BAAA,EAAQ,YAAA;ADnCf;AACE;AACA;AACF,kFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-equal/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Geometry } from \"geojson\";\nimport { geojsonEquality } from \"geojson-equality-ts\";\nimport { cleanCoords } from \"@turf/clean-coords\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Determine whether two geometries of the same type have identical X,Y coordinate values.\n * See http://edndoc.esri.com/arcsde/9.0/general_topics/understand_spatial_relations.htm\n *\n * @function\n * @param {Geometry|Feature} feature1 GeoJSON input\n * @param {Geometry|Feature} feature2 GeoJSON input\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.precision=6] decimal precision to use when comparing coordinates\n * @returns {boolean} true if the objects are equal, false otherwise\n * @example\n * var pt1 = turf.point([0, 0]);\n * var pt2 = turf.point([0, 0]);\n * var pt3 = turf.point([1, 1]);\n *\n * turf.booleanEqual(pt1, pt2);\n * //= true\n * turf.booleanEqual(pt2, pt3);\n * //= false\n */\nfunction booleanEqual(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry,\n  options: {\n    precision?: number;\n  } = {}\n): boolean {\n  let precision = options.precision;\n\n  precision =\n    precision === undefined || precision === null || isNaN(precision)\n      ? 6\n      : precision;\n\n  if (typeof precision !== \"number\" || !(precision >= 0)) {\n    throw new Error(\"precision must be a positive number\");\n  }\n\n  const type1 = getGeom(feature1).type;\n  const type2 = getGeom(feature2).type;\n  if (type1 !== type2) return false;\n\n  return geojsonEquality(cleanCoords(feature1), cleanCoords(feature2), {\n    precision,\n  });\n}\n\nexport { booleanEqual };\nexport default booleanEqual;\n"]}