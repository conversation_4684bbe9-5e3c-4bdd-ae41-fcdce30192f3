{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-dissolve/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wCAA0D;AAC1D,4CAA6B;AAC7B,kCAA4B;AAC5B,wCAAwB;AACxB,8FAA0B;AAuB1B,SAAS,QAAA,CACP,EAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACuB;AAE5B,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,MAAM,EAAE,aAAa,EAAA,EAAI,OAAA;AAGzB,EAAA,qCAAA,EAAa,EAAI,SAAA,EAAW,UAAU,CAAA;AAGtC,EAAA,MAAM,YAAA,EAAc,CAAC,CAAA;AACrB,EAAA,GAAA,CAAI,CAAC,YAAA,EAAc;AACjB,IAAA,OAAO,8BAAA;AAAA,MACL,mCAAA;AAAA,QACW,QAAA,CAAA,KAAA,CAAM,KAAA;AAAA,UACb,IAAA;AAAA;AAAA,UAEA,EAAA,CAAG,QAAA,CAAS,GAAA,CAAI,QAAA,CAAU,CAAA,EAAG;AAC3B,YAAA,OAAO,CAAA,CAAE,QAAA,CAAS,WAAA;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAA;AAAA,EACF,EAAA,KAAO;AAEL,IAAA,MAAM,mBAAA,EAAmD,CAAC,CAAA;AAC1D,IAAA,+BAAA,EAAY,EAAI,QAAA,CAAU,OAAA,EAAS;AACjC,MAAA,GAAA,CAAI,OAAA,CAAQ,UAAA,EAAY;AACtB,QAAA,GAAA,CACE,CAAC,MAAA,CAAO,SAAA,CAAU,cAAA,CAAe,IAAA;AAAA,UAC/B,kBAAA;AAAA,UACA,OAAA,CAAQ,UAAA,CAAW,YAAY;AAAA,QACjC,CAAA,EACA;AACA,UAAA,kBAAA,CAAmB,OAAA,CAAQ,UAAA,CAAW,YAAY,CAAC,EAAA,EACjD,CAAC,CAAA;AAAA,QACL;AACA,QAAA,kBAAA,CAAmB,OAAA,CAAQ,UAAA,CAAW,YAAY,CAAC,CAAA,CAAE,IAAA,CAAK,OAAO,CAAA;AAAA,MACnE;AAAA,IACF,CAAC,CAAA;AACD,IAAA,MAAM,KAAA,EAAO,MAAA,CAAO,IAAA,CAAK,kBAAkB,CAAA;AAG3C,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpC,MAAA,MAAM,GAAA,EAAK,mCAAA;AAAA,QACA,QAAA,CAAA,KAAA,CAAM,KAAA;AAAA,UACb,IAAA;AAAA;AAAA,UAEC,kBAAA,CAAmB,IAAA,CAAK,CAAC,CAAC,CAAA,CAAyB,GAAA,CAAI,QAAA,CAAU,CAAA,EAAG;AACnE,YAAA,OAAO,CAAA,CAAE,QAAA,CAAS,WAAA;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF,CAAA;AACA,MAAA,GAAA,CAAI,GAAA,GAAM,EAAA,CAAG,UAAA,EAAY;AACvB,QAAA,EAAA,CAAG,UAAA,CAAW,YAAY,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA;AACpC,QAAA,WAAA,CAAY,IAAA,CAAK,EAAE,CAAA;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAEA,EAAA,OAAO,8BAAA,wCAAQ,WAA6B,CAAC,CAAA;AAC/C;AAGA,IAAO,sBAAA,EAAQ,QAAA;ADxCf;AACE;AACA;AACF,qEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-dissolve/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, FeatureCollection, Polygon } from \"geojson\";\nimport { featureCollection, isObject, multiPolygon } from \"@turf/helpers\";\nimport { collectionOf } from \"@turf/invariant\";\nimport { featureEach } from \"@turf/meta\";\nimport { flatten } from \"@turf/flatten\";\nimport * as polyclip from \"polyclip-ts\";\n\n/**\n * Dissolves a FeatureCollection of {@link Polygon} features, filtered by an optional property name:value.\n * Note that {@link MultiPolygon} features within the collection are not supported\n *\n * @function\n * @param {FeatureCollection<Polygon>} featureCollection input feature collection to be dissolved\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.propertyName] features with the same `propertyName` value will be dissolved.\n * @returns {FeatureCollection<Polygon>} a FeatureCollection containing the dissolved polygons\n * @example\n * var features = turf.featureCollection([\n *   turf.polygon([[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]], {combine: 'yes'}),\n *   turf.polygon([[[0, -1], [0, 0], [1, 0], [1, -1], [0,-1]]], {combine: 'yes'}),\n *   turf.polygon([[[1,-1],[1, 0], [2, 0], [2, -1], [1, -1]]], {combine: 'no'}),\n * ]);\n *\n * var dissolved = turf.dissolve(features, {propertyName: 'combine'});\n *\n * //addToMap\n * var addToMap = [features, dissolved]\n */\nfunction dissolve(\n  fc: FeatureCollection<Polygon>,\n  options: {\n    propertyName?: string;\n  } = {}\n): FeatureCollection<Polygon> {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const { propertyName } = options;\n\n  // Input validation\n  collectionOf(fc, \"Polygon\", \"dissolve\");\n\n  // Main\n  const outFeatures = [];\n  if (!propertyName) {\n    return flatten(\n      multiPolygon(\n        polyclip.union.apply(\n          null,\n          // List of polygons expressed as Position[][][] a.k.a. Geom[]\n          fc.features.map(function (f) {\n            return f.geometry.coordinates;\n          }) as [polyclip.Geom, ...polyclip.Geom[]]\n        )\n      )\n    );\n  } else {\n    // Group polygons by the value of their property named by propertyName\n    const uniquePropertyVals: { [key: string]: Feature[] } = {};\n    featureEach(fc, function (feature) {\n      if (feature.properties) {\n        if (\n          !Object.prototype.hasOwnProperty.call(\n            uniquePropertyVals,\n            feature.properties[propertyName]\n          )\n        ) {\n          uniquePropertyVals[feature.properties[propertyName]] =\n            [] as Feature[];\n        }\n        uniquePropertyVals[feature.properties[propertyName]].push(feature);\n      }\n    });\n    const vals = Object.keys(uniquePropertyVals);\n\n    // Export each group of polygons as a separate feature.\n    for (let i = 0; i < vals.length; i++) {\n      const mp = multiPolygon(\n        polyclip.union.apply(\n          null,\n          // List of polygons expressed as Position[][][] a.k.a. Geom[]\n          (uniquePropertyVals[vals[i]] as Feature<Polygon>[]).map(function (f) {\n            return f.geometry.coordinates;\n          }) as [polyclip.Geom, ...polyclip.Geom[]]\n        )\n      );\n      if (mp && mp.properties) {\n        mp.properties[propertyName] = vals[i];\n        outFeatures.push(mp);\n      }\n    }\n  }\n\n  return flatten(featureCollection(outFeatures));\n}\n\nexport { dissolve };\nexport default dissolve;\n"]}