# @turf/centroid

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## centroid

Computes the centroid as the mean of all vertices within the object.

### Parameters

*   `geojson` **[GeoJSON][1]** GeoJSON to be centered
*   `options` **[Object][2]** Optional Parameters (optional, default `{}`)

    *   `options.properties` **[Object][2]** an Object that is used as the [Feature][3]'s properties (optional, default `{}`)

### Examples

```javascript
var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);

var centroid = turf.centroid(polygon);

//addToMap
var addToMap = [polygon, centroid]
```

Returns **[Feature][3]<[Point][4]>** the centroid of the input object

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/centroid
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
