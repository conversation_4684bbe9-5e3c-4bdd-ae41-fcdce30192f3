{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Coord, degreesToRadians, radiansToDegrees } from \"@turf/helpers\";\nimport { getCoord } from \"@turf/invariant\";\n\n// http://en.wikipedia.org/wiki/Haversine_formula\n// http://www.movable-type.co.uk/scripts/latlong.html#bearing\n\n/**\n * Takes two {@link Point|points} and finds the geographic bearing between them,\n * i.e. the angle measured in degrees from the north line (0 degrees)\n *\n * @function\n * @param {Coord} start starting Point\n * @param {Coord} end ending Point\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.final=false] calculates the final bearing if true\n * @returns {number} bearing in decimal degrees, between -180 and 180 degrees (positive clockwise)\n * @example\n * var point1 = turf.point([-75.343, 39.984]);\n * var point2 = turf.point([-75.534, 39.123]);\n *\n * var bearing = turf.bearing(point1, point2);\n *\n * //addToMap\n * var addToMap = [point1, point2]\n * point1.properties['marker-color'] = '#f00'\n * point2.properties['marker-color'] = '#0f0'\n * point1.properties.bearing = bearing\n */\nfunction bearing(\n  start: Coord,\n  end: Coord,\n  options: {\n    final?: boolean;\n  } = {}\n): number {\n  // Reverse calculation\n  if (options.final === true) {\n    return calculateFinalBearing(start, end);\n  }\n\n  const coordinates1 = getCoord(start);\n  const coordinates2 = getCoord(end);\n\n  const lon1 = degreesToRadians(coordinates1[0]);\n  const lon2 = degreesToRadians(coordinates2[0]);\n  const lat1 = degreesToRadians(coordinates1[1]);\n  const lat2 = degreesToRadians(coordinates2[1]);\n  const a = Math.sin(lon2 - lon1) * Math.cos(lat2);\n  const b =\n    Math.cos(lat1) * Math.sin(lat2) -\n    Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);\n\n  return radiansToDegrees(Math.atan2(a, b));\n}\n\n/**\n * Calculates Final Bearing\n *\n * @private\n * @param {Coord} start starting Point\n * @param {Coord} end ending Point\n * @returns {number} bearing\n */\nfunction calculateFinalBearing(start: Coord, end: Coord) {\n  // Swap start & end\n  let bear = bearing(end, start);\n  bear = (bear + 180) % 360;\n  return bear;\n}\n\nexport { bearing };\nexport default bearing;\n"], "mappings": ";AAAA,SAAgB,kBAAkB,wBAAwB;AAC1D,SAAS,gBAAgB;AA2BzB,SAAS,QACP,OACA,KACA,UAEI,CAAC,GACG;AAER,MAAI,QAAQ,UAAU,MAAM;AAC1B,WAAO,sBAAsB,OAAO,GAAG;AAAA,EACzC;AAEA,QAAM,eAAe,SAAS,KAAK;AACnC,QAAM,eAAe,SAAS,GAAG;AAEjC,QAAM,OAAO,iBAAiB,aAAa,CAAC,CAAC;AAC7C,QAAM,OAAO,iBAAiB,aAAa,CAAC,CAAC;AAC7C,QAAM,OAAO,iBAAiB,aAAa,CAAC,CAAC;AAC7C,QAAM,OAAO,iBAAiB,aAAa,CAAC,CAAC;AAC7C,QAAM,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,KAAK,IAAI,IAAI;AAC/C,QAAM,IACJ,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAC9B,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,OAAO,IAAI;AAExD,SAAO,iBAAiB,KAAK,MAAM,GAAG,CAAC,CAAC;AAC1C;AAUA,SAAS,sBAAsB,OAAc,KAAY;AAEvD,MAAI,OAAO,QAAQ,KAAK,KAAK;AAC7B,UAAQ,OAAO,OAAO;AACtB,SAAO;AACT;AAGA,IAAO,uBAAQ;", "names": []}