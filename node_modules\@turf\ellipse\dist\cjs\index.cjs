"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts





var _helpers = require('@turf/helpers');
var _rhumbdestination = require('@turf/rhumb-destination');
var _transformrotate = require('@turf/transform-rotate');
var _invariant = require('@turf/invariant');
function ellipse(center, xSemiAxis, ySemiAxis, options) {
  options = options || {};
  const steps = options.steps || 64;
  const units = options.units || "kilometers";
  const angle = options.angle || 0;
  const pivot = options.pivot || center;
  const properties = options.properties || {};
  if (!center) throw new Error("center is required");
  if (!xSemiAxis) throw new Error("xSemiAxis is required");
  if (!ySemiAxis) throw new Error("ySemiAxis is required");
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options must be an object");
  if (!_helpers.isNumber.call(void 0, steps)) throw new Error("steps must be a number");
  if (!_helpers.isNumber.call(void 0, angle)) throw new Error("angle must be a number");
  const centerCoords = _invariant.getCoord.call(void 0, center);
  if (units !== "degrees") {
    const xDest = _rhumbdestination.rhumbDestination.call(void 0, center, xSemiAxis, 90, { units });
    const yDest = _rhumbdestination.rhumbDestination.call(void 0, center, ySemiAxis, 0, { units });
    xSemiAxis = _invariant.getCoord.call(void 0, xDest)[0] - centerCoords[0];
    ySemiAxis = _invariant.getCoord.call(void 0, yDest)[1] - centerCoords[1];
  }
  const coordinates = [];
  for (let i = 0; i < steps; i += 1) {
    const stepAngle = i * -360 / steps;
    let x = xSemiAxis * ySemiAxis / Math.sqrt(
      Math.pow(ySemiAxis, 2) + Math.pow(xSemiAxis, 2) * Math.pow(getTanDeg(stepAngle), 2)
    );
    let y = xSemiAxis * ySemiAxis / Math.sqrt(
      Math.pow(xSemiAxis, 2) + Math.pow(ySemiAxis, 2) / Math.pow(getTanDeg(stepAngle), 2)
    );
    if (stepAngle < -90 && stepAngle >= -270) x = -x;
    if (stepAngle < -180 && stepAngle >= -360) y = -y;
    if (units === "degrees") {
      const angleRad = _helpers.degreesToRadians.call(void 0, angle);
      const newx = x * Math.cos(angleRad) + y * Math.sin(angleRad);
      const newy = y * Math.cos(angleRad) - x * Math.sin(angleRad);
      x = newx;
      y = newy;
    }
    coordinates.push([x + centerCoords[0], y + centerCoords[1]]);
  }
  coordinates.push(coordinates[0]);
  if (units === "degrees") {
    return _helpers.polygon.call(void 0, [coordinates], properties);
  } else {
    return _transformrotate.transformRotate.call(void 0, _helpers.polygon.call(void 0, [coordinates], properties), angle, {
      pivot
    });
  }
}
function getTanDeg(deg) {
  const rad = deg * Math.PI / 180;
  return Math.tan(rad);
}
var turf_ellipse_default = ellipse;



exports.default = turf_ellipse_default; exports.ellipse = ellipse;
//# sourceMappingURL=index.cjs.map