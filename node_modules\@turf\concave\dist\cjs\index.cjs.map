{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-concave/dist/cjs/index.cjs", "../../index.ts", "../../lib/turf-dissolve.ts", "../../lib/turf-line-dissolve.ts", "../../lib/turf-polygon-dissolve.ts"], "names": ["getType", "clone", "feature", "isObject", "flattenEach"], "mappings": "AAAA;ACAA,0CAAyB;AACzB,wCAA2C;AAS3C,kCAA4B;AAC5B,gCAAoB;ADNpB;AACA;AEEA,oCAAsB;AACtB;AACA,4CAAwB;AACxB;AFAA;AACA;AGNA;AACA;AACA;AACA;AAWA,SAAS,YAAA,CACP,OAAA,EACA,QAAA,EAAgC,CAAC,CAAA,EACa;AAE9C,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG;AACtB,IAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAAA,EACtC;AACA,EAAA,MAAM,OAAA,EAAS,OAAA,CAAQ,MAAA;AAGvB,EAAA,GAAA,CAAI,gCAAA,OAAe,EAAA,IAAM,mBAAA,EAAqB;AAC5C,IAAA,MAAM,IAAI,KAAA,CAAM,qCAAqC,CAAA;AAAA,EACvD;AACA,EAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,QAAA,CAAS,MAAA,EAAQ;AAC5B,IAAA,MAAM,IAAI,KAAA,CAAM,kBAAkB,CAAA;AAAA,EACpC;AAGA,EAAA,GAAA,CAAI,OAAA,IAAW,MAAA,GAAS,OAAA,IAAW,KAAA,CAAA,EAAW;AAC5C,IAAA,QAAA,EAAU,0BAAA,OAAa,CAAA;AAAA,EACzB;AAEA,EAAA,MAAM,OAAA,EAAgB,CAAC,CAAA;AACvB,EAAA,MAAM,SAAA,EAAW,8BAAA;AAAA,IACf,OAAA;AAAA,IACA,CAAC,YAAA,EAAmB,WAAA,EAAA,GAAqB;AAGvC,MAAA,MAAM,OAAA,EAAS,gBAAA,CAAiB,YAAA,EAAc,WAAW,CAAA;AAGzD,MAAA,GAAA,CAAI,MAAA,EAAQ;AACV,QAAA,OAAO,MAAA;AAAA,MAET,EAAA,KAAO;AACL,QAAA,MAAA,CAAO,IAAA,CAAK,YAAY,CAAA;AACxB,QAAA,OAAO,WAAA;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAA;AAEA,EAAA,GAAA,CAAI,QAAA,EAAU;AACZ,IAAA,MAAA,CAAO,IAAA,CAAK,QAAQ,CAAA;AAAA,EACtB;AAGA,EAAA,GAAA,CAAI,CAAC,MAAA,CAAO,MAAA,EAAQ;AAClB,IAAA,OAAO,IAAA;AAAA,EAET,EAAA,KAAA,GAAA,CAAW,MAAA,CAAO,OAAA,IAAW,CAAA,EAAG;AAC9B,IAAA,OAAO,MAAA,CAAO,CAAC,CAAA;AAAA,EAEjB,EAAA,KAAO;AACL,IAAA,OAAO,sCAAA;AAAA,MACL,MAAA,CAAO,GAAA,CAAI,CAAC,IAAA,EAAA,GAAS;AACnB,QAAA,OAAO,IAAA,CAAK,WAAA;AAAA,MACd,CAAC;AAAA,IACH,CAAA;AAAA,EACF;AACF;AAGA,SAAS,OAAA,CAAQ,KAAA,EAAiB;AAChC,EAAA,OAAO,KAAA,CAAM,CAAC,CAAA,CAAE,QAAA,CAAS,EAAA,EAAI,IAAA,EAAM,KAAA,CAAM,CAAC,CAAA,CAAE,QAAA,CAAS,CAAA;AACvD;AAUA,SAAS,gBAAA,CAAiB,CAAA,EAAwB,CAAA,EAAwB;AACxE,EAAA,MAAM,QAAA,EAAU,CAAA,CAAE,QAAA,CAAS,WAAA;AAC3B,EAAA,MAAM,QAAA,EAAU,CAAA,CAAE,QAAA,CAAS,WAAA;AAE3B,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,OAAA,CAAQ,CAAC,CAAC,CAAA;AAC7B,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,OAAA,CAAQ,OAAA,CAAQ,OAAA,EAAS,CAAC,CAAC,CAAA;AAC9C,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,OAAA,CAAQ,CAAC,CAAC,CAAA;AAC7B,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,OAAA,CAAQ,OAAA,CAAQ,OAAA,EAAS,CAAC,CAAC,CAAA;AAG9C,EAAA,IAAI,MAAA;AACJ,EAAA,GAAA,CAAI,GAAA,IAAO,EAAA,EAAI;AACb,IAAA,OAAA,EAAS,OAAA,CAAQ,MAAA,CAAO,OAAA,CAAQ,KAAA,CAAM,CAAC,CAAC,CAAA;AAAA,EAC1C,EAAA,KAAA,GAAA,CAAW,GAAA,IAAO,EAAA,EAAI;AACpB,IAAA,OAAA,EAAS,OAAA,CAAQ,MAAA,CAAO,OAAA,CAAQ,KAAA,CAAM,CAAC,CAAC,CAAA;AAAA,EAC1C,EAAA,KAAA,GAAA,CAAW,GAAA,IAAO,EAAA,EAAI;AACpB,IAAA,OAAA,EAAS,OAAA,CAAQ,KAAA,CAAM,CAAC,CAAA,CAAE,OAAA,CAAQ,CAAA,CAAE,MAAA,CAAO,OAAO,CAAA;AAAA,EACpD,EAAA,KAAA,GAAA,CAAW,GAAA,IAAO,EAAA,EAAI;AACpB,IAAA,OAAA,EAAS,OAAA,CAAQ,MAAA,CAAO,OAAA,CAAQ,OAAA,CAAQ,CAAA,CAAE,KAAA,CAAM,CAAC,CAAC,CAAA;AAAA,EACpD,EAAA,KAAO;AACL,IAAA,OAAO,IAAA;AAAA,EACT;AAEA,EAAA,OAAO,iCAAA,MAAiB,CAAA;AAC1B;AHpCA;AACA;AIpFA;AACA;AACA;AACA;AACA,iDAAsB;AACtB,iDAAyB;AAUzB,SAAS,eAAA,CACP,OAAA,EACA,QAAA,EAAgC,CAAC,CAAA,EACO;AAExC,EAAA,GAAA,CAAIA,gCAAAA,OAAe,EAAA,IAAM,mBAAA,EAAqB;AAC5C,IAAA,MAAM,IAAI,KAAA,CAAM,qCAAqC,CAAA;AAAA,EACvD;AACA,EAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,QAAA,CAAS,MAAA,EAAQ;AAC5B,IAAA,MAAM,IAAI,KAAA,CAAM,kBAAkB,CAAA;AAAA,EACpC;AAIA,EAAA,GAAA,CAAI,OAAA,CAAQ,OAAA,IAAW,MAAA,GAAS,OAAA,CAAQ,OAAA,IAAW,KAAA,CAAA,EAAW;AAC5D,IAAA,QAAA,EAAUC,0BAAAA,OAAa,CAAA;AAAA,EACzB;AAEA,EAAA,MAAM,MAAA,EAAe,CAAC,CAAA;AACtB,EAAA,+BAAA,OAAY,EAAS,CAACC,QAAAA,EAAAA,GAAY;AAChC,IAAA,KAAA,CAAM,IAAA,CAAKA,QAAAA,CAAQ,QAAQ,CAAA;AAAA,EAC7B,CAAC,CAAA;AACD,EAAA,MAAM,KAAA,EAAY,sCAAA,EAAW,KAAA,EAAO,yCAAA,KAAwB,CAAA,CAAE,SAAS,CAAC,CAAA;AACxE,EAAA,MAAM,OAAA,EAAc,mCAAA,IAAM,EAAM,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,UAAU,CAAA;AAC7D,EAAA,OAAO,MAAA;AACT;AJqEA;AACA;AEtFA,SAAS,QAAA,CACP,OAAA,EAGA,QAAA,EAEI,CAAC,CAAA,EACkE;AAEvE,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAACC,+BAAAA,OAAgB,CAAA,EAAG;AACtB,IAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAAA,EACtC;AACA,EAAA,MAAM,OAAA,EAAS,OAAA,CAAQ,MAAA;AAGvB,EAAA,GAAA,CAAIH,gCAAAA,OAAe,EAAA,IAAM,mBAAA,EAAqB;AAC5C,IAAA,MAAM,IAAI,KAAA,CAAM,qCAAqC,CAAA;AAAA,EACvD;AACA,EAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,QAAA,CAAS,MAAA,EAAQ;AAC5B,IAAA,MAAM,IAAI,KAAA,CAAM,kBAAkB,CAAA;AAAA,EACpC;AAIA,EAAA,GAAA,CAAI,OAAA,IAAW,MAAA,GAAS,OAAA,IAAW,KAAA,CAAA,EAAW;AAC5C,IAAA,QAAA,EAAUC,0BAAAA,OAAa,CAAA;AAAA,EACzB;AAGA,EAAA,MAAM,KAAA,EAAO,iBAAA,CAAkB,OAAO,CAAA;AACtC,EAAA,GAAA,CAAI,CAAC,IAAA,EAAM;AACT,IAAA,MAAM,IAAI,KAAA,CAAM,4BAA4B,CAAA;AAAA,EAC9C;AAGA,EAAA,MAAM,KAAA,EAAY,OAAA;AAElB,EAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,IACZ,KAAK,YAAA;AACH,MAAA,OAAO,YAAA,CAAa,IAAA,EAAM,OAAO,CAAA;AAAA,IACnC,KAAK,SAAA;AACH,MAAA,OAAO,eAAA,CAAgB,IAAA,EAAM,OAAO,CAAA;AAAA,IACtC,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,KAAA,EAAO,mBAAmB,CAAA;AAAA,EAC9C;AACF;AASA,SAAS,iBAAA,CAAkB,OAAA,EAAc;AACvC,EAAA,MAAM,MAAA,EAAoC,CAAC,CAAA;AAC3C,EAAAG,+BAAAA,OAAY,EAAS,CAACF,QAAAA,EAAAA,GAAY;AAChC,IAAA,KAAA,CAAMA,QAAAA,CAAQ,QAAA,CAAS,IAAI,EAAA,EAAI,IAAA;AAAA,EACjC,CAAC,CAAA;AACD,EAAA,MAAM,KAAA,EAAO,MAAA,CAAO,IAAA,CAAK,KAAK,CAAA;AAC9B,EAAA,GAAA,CAAI,IAAA,CAAK,OAAA,IAAW,CAAA,EAAG;AACrB,IAAA,OAAO,IAAA,CAAK,CAAC,CAAA;AAAA,EACf;AACA,EAAA,OAAO,IAAA;AACT;AF8DA;AACA;AChHA,SAAS,OAAA,CACP,MAAA,EACA,QAAA,EAA+C,CAAC,CAAA,EACR;AACxC,EAAA,MAAM,QAAA,EAAU,OAAA,CAAQ,QAAA,GAAW,QAAA;AAEnC,EAAA,MAAM,QAAA,EAAU,gBAAA,CAAiB,MAAM,CAAA;AAEvC,EAAA,MAAM,SAAA,EAAW,sBAAA,OAAW,CAAA;AAG5B,EAAA,QAAA,CAAS,SAAA,EAAW,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,CAAC,QAAA,EAAA,GAAa;AACzD,IAAA,MAAM,IAAA,EAAM,QAAA,CAAS,QAAA,CAAS,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA;AAC9C,IAAA,MAAM,IAAA,EAAM,QAAA,CAAS,QAAA,CAAS,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA;AAC9C,IAAA,MAAM,IAAA,EAAM,QAAA,CAAS,QAAA,CAAS,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA;AAC9C,IAAA,MAAM,MAAA,EAAQ,gCAAA,GAAS,EAAK,GAAA,EAAK,OAAO,CAAA;AACxC,IAAA,MAAM,MAAA,EAAQ,gCAAA,GAAS,EAAK,GAAA,EAAK,OAAO,CAAA;AACxC,IAAA,MAAM,MAAA,EAAQ,gCAAA,GAAS,EAAK,GAAA,EAAK,OAAO,CAAA;AACxC,IAAA,OAAO,MAAA,GAAS,QAAA,GAAW,MAAA,GAAS,QAAA,GAAW,MAAA,GAAS,OAAA;AAAA,EAC1D,CAAC,CAAA;AAED,EAAA,GAAA,CAAI,QAAA,CAAS,QAAA,CAAS,OAAA,EAAS,CAAA,EAAG;AAChC,IAAA,OAAO,IAAA;AAAA,EACT;AAGA,EAAA,MAAM,UAAA,EAAiB,QAAA,CAAS,QAAQ,CAAA;AAGxC,EAAA,GAAA,CAAI,SAAA,CAAU,WAAA,CAAY,OAAA,IAAW,CAAA,EAAG;AACtC,IAAA,SAAA,CAAU,YAAA,EAAc,SAAA,CAAU,WAAA,CAAY,CAAC,CAAA;AAC/C,IAAA,SAAA,CAAU,KAAA,EAAO,SAAA;AAAA,EACnB;AACA,EAAA,OAAO,8BAAA,SAAiB,CAAA;AAC1B;AASA,SAAS,gBAAA,CACP,MAAA,EAC0B;AAC1B,EAAA,MAAM,QAAA,EAAiC,CAAC,CAAA;AACxC,EAAA,MAAM,SAAA,EAAuC,CAAC,CAAA;AAE9C,EAAA,+BAAA,MAAY,EAAQ,CAAC,EAAA,EAAA,GAAO;AAC1B,IAAA,GAAA,CAAI,CAAC,EAAA,CAAG,QAAA,EAAU;AAChB,MAAA,MAAA;AAAA,IACF;AACA,IAAA,MAAM,IAAA,EAAM,EAAA,CAAG,QAAA,CAAS,WAAA,CAAY,IAAA,CAAK,GAAG,CAAA;AAC5C,IAAA,GAAA,CAAI,CAAC,MAAA,CAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAA,EAAU,GAAG,CAAA,EAAG;AACxD,MAAA,OAAA,CAAQ,IAAA,CAAK,EAAE,CAAA;AACf,MAAA,QAAA,CAAS,GAAG,EAAA,EAAI,IAAA;AAAA,IAClB;AAAA,EACF,CAAC,CAAA;AACD,EAAA,OAAO,wCAAA,OAAyB,CAAA;AAClC;AAGA,IAAO,qBAAA,EAAQ,OAAA;ADyFf;AACE;AACA;AACF,kEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-concave/dist/cjs/index.cjs", "sourcesContent": [null, "import { distance } from \"@turf/distance\";\nimport { feature, featureCollection } from \"@turf/helpers\";\nimport {\n  Feature,\n  FeatureCollection,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { Units } from \"@turf/helpers\";\nimport { featureEach } from \"@turf/meta\";\nimport { tin } from \"@turf/tin\";\nimport { dissolve } from \"./lib/turf-dissolve.js\";\n\n/**\n * Takes a set of {@link Point|points} and returns a concave hull Polygon or MultiPolygon.\n * Internally, this uses [turf-tin](https://github.com/Turfjs/turf-tin) to generate geometries.\n *\n * @function\n * @param {FeatureCollection<Point>} points input points\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.maxEdge=Infinity] the length (in 'units') of an edge necessary for part of the\n * hull to become concave.\n * @param {string} [options.units='kilometers'] can be degrees, radians, miles, or kilometers\n * @returns {Feature<(Polygon|MultiPolygon)>|null} a concave hull (null value is returned if unable to compute hull)\n * @example\n * var points = turf.featureCollection([\n *   turf.point([-63.601226, 44.642643]),\n *   turf.point([-63.591442, 44.651436]),\n *   turf.point([-63.580799, 44.648749]),\n *   turf.point([-63.573589, 44.641788]),\n *   turf.point([-63.587665, 44.64533]),\n *   turf.point([-63.595218, 44.64765])\n * ]);\n * var options = {units: 'miles', maxEdge: 1};\n *\n * var hull = turf.concave(points, options);\n *\n * //addToMap\n * var addToMap = [points, hull]\n */\nfunction concave(\n  points: FeatureCollection<Point>,\n  options: { maxEdge?: number; units?: Units } = {}\n): Feature<Polygon | MultiPolygon> | null {\n  const maxEdge = options.maxEdge || Infinity;\n\n  const cleaned = removeDuplicates(points);\n\n  const tinPolys = tin(cleaned);\n  // calculate length of all edges and area of all triangles\n  // and remove triangles that fail the max length test\n  tinPolys.features = tinPolys.features.filter((triangle) => {\n    const pt1 = triangle.geometry.coordinates[0][0];\n    const pt2 = triangle.geometry.coordinates[0][1];\n    const pt3 = triangle.geometry.coordinates[0][2];\n    const dist1 = distance(pt1, pt2, options);\n    const dist2 = distance(pt2, pt3, options);\n    const dist3 = distance(pt1, pt3, options);\n    return dist1 <= maxEdge && dist2 <= maxEdge && dist3 <= maxEdge;\n  });\n\n  if (tinPolys.features.length < 1) {\n    return null;\n  }\n\n  // merge the adjacent triangles\n  const dissolved: any = dissolve(tinPolys);\n\n  // geojson-dissolve always returns a MultiPolygon\n  if (dissolved.coordinates.length === 1) {\n    dissolved.coordinates = dissolved.coordinates[0];\n    dissolved.type = \"Polygon\";\n  }\n  return feature(dissolved);\n}\n\n/**\n * Removes duplicated points in a collection returning a new collection\n *\n * @private\n * @param {FeatureCollection<Point>} points to be cleaned\n * @returns {FeatureCollection<Point>} cleaned set of points\n */\nfunction removeDuplicates(\n  points: FeatureCollection<Point>\n): FeatureCollection<Point> {\n  const cleaned: Array<Feature<Point>> = [];\n  const existing: { [key: string]: boolean } = {};\n\n  featureEach(points, (pt) => {\n    if (!pt.geometry) {\n      return;\n    }\n    const key = pt.geometry.coordinates.join(\"-\");\n    if (!Object.prototype.hasOwnProperty.call(existing, key)) {\n      cleaned.push(pt);\n      existing[key] = true;\n    }\n  });\n  return featureCollection(cleaned);\n}\n\nexport { concave };\nexport default concave;\n", "import {\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  Polygon,\n} from \"geojson\";\nimport { clone } from \"@turf/clone\";\nimport { isObject } from \"@turf/helpers\";\nimport { getType } from \"@turf/invariant\";\nimport { flattenEach } from \"@turf/meta\";\nimport { lineDissolve } from \"./turf-line-dissolve.js\";\nimport { polygonDissolve } from \"./turf-polygon-dissolve.js\";\n\n/**\n * Transform function: attempts to dissolve geojson objects where possible\n * [GeoJSON] -> GeoJSON geometry\n *\n * @private\n * @param {FeatureCollection<LineString|MultiLineString|Polygon|MultiPolygon>} geojson Features to dissolved\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.mutate=false] Prevent input mutation\n * @returns {Feature<MultiLineString|MultiPolygon>} Dissolved Features\n */\nfunction dissolve(\n  geojson: FeatureCollection<\n    LineString | MultiLineString | Polygon | MultiPolygon\n  >,\n  options: {\n    mutate?: boolean;\n  } = {}\n): Feature<LineString | MultiLineString | Polygon | MultiPolygon> | null {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) {\n    throw new Error(\"options is invalid\");\n  }\n  const mutate = options.mutate;\n\n  // Validation\n  if (getType(geojson) !== \"FeatureCollection\") {\n    throw new Error(\"geojson must be a FeatureCollection\");\n  }\n  if (!geojson.features.length) {\n    throw new Error(\"geojson is empty\");\n  }\n\n  // Clone geojson to avoid side effects\n  // Topojson modifies in place, so we need to deep clone first\n  if (mutate === false || mutate === undefined) {\n    geojson = clone(geojson);\n  }\n\n  // Assert homogenity\n  const type = getHomogenousType(geojson);\n  if (!type) {\n    throw new Error(\"geojson must be homogenous\");\n  }\n\n  // Data => Typescript hack\n  const data: any = geojson;\n\n  switch (type) {\n    case \"LineString\":\n      return lineDissolve(data, options);\n    case \"Polygon\":\n      return polygonDissolve(data, options);\n    default:\n      throw new Error(type + \" is not supported\");\n  }\n}\n\n/**\n * Checks if GeoJSON is Homogenous\n *\n * @private\n * @param {GeoJSON} geojson GeoJSON\n * @returns {string|null} Homogenous type or null if multiple types\n */\nfunction getHomogenousType(geojson: any) {\n  const types: { [key: string]: boolean } = {};\n  flattenEach(geojson, (feature) => {\n    types[feature.geometry.type] = true;\n  });\n  const keys = Object.keys(types);\n  if (keys.length === 1) {\n    return keys[0];\n  }\n  return null;\n}\n\nexport { dissolve };\nexport default dissolve;\n", "import {\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n} from \"geojson\";\nimport { clone } from \"@turf/clone\";\nimport { isObject, lineString, multiLineString } from \"@turf/helpers\";\nimport { getType } from \"@turf/invariant\";\nimport { lineReduce } from \"@turf/meta\";\n\n/**\n * Merges all connected (non-forking, non-junctioning) line strings into single lineStrings.\n * [LineString] -> LineString|MultiLineString\n *\n * @param {FeatureCollection<LineString|MultiLineString>} geojson Lines to dissolve\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.mutate=false] Prevent input mutation\n * @returns {Feature<LineString|MultiLineString>} Dissolved lines\n */\nfunction lineDissolve(\n  geojson: FeatureCollection<LineString | MultiLineString>,\n  options: { mutate?: boolean } = {}\n): Feature<LineString | MultiLineString> | null {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) {\n    throw new Error(\"options is invalid\");\n  }\n  const mutate = options.mutate;\n\n  // Validation\n  if (getType(geojson) !== \"FeatureCollection\") {\n    throw new Error(\"geojson must be a FeatureCollection\");\n  }\n  if (!geojson.features.length) {\n    throw new Error(\"geojson is empty\");\n  }\n\n  // Clone geojson to avoid side effects\n  if (mutate === false || mutate === undefined) {\n    geojson = clone(geojson);\n  }\n\n  const result: any[] = [];\n  const lastLine = lineReduce(\n    geojson,\n    (previousLine: any, currentLine: any) => {\n      // Attempt to merge this LineString with the other LineStrings, updating\n      // the reference as it is merged with others and grows.\n      const merged = mergeLineStrings(previousLine, currentLine);\n\n      // Accumulate the merged LineString\n      if (merged) {\n        return merged;\n        // Put the unmerged LineString back into the list\n      } else {\n        result.push(previousLine);\n        return currentLine;\n      }\n    }\n  );\n  // Append the last line\n  if (lastLine) {\n    result.push(lastLine);\n  }\n\n  // Return null if no lines were dissolved\n  if (!result.length) {\n    return null;\n    // Return LineString if only 1 line was dissolved\n  } else if (result.length === 1) {\n    return result[0];\n    // Return MultiLineString if multiple lines were dissolved with gaps\n  } else {\n    return multiLineString(\n      result.map((line) => {\n        return line.coordinates;\n      })\n    );\n  }\n}\n\n// [Number, Number] -> String\nfunction coordId(coord: number[]) {\n  return coord[0].toString() + \",\" + coord[1].toString();\n}\n\n/**\n * LineString, LineString -> LineString\n *\n * @private\n * @param {Feature<LineString>} a line1\n * @param {Feature<LineString>} b line2\n * @returns {Feature<LineString>|null} Merged LineString\n */\nfunction mergeLineStrings(a: Feature<LineString>, b: Feature<LineString>) {\n  const coords1 = a.geometry.coordinates;\n  const coords2 = b.geometry.coordinates;\n\n  const s1 = coordId(coords1[0]);\n  const e1 = coordId(coords1[coords1.length - 1]);\n  const s2 = coordId(coords2[0]);\n  const e2 = coordId(coords2[coords2.length - 1]);\n\n  // TODO: handle case where more than one of these is true!\n  let coords;\n  if (s1 === e2) {\n    coords = coords2.concat(coords1.slice(1));\n  } else if (s2 === e1) {\n    coords = coords1.concat(coords2.slice(1));\n  } else if (s1 === s2) {\n    coords = coords1.slice(1).reverse().concat(coords2);\n  } else if (e1 === e2) {\n    coords = coords1.concat(coords2.reverse().slice(1));\n  } else {\n    return null;\n  }\n\n  return lineString(coords);\n}\n\nexport { lineDissolve };\nexport default lineDissolve;\n", "import { Feature, FeatureCollection, MultiPolygon, Polygon } from \"geojson\";\nimport { clone } from \"@turf/clone\";\nimport { geometryCollection } from \"@turf/helpers\";\nimport { getType } from \"@turf/invariant\";\nimport { flattenEach } from \"@turf/meta\";\nimport { merge } from \"topojson-client\";\nimport { topology } from \"topojson-server\";\n\n/**\n * Dissolves all overlapping (Multi)Polygon\n *\n * @param {FeatureCollection<Polygon|MultiPolygon>} geojson Polygons to dissolve\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.mutate=false] Prevent input mutation\n * @returns {Feature<Polygon|MultiPolygon>} Dissolved Polygons\n */\nfunction polygonDissolve(\n  geojson: FeatureCollection<Polygon | MultiPolygon>,\n  options: { mutate?: boolean } = {}\n): Feature<Polygon | MultiPolygon> | null {\n  // Validation\n  if (getType(geojson) !== \"FeatureCollection\") {\n    throw new Error(\"geojson must be a FeatureCollection\");\n  }\n  if (!geojson.features.length) {\n    throw new Error(\"geojson is empty\");\n  }\n\n  // Clone geojson to avoid side effects\n  // Topojson modifies in place, so we need to deep clone first\n  if (options.mutate === false || options.mutate === undefined) {\n    geojson = clone(geojson);\n  }\n\n  const geoms: any[] = [];\n  flattenEach(geojson, (feature) => {\n    geoms.push(feature.geometry);\n  });\n  const topo: any = topology({ geoms: geometryCollection(geoms).geometry });\n  const merged: any = merge(topo, topo.objects.geoms.geometries);\n  return merged;\n}\n\nexport { polygonDissolve };\nexport default polygonDissolve;\n"]}