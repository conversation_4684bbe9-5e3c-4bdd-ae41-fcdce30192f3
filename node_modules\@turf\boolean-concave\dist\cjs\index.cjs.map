{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-concave/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,4CAAwB;AAcxB,SAAS,cAAA,CAAe,OAAA,EAAqC;AAE3D,EAAA,MAAM,OAAA,EAAS,gCAAA,OAAe,CAAA,CAAE,WAAA;AAChC,EAAA,GAAA,CAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,GAAU,CAAA,EAAG;AACzB,IAAA,OAAO,KAAA;AAAA,EACT;AAEA,EAAA,IAAI,KAAA,EAAO,KAAA;AACX,EAAA,MAAM,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA;AAC7B,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,CAAA,EAAG,CAAA,EAAA,EAAK;AAC1B,IAAA,MAAM,IAAA,EAAM,MAAA,CAAO,CAAC,CAAA,CAAA,CAAG,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAA,CAAG,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC,CAAA,CAAE,CAAC,CAAA;AAChE,IAAA,MAAM,IAAA,EAAM,MAAA,CAAO,CAAC,CAAA,CAAA,CAAG,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAA,CAAG,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC,CAAA,CAAE,CAAC,CAAA;AAChE,IAAA,MAAM,IAAA,EAAM,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAA,CAAG,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC,CAAA,CAAE,CAAC,CAAA;AACtD,IAAA,MAAM,IAAA,EAAM,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAA,CAAG,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC,CAAA,CAAE,CAAC,CAAA;AACtD,IAAA,MAAM,cAAA,EAAgB,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,GAAA;AACxC,IAAA,GAAA,CAAI,EAAA,IAAM,CAAA,EAAG;AACX,MAAA,KAAA,EAAO,cAAA,EAAgB,CAAA;AAAA,IACzB,EAAA,KAAA,GAAA,CAAW,KAAA,IAAS,cAAA,EAAgB,CAAA,EAAG;AACrC,MAAA,OAAO,IAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,KAAA;AACT;AAGA,IAAO,6BAAA,EAAQ,cAAA;ADhBf;AACE;AACA;AACF,wFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-concave/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Polygon } from \"geojson\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Takes a polygon and return true or false as to whether it is concave or not.\n *\n * @function\n * @param {Feature<Polygon>} polygon to be evaluated\n * @returns {boolean} true/false\n * @example\n * var convexPolygon = turf.polygon([[[0,0],[0,1],[1,1],[1,0],[0,0]]]);\n *\n * turf.booleanConcave(convexPolygon)\n * //=false\n */\nfunction booleanConcave(polygon: Feature<Polygon> | Polygon) {\n  // Taken from https://stackoverflow.com/a/1881201 & https://stackoverflow.com/a/25304159\n  const coords = getGeom(polygon).coordinates;\n  if (coords[0].length <= 4) {\n    return false;\n  }\n\n  let sign = false;\n  const n = coords[0].length - 1;\n  for (let i = 0; i < n; i++) {\n    const dx1 = coords[0][(i + 2) % n][0] - coords[0][(i + 1) % n][0];\n    const dy1 = coords[0][(i + 2) % n][1] - coords[0][(i + 1) % n][1];\n    const dx2 = coords[0][i][0] - coords[0][(i + 1) % n][0];\n    const dy2 = coords[0][i][1] - coords[0][(i + 1) % n][1];\n    const zcrossproduct = dx1 * dy2 - dy1 * dx2;\n    if (i === 0) {\n      sign = zcrossproduct > 0;\n    } else if (sign !== zcrossproduct > 0) {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport { booleanConcave };\nexport default booleanConcave;\n"]}