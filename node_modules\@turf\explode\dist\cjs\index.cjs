"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _meta = require('@turf/meta');
var _helpers = require('@turf/helpers');
function explode(geojson) {
  const points = [];
  if (geojson.type === "FeatureCollection") {
    _meta.featureEach.call(void 0, geojson, function(feature) {
      _meta.coordEach.call(void 0, feature, function(coord) {
        points.push(_helpers.point.call(void 0, coord, feature.properties));
      });
    });
  } else if (geojson.type === "Feature") {
    _meta.coordEach.call(void 0, geojson, function(coord) {
      points.push(_helpers.point.call(void 0, coord, geojson.properties));
    });
  } else {
    _meta.coordEach.call(void 0, geojson, function(coord) {
      points.push(_helpers.point.call(void 0, coord));
    });
  }
  return _helpers.featureCollection.call(void 0, points);
}
var turf_explode_default = explode;



exports.default = turf_explode_default; exports.explode = explode;
//# sourceMappingURL=index.cjs.map