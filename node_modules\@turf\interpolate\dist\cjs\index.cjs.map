{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-interpolate/dist/cjs/index.cjs", "../../index.js"], "names": [], "mappings": "AAAA;ACAA,kCAAqB;AACrB,yCAAwB;AACxB,6CAA0B;AAC1B,0CAAyB;AACzB,0CAAyB;AACzB,+CAA2B;AAC3B,mDAA6B;AAC7B,oCAAsB;AACtB,wCAAgD;AAChD,kCAA4B;AAC5B,4CAA6B;AA4B7B,SAAS,WAAA,CAAY,MAAA,EAAQ,QAAA,EAAU,OAAA,EAAS;AAE9C,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,OAAO,QAAA,IAAY,QAAA,EAAU,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACrE,EAAA,IAAI,SAAA,EAAW,OAAA,CAAQ,QAAA;AACvB,EAAA,IAAI,SAAA,EAAW,OAAA,CAAQ,QAAA;AACvB,EAAA,IAAI,OAAA,EAAS,OAAA,CAAQ,MAAA;AACrB,EAAA,IAAI,IAAA,EAAM,OAAA,CAAQ,IAAA;AAGlB,EAAA,GAAA,CAAI,CAAC,MAAA,EAAQ,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACjD,EAAA,qCAAA,MAAa,EAAQ,OAAA,EAAS,2BAA2B,CAAA;AACzD,EAAA,GAAA,CAAI,CAAC,QAAA,EAAU,MAAM,IAAI,KAAA,CAAM,sBAAsB,CAAA;AACrD,EAAA,GAAA,CAAI,OAAA,IAAW,KAAA,EAAA,GAAa,OAAO,OAAA,IAAW,QAAA;AAC5C,IAAA,MAAM,IAAI,KAAA,CAAM,yBAAyB,CAAA;AAG3C,EAAA,SAAA,EAAW,SAAA,GAAY,WAAA;AACvB,EAAA,SAAA,EAAW,SAAA,GAAY,QAAA;AACvB,EAAA,OAAA,EAAS,OAAA,GAAU,CAAA;AAEnB,EAAA,IAAA,EAAM,IAAA,GAAA,KAAA,EAAA,IAAA,EAAO,wBAAA,MAAW,CAAA;AACxB,EAAA,mCAAA,GAAgB,CAAA;AAChB,EAAA,IAAI,IAAA;AACJ,EAAA,OAAA,CAAQ,QAAA,EAAU;AAAA,IAChB,KAAK,OAAA;AAAA,IACL,KAAK,QAAA;AACH,MAAA,KAAA,EAAO,kCAAA,GAAU,EAAK,QAAA,EAAU,OAAO,CAAA;AACvC,MAAA,KAAA;AAAA,IACF,KAAK,QAAA;AAAA,IACL,KAAK,SAAA;AACH,MAAA,KAAA,EAAO,oCAAA,GAAW,EAAK,QAAA,EAAU,OAAO,CAAA;AACxC,MAAA,KAAA;AAAA,IACF,KAAK,KAAA;AAAA,IACL,KAAK,OAAA;AACH,MAAA,KAAA,EAAO,8BAAA,GAAQ,EAAK,QAAA,EAAU,OAAO,CAAA;AACrC,MAAA,KAAA;AAAA,IACF,KAAK,UAAA;AAAA,IACL,KAAK,WAAA;AACH,MAAA,KAAA,EAAO,wCAAA,GAAa,EAAK,QAAA,EAAU,OAAO,CAAA;AAC1C,MAAA,KAAA;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,kBAAkB,CAAA;AAAA,EACtC;AACA,EAAA,IAAI,QAAA,EAAU,CAAC,CAAA;AACf,EAAA,+BAAA,IAAY,EAAM,QAAA,CAAU,WAAA,EAAa;AACvC,IAAA,IAAI,GAAA,EAAK,CAAA;AACT,IAAA,IAAI,GAAA,EAAK,CAAA;AAET,IAAA,+BAAA,MAAY,EAAQ,QAAA,CAAU,KAAA,EAAO;AACnC,MAAA,IAAI,UAAA,EACF,SAAA,IAAa,QAAA,EAAU,YAAA,EAAc,gCAAA,WAAoB,CAAA;AAC3D,MAAA,IAAI,EAAA,EAAI,gCAAA,SAAS,EAAW,KAAA,EAAO,OAAO,CAAA;AAC1C,MAAA,IAAI,MAAA;AAEJ,MAAA,GAAA,CAAI,SAAA,IAAa,KAAA,CAAA,EAAW,OAAA,EAAS,KAAA,CAAM,UAAA,CAAW,QAAQ,CAAA;AAC9D,MAAA,GAAA,CAAI,OAAA,IAAW,KAAA,CAAA,EAAW,OAAA,EAAS,KAAA,CAAM,QAAA,CAAS,WAAA,CAAY,CAAC,CAAA;AAC/D,MAAA,GAAA,CAAI,OAAA,IAAW,KAAA,CAAA,EAAW,MAAM,IAAI,KAAA,CAAM,mBAAmB,CAAA;AAC7D,MAAA,GAAA,CAAI,EAAA,IAAM,CAAA,EAAG,GAAA,EAAK,MAAA;AAClB,MAAA,IAAI,EAAA,EAAI,EAAA,EAAM,IAAA,CAAK,GAAA,CAAI,CAAA,EAAG,MAAM,CAAA;AAChC,MAAA,GAAA,GAAM,CAAA;AACN,MAAA,GAAA,GAAM,EAAA,EAAI,MAAA;AAAA,IACZ,CAAC,CAAA;AAED,IAAA,IAAI,WAAA,EAAa,0BAAA,WAAiB,CAAA;AAClC,IAAA,UAAA,CAAW,UAAA,CAAW,QAAQ,EAAA,EAAI,GAAA,EAAK,EAAA;AACvC,IAAA,OAAA,CAAQ,IAAA,CAAK,UAAU,CAAA;AAAA,EACzB,CAAC,CAAA;AACD,EAAA,OAAO,wCAAA,OAAyB,CAAA;AAClC;AAGA,IAAO,yBAAA,EAAQ,WAAA;ADrCf;AACE;AACA;AACF,8EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-interpolate/dist/cjs/index.cjs", "sourcesContent": [null, "import { bbox } from \"@turf/bbox\";\nimport { hexGrid } from \"@turf/hex-grid\";\nimport { pointGrid } from \"@turf/point-grid\";\nimport { distance } from \"@turf/distance\";\nimport { centroid } from \"@turf/centroid\";\nimport { squareGrid } from \"@turf/square-grid\";\nimport { triangleGrid } from \"@turf/triangle-grid\";\nimport { clone } from \"@turf/clone\";\nimport { featureCollection, validateBBox } from \"@turf/helpers\";\nimport { featureEach } from \"@turf/meta\";\nimport { collectionOf } from \"@turf/invariant\";\n\n/**\n * Takes a set of points and estimates their 'property' values on a grid using the [Inverse Distance Weighting (IDW) method](https://en.wikipedia.org/wiki/Inverse_distance_weighting).\n *\n * @function\n * @param {FeatureCollection<Point>} points with known value\n * @param {number} cellSize the distance across each grid point\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.gridType='square'] defines the output format based on a Grid Type (options: 'square' | 'point' | 'hex' | 'triangle')\n * @param {string} [options.property='elevation'] the property name in `points` from which z-values will be pulled, zValue fallbacks to 3rd coordinate if no property exists.\n * @param {string} [options.units='kilometers'] used in calculating cellSize, can be degrees, radians, miles, or kilometers\n * @param {number} [options.weight=1] exponent regulating the distance-decay weighting\n * @param {BBox}   [options.bbox=bbox(points)] Bounding Box Array [west, south, east, north] associated with the FeatureCollection.\n * @returns {FeatureCollection<Point|Polygon>} grid of points or polygons with interpolated 'property'\n * @example\n * var points = turf.randomPoint(30, {bbox: [50, 30, 70, 50]});\n *\n * // add a random property to each point\n * turf.featureEach(points, function(point) {\n *     point.properties.solRad = Math.random() * 50;\n * });\n * var options = {gridType: 'points', property: 'solRad', units: 'miles'};\n * var grid = turf.interpolate(points, 100, options);\n *\n * //addToMap\n * var addToMap = [grid];\n */\nfunction interpolate(points, cellSize, options) {\n  // Optional parameters\n  options = options || {};\n  if (typeof options !== \"object\") throw new Error(\"options is invalid\");\n  var gridType = options.gridType;\n  var property = options.property;\n  var weight = options.weight;\n  var box = options.bbox;\n\n  // validation\n  if (!points) throw new Error(\"points is required\");\n  collectionOf(points, \"Point\", \"input must contain Points\");\n  if (!cellSize) throw new Error(\"cellSize is required\");\n  if (weight !== undefined && typeof weight !== \"number\")\n    throw new Error(\"weight must be a number\");\n\n  // default values\n  property = property || \"elevation\";\n  gridType = gridType || \"square\";\n  weight = weight || 1;\n\n  box = box ?? bbox(points);\n  validateBBox(box);\n  var grid;\n  switch (gridType) {\n    case \"point\":\n    case \"points\":\n      grid = pointGrid(box, cellSize, options);\n      break;\n    case \"square\":\n    case \"squares\":\n      grid = squareGrid(box, cellSize, options);\n      break;\n    case \"hex\":\n    case \"hexes\":\n      grid = hexGrid(box, cellSize, options);\n      break;\n    case \"triangle\":\n    case \"triangles\":\n      grid = triangleGrid(box, cellSize, options);\n      break;\n    default:\n      throw new Error(\"invalid gridType\");\n  }\n  var results = [];\n  featureEach(grid, function (gridFeature) {\n    var zw = 0;\n    var sw = 0;\n    // calculate the distance from each input point to the grid points\n    featureEach(points, function (point) {\n      var gridPoint =\n        gridType === \"point\" ? gridFeature : centroid(gridFeature);\n      var d = distance(gridPoint, point, options);\n      var zValue;\n      // property has priority for zValue, fallbacks to 3rd coordinate from geometry\n      if (property !== undefined) zValue = point.properties[property];\n      if (zValue === undefined) zValue = point.geometry.coordinates[2];\n      if (zValue === undefined) throw new Error(\"zValue is missing\");\n      if (d === 0) zw = zValue;\n      var w = 1.0 / Math.pow(d, weight);\n      sw += w;\n      zw += w * zValue;\n    });\n    // write interpolated value for each grid point\n    var newFeature = clone(gridFeature);\n    newFeature.properties[property] = zw / sw;\n    results.push(newFeature);\n  });\n  return featureCollection(results);\n}\n\nexport { interpolate };\nexport default interpolate;\n"]}