{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-along/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wCAAwB;AACxB,gDAA4B;AAC5B,0CAA4C;AAC5C,wCAA6B;AAC7B,4CAAwB;AAoBxB,SAAS,KAAA,CACP,IAAA,EACA,QAAA,EACA,QAAA,EAA6B,CAAC,CAAA,EACd;AAEhB,EAAA,MAAM,KAAA,EAAO,gCAAA,IAAY,CAAA;AACzB,EAAA,MAAM,OAAA,EAAS,IAAA,CAAK,WAAA;AACpB,EAAA,IAAI,UAAA,EAAY,CAAA;AAChB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,IAAA,GAAA,CAAI,SAAA,GAAY,UAAA,GAAa,EAAA,IAAM,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG;AACpD,MAAA,KAAA;AAAA,IACF,EAAA,KAAA,GAAA,CAAW,UAAA,GAAa,QAAA,EAAU;AAChC,MAAA,MAAM,SAAA,EAAW,SAAA,EAAW,SAAA;AAC5B,MAAA,GAAA,CAAI,CAAC,QAAA,EAAU;AACb,QAAA,OAAO,4BAAA,MAAM,CAAO,CAAC,CAAC,CAAA;AAAA,MACxB,EAAA,KAAO;AACL,QAAA,MAAM,UAAA,EAAY,8BAAA,MAAQ,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,EAAA,EAAI,CAAC,CAAC,EAAA,EAAI,GAAA;AACtD,QAAA,MAAM,aAAA,EAAe,sCAAA;AAAA,UACnB,MAAA,CAAO,CAAC,CAAA;AAAA,UACR,QAAA;AAAA,UACA,SAAA;AAAA,UACA;AAAA,QACF,CAAA;AACA,QAAA,OAAO,YAAA;AAAA,MACT;AAAA,IACF,EAAA,KAAO;AACL,MAAA,UAAA,GAAa,gCAAA,MAAgB,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,EAAG,OAAO,CAAA;AAAA,IAChE;AAAA,EACF;AACA,EAAA,OAAO,4BAAA,MAAM,CAAO,MAAA,CAAO,OAAA,EAAS,CAAC,CAAC,CAAA;AACxC;AAGA,IAAO,mBAAA,EAAQ,KAAA;ADzBf;AACE;AACA;AACF,4DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-along/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, LineString, Point } from \"geojson\";\nimport { bearing } from \"@turf/bearing\";\nimport { destination } from \"@turf/destination\";\nimport { distance as measureDistance } from \"@turf/distance\";\nimport { point, Units } from \"@turf/helpers\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Takes a {@link LineString} and returns a {@link Point} at a specified distance along the line.\n *\n * @function\n * @param {Feature<LineString>|LineString} line input line\n * @param {number} distance distance along the line\n * @param {Object} [options] Optional parameters\n * @param {Units} [options.units=\"kilometers\"] can be degrees, radians, miles, or kilometers\n * @returns {Feature<Point>} Point `distance` `units` along the line\n * @example\n * var line = turf.lineString([[-83, 30], [-84, 36], [-78, 41]]);\n * var options = {units: 'miles'};\n *\n * var along = turf.along(line, 200, options);\n *\n * //addToMap\n * var addToMap = [along, line]\n */\nfunction along(\n  line: Feature<LineString> | LineString,\n  distance: number,\n  options: { units?: Units } = {}\n): Feature<Point> {\n  // Get Coords\n  const geom = getGeom(line);\n  const coords = geom.coordinates;\n  let travelled = 0;\n  for (let i = 0; i < coords.length; i++) {\n    if (distance >= travelled && i === coords.length - 1) {\n      break;\n    } else if (travelled >= distance) {\n      const overshot = distance - travelled;\n      if (!overshot) {\n        return point(coords[i]);\n      } else {\n        const direction = bearing(coords[i], coords[i - 1]) - 180;\n        const interpolated = destination(\n          coords[i],\n          overshot,\n          direction,\n          options\n        );\n        return interpolated;\n      }\n    } else {\n      travelled += measureDistance(coords[i], coords[i + 1], options);\n    }\n  }\n  return point(coords[coords.length - 1]);\n}\n\nexport { along };\nexport default along;\n"]}