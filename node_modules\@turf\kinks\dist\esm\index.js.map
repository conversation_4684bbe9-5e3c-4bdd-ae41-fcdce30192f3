{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { point } from \"@turf/helpers\";\n\n/**\n * Takes a {@link LineString|linestring}, {@link MultiLineString|multi-linestring},\n * {@link MultiPolygon|multi-polygon} or {@link Polygon|polygon} and\n * returns {@link Point|points} at all self-intersections.\n *\n * @function\n * @param {Feature<LineString|MultiLineString|MultiPolygon|Polygon>} featureIn input feature\n * @returns {FeatureCollection<Point>} self-intersections\n * @example\n * var poly = turf.polygon([[\n *   [-12.034835, 8.901183],\n *   [-12.060413, 8.899826],\n *   [-12.03638, 8.873199],\n *   [-12.059383, 8.871418],\n *   [-12.034835, 8.901183]\n * ]]);\n *\n * var kinks = turf.kinks(poly);\n *\n * //addToMap\n * var addToMap = [poly, kinks]\n */\nfunction kinks<T extends LineString | MultiLineString | Polygon | MultiPolygon>(\n  featureIn: Feature<T> | T\n): FeatureCollection<Point> {\n  let coordinates: any;\n  let feature: any;\n  const results: FeatureCollection<Point> = {\n    type: \"FeatureCollection\",\n    features: [],\n  };\n  if (featureIn.type === \"Feature\") {\n    feature = featureIn.geometry;\n  } else {\n    feature = featureIn;\n  }\n  if (feature.type === \"LineString\") {\n    coordinates = [feature.coordinates];\n  } else if (feature.type === \"MultiLineString\") {\n    coordinates = feature.coordinates;\n  } else if (feature.type === \"MultiPolygon\") {\n    coordinates = [].concat(...feature.coordinates);\n  } else if (feature.type === \"Polygon\") {\n    coordinates = feature.coordinates;\n  } else {\n    throw new Error(\n      \"Input must be a LineString, MultiLineString, \" +\n        \"Polygon, or MultiPolygon Feature or Geometry\"\n    );\n  }\n  coordinates.forEach((line1: any) => {\n    coordinates.forEach((line2: any) => {\n      for (let i = 0; i < line1.length - 1; i++) {\n        // start iteration at i, intersections for k < i have already\n        // been checked in previous outer loop iterations\n        for (let k = i; k < line2.length - 1; k++) {\n          if (line1 === line2) {\n            // segments are adjacent and always share a vertex, not a kink\n            if (Math.abs(i - k) === 1) {\n              continue;\n            }\n            // first and last segment in a closed lineString or ring always share a vertex, not a kink\n            if (\n              // segments are first and last segment of lineString\n              i === 0 &&\n              k === line1.length - 2 &&\n              // lineString is closed\n              line1[i][0] === line1[line1.length - 1][0] &&\n              line1[i][1] === line1[line1.length - 1][1]\n            ) {\n              continue;\n            }\n          }\n\n          const intersection: any = lineIntersects(\n            line1[i][0],\n            line1[i][1],\n            line1[i + 1][0],\n            line1[i + 1][1],\n            line2[k][0],\n            line2[k][1],\n            line2[k + 1][0],\n            line2[k + 1][1]\n          );\n          if (intersection) {\n            results.features.push(point([intersection[0], intersection[1]]));\n          }\n        }\n      }\n    });\n  });\n  return results;\n}\n\n// modified from http://jsfiddle.net/justin_c_rounds/Gd2S2/light/\nfunction lineIntersects(\n  line1StartX: any,\n  line1StartY: any,\n  line1EndX: any,\n  line1EndY: any,\n  line2StartX: any,\n  line2StartY: any,\n  line2EndX: any,\n  line2EndY: any\n) {\n  // if the lines intersect, the result contains the x and y of the\n  // intersection (treating the lines as infinite) and booleans for whether\n  // line segment 1 or line segment 2 contain the point\n  let denominator;\n  let a;\n  let b;\n  let numerator1;\n  let numerator2;\n  const result = {\n    x: null,\n    y: null,\n    onLine1: false,\n    onLine2: false,\n  };\n  denominator =\n    (line2EndY - line2StartY) * (line1EndX - line1StartX) -\n    (line2EndX - line2StartX) * (line1EndY - line1StartY);\n  if (denominator === 0) {\n    if (result.x !== null && result.y !== null) {\n      return result;\n    } else {\n      return false;\n    }\n  }\n  a = line1StartY - line2StartY;\n  b = line1StartX - line2StartX;\n  numerator1 = (line2EndX - line2StartX) * a - (line2EndY - line2StartY) * b;\n  numerator2 = (line1EndX - line1StartX) * a - (line1EndY - line1StartY) * b;\n  a = numerator1 / denominator;\n  b = numerator2 / denominator;\n\n  // if we cast these lines infinitely in both directions, they intersect here:\n  result.x = line1StartX + a * (line1EndX - line1StartX);\n  result.y = line1StartY + a * (line1EndY - line1StartY);\n\n  // if line1 is a segment and line2 is infinite, they intersect if:\n  if (a >= 0 && a <= 1) {\n    result.onLine1 = true;\n  }\n  // if line2 is a segment and line1 is infinite, they intersect if:\n  if (b >= 0 && b <= 1) {\n    result.onLine2 = true;\n  }\n  // if line1 and line2 are segments, they intersect if both of the above are true\n  if (result.onLine1 && result.onLine2) {\n    return [result.x, result.y];\n  } else {\n    return false;\n  }\n}\n\nexport { kinks };\nexport default kinks;\n"], "mappings": ";AASA,SAAS,aAAa;AAwBtB,SAAS,MACP,WAC0B;AAC1B,MAAI;AACJ,MAAI;AACJ,QAAM,UAAoC;AAAA,IACxC,MAAM;AAAA,IACN,UAAU,CAAC;AAAA,EACb;AACA,MAAI,UAAU,SAAS,WAAW;AAChC,cAAU,UAAU;AAAA,EACtB,OAAO;AACL,cAAU;AAAA,EACZ;AACA,MAAI,QAAQ,SAAS,cAAc;AACjC,kBAAc,CAAC,QAAQ,WAAW;AAAA,EACpC,WAAW,QAAQ,SAAS,mBAAmB;AAC7C,kBAAc,QAAQ;AAAA,EACxB,WAAW,QAAQ,SAAS,gBAAgB;AAC1C,kBAAc,CAAC,EAAE,OAAO,GAAG,QAAQ,WAAW;AAAA,EAChD,WAAW,QAAQ,SAAS,WAAW;AACrC,kBAAc,QAAQ;AAAA,EACxB,OAAO;AACL,UAAM,IAAI;AAAA,MACR;AAAA,IAEF;AAAA,EACF;AACA,cAAY,QAAQ,CAAC,UAAe;AAClC,gBAAY,QAAQ,CAAC,UAAe;AAClC,eAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AAGzC,iBAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AACzC,cAAI,UAAU,OAAO;AAEnB,gBAAI,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG;AACzB;AAAA,YACF;AAEA;AAAA;AAAA,cAEE,MAAM,KACN,MAAM,MAAM,SAAS;AAAA,cAErB,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,KACzC,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC;AAAA,cACzC;AACA;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,eAAoB;AAAA,YACxB,MAAM,CAAC,EAAE,CAAC;AAAA,YACV,MAAM,CAAC,EAAE,CAAC;AAAA,YACV,MAAM,IAAI,CAAC,EAAE,CAAC;AAAA,YACd,MAAM,IAAI,CAAC,EAAE,CAAC;AAAA,YACd,MAAM,CAAC,EAAE,CAAC;AAAA,YACV,MAAM,CAAC,EAAE,CAAC;AAAA,YACV,MAAM,IAAI,CAAC,EAAE,CAAC;AAAA,YACd,MAAM,IAAI,CAAC,EAAE,CAAC;AAAA,UAChB;AACA,cAAI,cAAc;AAChB,oBAAQ,SAAS,KAAK,MAAM,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AAAA,UACjE;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAGA,SAAS,eACP,aACA,aACA,WACA,WACA,aACA,aACA,WACA,WACA;AAIA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,GAAG;AAAA,IACH,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACA,iBACG,YAAY,gBAAgB,YAAY,gBACxC,YAAY,gBAAgB,YAAY;AAC3C,MAAI,gBAAgB,GAAG;AACrB,QAAI,OAAO,MAAM,QAAQ,OAAO,MAAM,MAAM;AAC1C,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,gBAAc,YAAY,eAAe,KAAK,YAAY,eAAe;AACzE,gBAAc,YAAY,eAAe,KAAK,YAAY,eAAe;AACzE,MAAI,aAAa;AACjB,MAAI,aAAa;AAGjB,SAAO,IAAI,cAAc,KAAK,YAAY;AAC1C,SAAO,IAAI,cAAc,KAAK,YAAY;AAG1C,MAAI,KAAK,KAAK,KAAK,GAAG;AACpB,WAAO,UAAU;AAAA,EACnB;AAEA,MAAI,KAAK,KAAK,KAAK,GAAG;AACpB,WAAO,UAAU;AAAA,EACnB;AAEA,MAAI,OAAO,WAAW,OAAO,SAAS;AACpC,WAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAAA,EAC5B,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAGA,IAAO,qBAAQ;", "names": []}