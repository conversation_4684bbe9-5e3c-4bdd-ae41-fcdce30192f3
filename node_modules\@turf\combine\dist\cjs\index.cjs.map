{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-combine/dist/cjs/index.cjs", "../../index.ts"], "names": ["feature"], "mappings": "AAAA;ACUA,wCAA2C;AAC3C,kCAA4B;AAoB5B,SAAS,OAAA,CACP,EAAA,EAGA;AACA,EAAA,IAAI,OAAA,EAAS;AAAA,IACX,UAAA,EAAY;AAAA,MACV,WAAA,EAAa,CAAC,CAAA;AAAA,MACd,UAAA,EAAY,CAAC;AAAA,IACf,CAAA;AAAA,IACA,eAAA,EAAiB;AAAA,MACf,WAAA,EAAa,CAAC,CAAA;AAAA,MACd,UAAA,EAAY,CAAC;AAAA,IACf,CAAA;AAAA,IACA,YAAA,EAAc;AAAA,MACZ,WAAA,EAAa,CAAC,CAAA;AAAA,MACd,UAAA,EAAY,CAAC;AAAA,IACf;AAAA,EACF,CAAA;AAEA,EAAA,+BAAA,EAAY,EAAI,CAACA,QAAAA,EAAAA,GAAY;AAnD/B,IAAA,IAAA,EAAA;AAoDI,IAAA,OAAA,CAAA,CAAQ,GAAA,EAAAA,QAAAA,CAAQ,QAAA,EAAA,GAAR,KAAA,EAAA,KAAA,EAAA,EAAA,EAAA,CAAkB,IAAA,EAAM;AAAA,MAC9B,KAAK,OAAA;AACH,QAAA,MAAA,CAAO,UAAA,CAAW,WAAA,CAAY,IAAA,CAAKA,QAAAA,CAAQ,QAAA,CAAS,WAAW,CAAA;AAC/D,QAAA,MAAA,CAAO,UAAA,CAAW,UAAA,CAAW,IAAA,CAAKA,QAAAA,CAAQ,UAAU,CAAA;AACpD,QAAA,KAAA;AAAA,MACF,KAAK,YAAA;AACH,QAAA,MAAA,CAAO,UAAA,CAAW,WAAA,CAAY,IAAA,CAAK,GAAGA,QAAAA,CAAQ,QAAA,CAAS,WAAW,CAAA;AAClE,QAAA,MAAA,CAAO,UAAA,CAAW,UAAA,CAAW,IAAA,CAAKA,QAAAA,CAAQ,UAAU,CAAA;AACpD,QAAA,KAAA;AAAA,MACF,KAAK,YAAA;AACH,QAAA,MAAA,CAAO,eAAA,CAAgB,WAAA,CAAY,IAAA,CAAKA,QAAAA,CAAQ,QAAA,CAAS,WAAW,CAAA;AACpE,QAAA,MAAA,CAAO,eAAA,CAAgB,UAAA,CAAW,IAAA,CAAKA,QAAAA,CAAQ,UAAU,CAAA;AACzD,QAAA,KAAA;AAAA,MACF,KAAK,iBAAA;AACH,QAAA,MAAA,CAAO,eAAA,CAAgB,WAAA,CAAY,IAAA;AAAA,UACjC,GAAGA,QAAAA,CAAQ,QAAA,CAAS;AAAA,QACtB,CAAA;AACA,QAAA,MAAA,CAAO,eAAA,CAAgB,UAAA,CAAW,IAAA,CAAKA,QAAAA,CAAQ,UAAU,CAAA;AACzD,QAAA,KAAA;AAAA,MACF,KAAK,SAAA;AACH,QAAA,MAAA,CAAO,YAAA,CAAa,WAAA,CAAY,IAAA,CAAKA,QAAAA,CAAQ,QAAA,CAAS,WAAW,CAAA;AACjE,QAAA,MAAA,CAAO,YAAA,CAAa,UAAA,CAAW,IAAA,CAAKA,QAAAA,CAAQ,UAAU,CAAA;AACtD,QAAA,KAAA;AAAA,MACF,KAAK,cAAA;AACH,QAAA,MAAA,CAAO,YAAA,CAAa,WAAA,CAAY,IAAA,CAAK,GAAGA,QAAAA,CAAQ,QAAA,CAAS,WAAW,CAAA;AACpE,QAAA,MAAA,CAAO,YAAA,CAAa,UAAA,CAAW,IAAA,CAAKA,QAAAA,CAAQ,UAAU,CAAA;AACtD,QAAA,KAAA;AAAA,MACF,OAAA;AACE,QAAA,KAAA;AAAA,IACJ;AAAA,EACF,CAAC,CAAA;AAED,EAAA,OAAO,wCAAA;AAAA,IACJ,MAAA,CAAO,IAAA,CAAK,MAAM,CAAA,CAChB,MAAA,CAAO,QAAA,CAAU,GAAA,EAAK;AACrB,MAAA,OAAO,MAAA,CAAO,GAAG,CAAA,CAAE,WAAA,CAAY,MAAA;AAAA,IACjC,CAAC,CAAA,CACA,IAAA,CAAK,CAAA,CACL,GAAA,CAAI,QAAA,CAAU,GAAA,EAAK;AAClB,MAAA,IAAI,SAAA,EAAW,EAAE,IAAA,EAAM,GAAA,EAAK,WAAA,EAAa,MAAA,CAAO,GAAG,CAAA,CAAE,YAAY,CAAA;AAIjE,MAAA,IAAI,WAAA,EAAa,EAAE,mBAAA,EAAqB,MAAA,CAAO,GAAG,CAAA,CAAE,WAAW,CAAA;AAC/D,MAAA,OAAO,8BAAA,QAAQ,EAAU,UAAU,CAAA;AAAA,IACrC,CAAC;AAAA,EACL,CAAA;AACF;AAGA,IAAO,qBAAA,EAAQ,OAAA;ADxCf;AACE;AACA;AACF,kEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-combine/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  GeoJsonProperties,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { feature, featureCollection } from \"@turf/helpers\";\nimport { featureEach } from \"@turf/meta\";\n\n/**\n * Combines a {@link FeatureCollection} of {@link Point}, {@link LineString}, or {@link Polygon} features\n * into {@link MultiPoint}, {@link MultiLineString}, or {@link MultiPolygon} features.\n *\n * @function\n * @param {FeatureCollection<Point|LineString|Polygon>} fc a FeatureCollection of any type\n * @returns {FeatureCollection<MultiPoint|MultiLineString|MultiPolygon>} a FeatureCollection of corresponding type to input\n * @example\n * var fc = turf.featureCollection([\n *   turf.point([19.026432, 47.49134]),\n *   turf.point([19.074497, 47.509548])\n * ]);\n *\n * var combined = turf.combine(fc);\n *\n * //addToMap\n * var addToMap = [combined]\n */\nfunction combine(\n  fc: FeatureCollection<\n    Point | MultiPoint | LineString | MultiLineString | Polygon | MultiPolygon\n  >\n) {\n  var groups = {\n    MultiPoint: {\n      coordinates: [] as number[][],\n      properties: [] as GeoJsonProperties[],\n    },\n    MultiLineString: {\n      coordinates: [] as number[][][],\n      properties: [] as GeoJsonProperties[],\n    },\n    MultiPolygon: {\n      coordinates: [] as number[][][][],\n      properties: [] as GeoJsonProperties[],\n    },\n  };\n\n  featureEach(fc, (feature) => {\n    switch (feature.geometry?.type) {\n      case \"Point\":\n        groups.MultiPoint.coordinates.push(feature.geometry.coordinates);\n        groups.MultiPoint.properties.push(feature.properties);\n        break;\n      case \"MultiPoint\":\n        groups.MultiPoint.coordinates.push(...feature.geometry.coordinates);\n        groups.MultiPoint.properties.push(feature.properties);\n        break;\n      case \"LineString\":\n        groups.MultiLineString.coordinates.push(feature.geometry.coordinates);\n        groups.MultiLineString.properties.push(feature.properties);\n        break;\n      case \"MultiLineString\":\n        groups.MultiLineString.coordinates.push(\n          ...feature.geometry.coordinates\n        );\n        groups.MultiLineString.properties.push(feature.properties);\n        break;\n      case \"Polygon\":\n        groups.MultiPolygon.coordinates.push(feature.geometry.coordinates);\n        groups.MultiPolygon.properties.push(feature.properties);\n        break;\n      case \"MultiPolygon\":\n        groups.MultiPolygon.coordinates.push(...feature.geometry.coordinates);\n        groups.MultiPolygon.properties.push(feature.properties);\n        break;\n      default:\n        break;\n    }\n  });\n\n  return featureCollection(\n    (Object.keys(groups) as (keyof typeof groups)[])\n      .filter(function (key) {\n        return groups[key].coordinates.length;\n      })\n      .sort()\n      .map(function (key) {\n        var geometry = { type: key, coordinates: groups[key].coordinates } as\n          | MultiPoint\n          | MultiLineString\n          | MultiPolygon;\n        var properties = { collectedProperties: groups[key].properties };\n        return feature(geometry, properties);\n      })\n  );\n}\n\nexport { combine };\nexport default combine;\n"]}