{"version": 3, "sources": ["../../index.ts", "../../lib/grid-to-matrix.js"], "sourcesContent": ["import { bbox } from \"@turf/bbox\";\nimport { area } from \"@turf/area\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { explode } from \"@turf/explode\";\nimport { collectionOf } from \"@turf/invariant\";\nimport {\n  polygon,\n  multiPolygon,\n  featureCollection,\n  isObject,\n} from \"@turf/helpers\";\n\nimport {\n  FeatureCollection,\n  Point,\n  GeoJsonProperties,\n  MultiPolygon,\n  Position,\n  Polygon,\n  Feature,\n} from \"geojson\";\n\nimport { gridToMatrix } from \"./lib/grid-to-matrix.js\";\nimport { isoBands } from \"marchingsquares\";\n\ntype GroupRingProps = { [prop: string]: string };\ntype GroupedRings =\n  | {\n      groupedRings: Position[][][];\n    }\n  | GroupRingProps;\n\n/**\n * Takes a square or rectangular grid {@link FeatureCollection} of {@link Point} features with z-values and an array of\n * value breaks and generates filled contour isobands.\n *\n * @function\n * @param {FeatureCollection<Point>} pointGrid input points - must be square or rectangular\n * @param {Array<number>} breaks where to draw contours\n * @param {Object} [options={}] options on output\n * @param {string} [options.zProperty='elevation'] the property name in `points` from which z-values will be pulled\n * @param {Object} [options.commonProperties={}] GeoJSON properties passed to ALL isobands\n * @param {Array<Object>} [options.breaksProperties=[]] GeoJSON properties passed, in order, to the correspondent isoband (order defined by breaks)\n * @returns {FeatureCollection<MultiPolygon>} a FeatureCollection of {@link MultiPolygon} features representing isobands\n */\nfunction isobands(\n  pointGrid: FeatureCollection<Point>,\n  breaks: number[],\n  options?: {\n    zProperty?: string;\n    commonProperties?: GeoJsonProperties;\n    breaksProperties?: GeoJsonProperties[];\n  }\n): FeatureCollection<MultiPolygon> {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const zProperty = options.zProperty || \"elevation\";\n  const commonProperties = options.commonProperties || {};\n  const breaksProperties = options.breaksProperties || [];\n\n  // Validation\n  collectionOf(pointGrid, \"Point\", \"Input must contain Points\");\n  if (!breaks) throw new Error(\"breaks is required\");\n  if (!Array.isArray(breaks)) throw new Error(\"breaks is not an Array\");\n  if (!isObject(commonProperties))\n    throw new Error(\"commonProperties is not an Object\");\n  if (!Array.isArray(breaksProperties))\n    throw new Error(\"breaksProperties is not an Array\");\n\n  // Isoband methods\n  const matrix = gridToMatrix(pointGrid, { zProperty: zProperty, flip: true });\n  let contours = createContourLines(matrix, breaks, zProperty);\n  contours = rescaleContours(contours, matrix, pointGrid);\n\n  const multipolygons = contours.map((contour, index) => {\n    if (breaksProperties[index] && !isObject(breaksProperties[index])) {\n      throw new Error(\"Each mappedProperty is required to be an Object\");\n    }\n    // collect all properties\n    const contourProperties = {\n      ...commonProperties,\n      ...breaksProperties[index],\n    };\n\n    contourProperties[zProperty] = (contour as GroupRingProps)[zProperty];\n\n    const multiP = multiPolygon(\n      contour.groupedRings as Position[][][],\n      contourProperties\n    );\n    return multiP;\n  });\n\n  return featureCollection(multipolygons);\n}\n\n/**\n * Creates the contours lines (featuresCollection of polygon features) from the 2D data grid\n *\n * Marchingsquares process the grid data as a 3D representation of a function on a 2D plane, therefore it\n * assumes the points (x-y coordinates) are one 'unit' distance. The result of the IsoBands function needs to be\n * rescaled, with turfjs, to the original area and proportions on the map\n *\n * @private\n * @param {Array<Array<number>>} matrix Grid Data\n * @param {Array<number>} breaks Breaks\n * @param {string} [property='elevation'] Property\n * @returns {Array<any>} contours\n */\nfunction createContourLines(\n  matrix: number[][],\n  breaks: number[],\n  property: string\n): GroupedRings[] {\n  const contours: GroupedRings[] = [];\n  for (let i = 1; i < breaks.length; i++) {\n    const lowerBand = +breaks[i - 1]; // make sure the breaks value is a number\n    const upperBand = +breaks[i];\n\n    const isobandsCoords = isoBands(matrix, lowerBand, upperBand - lowerBand);\n    // as per GeoJson rules for creating a Polygon, make sure the first element\n    // in the array of LinearRings represents the exterior ring (i.e. biggest area),\n    // and any subsequent elements represent interior rings (i.e. smaller area);\n    // this avoids rendering issues of the MultiPolygons on the map\n    const nestedRings = orderByArea(isobandsCoords);\n    const groupedRings = groupNestedRings(nestedRings);\n\n    contours.push({\n      groupedRings: groupedRings as Position[][][],\n      [property]: lowerBand + \"-\" + upperBand,\n    });\n  }\n  return contours;\n}\n\n/**\n * Transform isobands of 2D grid to polygons for the map\n *\n * @private\n * @param {Array<any>} contours Contours\n * @param {Array<Array<number>>} matrix Grid Data\n * @param {Object} points Points by Latitude\n * @returns {Array<any>} contours\n */\nfunction rescaleContours(\n  contours: GroupedRings[],\n  matrix: number[][],\n  points: FeatureCollection<Point>\n): GroupedRings[] {\n  // get dimensions (on the map) of the original grid\n  const gridBbox = bbox(points); // [ minX, minY, maxX, maxY ]\n  const originalWidth = gridBbox[2] - gridBbox[0];\n  const originalHeigth = gridBbox[3] - gridBbox[1];\n\n  // get origin, which is the first point of the last row on the rectangular data on the map\n  const x0 = gridBbox[0];\n  const y0 = gridBbox[1];\n  // get number of cells per side\n  const matrixWidth = matrix[0].length - 1;\n  const matrixHeight = matrix.length - 1;\n  // calculate the scaling factor between matrix and rectangular grid on the map\n  const scaleX = originalWidth / matrixWidth;\n  const scaleY = originalHeigth / matrixHeight;\n\n  // resize and shift each point/line of the isobands\n  return contours.map(function (contour) {\n    contour.groupedRings = (contour.groupedRings as Position[][][]).map(\n      function (lineRingSet) {\n        return lineRingSet.map(function (lineRing) {\n          return lineRing.map((point: Position) => [\n            point[0] * scaleX + x0,\n            point[1] * scaleY + y0,\n          ]);\n        });\n      }\n    );\n\n    return contour;\n  });\n}\n\n/*  utility functions */\n\n/**\n * Returns an array of coordinates (of LinearRings) in descending order by area\n *\n * @private\n * @param {Array<LineString>} ringsCoords array of closed LineString\n * @returns {Array} array of the input LineString ordered by area\n */\nfunction orderByArea(ringsCoords: Position[][]): Position[][] {\n  const ringsWithArea = ringsCoords.map(function (coords) {\n    // associate each lineRing with its area\n    return { ring: coords, area: area(polygon([coords])) };\n  });\n  ringsWithArea.sort(function (a, b) {\n    // bigger --> smaller\n    return b.area - a.area;\n  });\n  // create a new array of linearRings coordinates ordered by their area\n  return ringsWithArea.map(function (x) {\n    return x.ring;\n  });\n}\n\n/**\n * Returns an array of arrays of coordinates, each representing\n * a set of (coordinates of) nested LinearRings,\n * i.e. the first ring contains all the others\n *\n * @private\n * @param {Array} orderedLinearRings array of coordinates (of LinearRings) in descending order by area\n * @returns {Array<Array>} Array of coordinates of nested LinearRings\n */\nfunction groupNestedRings(orderedLinearRings: Position[][]): Position[][][] {\n  // create a list of the (coordinates of) LinearRings\n  const lrList = orderedLinearRings.map((lr) => {\n    return { lrCoordinates: lr, grouped: false };\n  });\n  const groupedLinearRingsCoords: Position[][][] = [];\n\n  while (!allGrouped(lrList)) {\n    for (let i = 0; i < lrList.length; i++) {\n      if (!lrList[i].grouped) {\n        // create new group starting with the larger not already grouped ring\n        const group: Position[][] = [];\n        group.push(lrList[i].lrCoordinates);\n        lrList[i].grouped = true;\n        const outerMostPoly = polygon([lrList[i].lrCoordinates]);\n        // group all the rings contained by the outermost ring\n        for (let j = i + 1; j < lrList.length; j++) {\n          if (!lrList[j].grouped) {\n            const lrPoly = polygon([lrList[j].lrCoordinates]);\n            if (isInside(lrPoly, outerMostPoly)) {\n              group.push(lrList[j].lrCoordinates);\n              lrList[j].grouped = true;\n            }\n          }\n        }\n        // insert the new group\n        groupedLinearRingsCoords.push(group);\n      }\n    }\n  }\n  return groupedLinearRingsCoords;\n}\n\n/**\n * @private\n * @param {Polygon} testPolygon polygon of interest\n * @param {Polygon} targetPolygon polygon you want to compare with\n * @returns {boolean} true if test-Polygon is inside target-Polygon\n */\nfunction isInside(\n  testPolygon: Feature<Polygon>,\n  targetPolygon: Feature<Polygon>\n): boolean {\n  const points = explode(testPolygon);\n  for (let i = 0; i < points.features.length; i++) {\n    if (!booleanPointInPolygon(points.features[i], targetPolygon)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * @private\n * @param {Array<Object>} list list of objects which might contain the 'group' attribute\n * @returns {boolean} true if all the objects in the list are marked as grouped\n */\nfunction allGrouped(\n  list: { grouped: boolean; lrCoordinates: Position[] }[]\n): boolean {\n  for (let i = 0; i < list.length; i++) {\n    if (list[i].grouped === false) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport { isobands };\nexport default isobands;\n", "import { getCoords, collectionOf } from \"@turf/invariant\";\nimport { featureEach } from \"@turf/meta\";\nimport { isObject } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Point} grid and returns a correspondent matrix {Array<Array<number>>}\n * of the 'property' values\n *\n * @name gridToMatrix\n * @param {FeatureCollection<Point>} grid of points\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.zProperty='elevation'] the property name in `points` from which z-values will be pulled\n * @param {boolean} [options.flip=false] returns the matrix upside-down\n * @param {boolean} [options.flags=false] flags, adding a `matrixPosition` array field ([row, column]) to its properties,\n * the grid points with coordinates on the matrix\n * @returns {Array<Array<number>>} matrix of property values\n * @example\n *   var extent = [-70.823364, -33.553984, -70.473175, -33.302986];\n *   var cellSize = 3;\n *   var grid = turf.pointGrid(extent, cellSize);\n *   // add a random property to each point between 0 and 60\n *   for (var i = 0; i < grid.features.length; i++) {\n *     grid.features[i].properties.elevation = (Math.random() * 60);\n *   }\n *   gridToMatrix(grid);\n *   //= [\n *     [ 1, 13, 10,  9, 10, 13, 18],\n *     [34,  8,  5,  4,  5,  8, 13],\n *     [10,  5,  2,  1,  2,  5,  4],\n *     [ 0,  4, 56, 19,  1,  4,  9],\n *     [10,  5,  2,  1,  2,  5, 10],\n *     [57,  8,  5,  4,  5,  0, 57],\n *     [ 3, 13, 10,  9,  5, 13, 18],\n *     [18, 13, 10,  9, 78, 13, 18]\n *   ]\n */\nfunction gridToMatrix(grid, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var zProperty = options.zProperty || \"elevation\";\n  var flip = options.flip;\n  var flags = options.flags;\n\n  // validation\n  collectionOf(grid, \"Point\", \"input must contain Points\");\n\n  var pointsMatrix = sortPointsByLatLng(grid, flip);\n\n  var matrix = [];\n  // create property matrix from sorted points\n  // looping order matters here\n  for (var r = 0; r < pointsMatrix.length; r++) {\n    var pointRow = pointsMatrix[r];\n    var row = [];\n    for (var c = 0; c < pointRow.length; c++) {\n      var point = pointRow[c];\n      // Check if zProperty exist\n      if (point.properties[zProperty]) row.push(point.properties[zProperty]);\n      else row.push(0);\n      // add flags\n      if (flags === true) point.properties.matrixPosition = [r, c];\n    }\n    matrix.push(row);\n  }\n\n  return matrix;\n}\n\n/**\n * Sorts points by latitude and longitude, creating a 2-dimensional array of points\n *\n * @private\n * @param {FeatureCollection<Point>} points GeoJSON Point features\n * @param {boolean} [flip=false] returns the matrix upside-down\n * @returns {Array<Array<Point>>} points ordered by latitude and longitude\n */\nfunction sortPointsByLatLng(points, flip) {\n  var pointsByLatitude = {};\n\n  // divide points by rows with the same latitude\n  featureEach(points, function (point) {\n    var lat = getCoords(point)[1];\n    if (!pointsByLatitude[lat]) pointsByLatitude[lat] = [];\n    pointsByLatitude[lat].push(point);\n  });\n\n  // sort points (with the same latitude) by longitude\n  var orderedRowsByLatitude = Object.keys(pointsByLatitude).map(function (lat) {\n    var row = pointsByLatitude[lat];\n    var rowOrderedByLongitude = row.sort(function (a, b) {\n      return getCoords(a)[0] - getCoords(b)[0];\n    });\n    return rowOrderedByLongitude;\n  });\n\n  // sort rows (of points with the same latitude) by latitude\n  var pointMatrix = orderedRowsByLatitude.sort(function (a, b) {\n    if (flip) return getCoords(a[0])[1] - getCoords(b[0])[1];\n    else return getCoords(b[0])[1] - getCoords(a[0])[1];\n  });\n\n  return pointMatrix;\n}\n\nexport { gridToMatrix };\nexport default gridToMatrix;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,SAAS,YAAY;AACrB,SAAS,YAAY;AACrB,SAAS,6BAA6B;AACtC,SAAS,eAAe;AACxB,SAAS,gBAAAA,qBAAoB;AAC7B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC;AAAA,OACK;;;ACVP,SAAS,WAAW,oBAAoB;AACxC,SAAS,mBAAmB;AAC5B,SAAS,gBAAgB;AAkCzB,SAAS,aAAa,MAAM,SAAS;AAEnC,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,MAAI,YAAY,QAAQ,aAAa;AACrC,MAAI,OAAO,QAAQ;AACnB,MAAI,QAAQ,QAAQ;AAGpB,eAAa,MAAM,SAAS,2BAA2B;AAEvD,MAAI,eAAe,mBAAmB,MAAM,IAAI;AAEhD,MAAI,SAAS,CAAC;AAGd,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,QAAI,WAAW,aAAa,CAAC;AAC7B,QAAI,MAAM,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAI,QAAQ,SAAS,CAAC;AAEtB,UAAI,MAAM,WAAW,SAAS,EAAG,KAAI,KAAK,MAAM,WAAW,SAAS,CAAC;AAAA,UAChE,KAAI,KAAK,CAAC;AAEf,UAAI,UAAU,KAAM,OAAM,WAAW,iBAAiB,CAAC,GAAG,CAAC;AAAA,IAC7D;AACA,WAAO,KAAK,GAAG;AAAA,EACjB;AAEA,SAAO;AACT;AAUA,SAAS,mBAAmB,QAAQ,MAAM;AACxC,MAAI,mBAAmB,CAAC;AAGxB,cAAY,QAAQ,SAAU,OAAO;AACnC,QAAI,MAAM,UAAU,KAAK,EAAE,CAAC;AAC5B,QAAI,CAAC,iBAAiB,GAAG,EAAG,kBAAiB,GAAG,IAAI,CAAC;AACrD,qBAAiB,GAAG,EAAE,KAAK,KAAK;AAAA,EAClC,CAAC;AAGD,MAAI,wBAAwB,OAAO,KAAK,gBAAgB,EAAE,IAAI,SAAU,KAAK;AAC3E,QAAI,MAAM,iBAAiB,GAAG;AAC9B,QAAI,wBAAwB,IAAI,KAAK,SAAU,GAAG,GAAG;AACnD,aAAO,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC;AAAA,IACzC,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AAGD,MAAI,cAAc,sBAAsB,KAAK,SAAU,GAAG,GAAG;AAC3D,QAAI,KAAM,QAAO,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;AAAA,QAClD,QAAO,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;AAAA,EACpD,CAAC;AAED,SAAO;AACT;;;ADhFA,SAAS,gBAAgB;AAsBzB,SAAS,SACP,WACA,QACA,SAKiC;AAEjC,YAAU,WAAW,CAAC;AACtB,MAAI,CAACC,UAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,QAAM,YAAY,QAAQ,aAAa;AACvC,QAAM,mBAAmB,QAAQ,oBAAoB,CAAC;AACtD,QAAM,mBAAmB,QAAQ,oBAAoB,CAAC;AAGtD,EAAAC,cAAa,WAAW,SAAS,2BAA2B;AAC5D,MAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,oBAAoB;AACjD,MAAI,CAAC,MAAM,QAAQ,MAAM,EAAG,OAAM,IAAI,MAAM,wBAAwB;AACpE,MAAI,CAACD,UAAS,gBAAgB;AAC5B,UAAM,IAAI,MAAM,mCAAmC;AACrD,MAAI,CAAC,MAAM,QAAQ,gBAAgB;AACjC,UAAM,IAAI,MAAM,kCAAkC;AAGpD,QAAM,SAAS,aAAa,WAAW,EAAE,WAAsB,MAAM,KAAK,CAAC;AAC3E,MAAI,WAAW,mBAAmB,QAAQ,QAAQ,SAAS;AAC3D,aAAW,gBAAgB,UAAU,QAAQ,SAAS;AAEtD,QAAM,gBAAgB,SAAS,IAAI,CAAC,SAAS,UAAU;AACrD,QAAI,iBAAiB,KAAK,KAAK,CAACA,UAAS,iBAAiB,KAAK,CAAC,GAAG;AACjE,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AAEA,UAAM,oBAAoB,kCACrB,mBACA,iBAAiB,KAAK;AAG3B,sBAAkB,SAAS,IAAK,QAA2B,SAAS;AAEpE,UAAM,SAAS;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AAED,SAAO,kBAAkB,aAAa;AACxC;AAeA,SAAS,mBACP,QACA,QACA,UACgB;AAChB,QAAM,WAA2B,CAAC;AAClC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,YAAY,CAAC,OAAO,IAAI,CAAC;AAC/B,UAAM,YAAY,CAAC,OAAO,CAAC;AAE3B,UAAM,iBAAiB,SAAS,QAAQ,WAAW,YAAY,SAAS;AAKxE,UAAM,cAAc,YAAY,cAAc;AAC9C,UAAM,eAAe,iBAAiB,WAAW;AAEjD,aAAS,KAAK;AAAA,MACZ;AAAA,MACA,CAAC,QAAQ,GAAG,YAAY,MAAM;AAAA,IAChC,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAWA,SAAS,gBACP,UACA,QACA,QACgB;AAEhB,QAAM,WAAW,KAAK,MAAM;AAC5B,QAAM,gBAAgB,SAAS,CAAC,IAAI,SAAS,CAAC;AAC9C,QAAM,iBAAiB,SAAS,CAAC,IAAI,SAAS,CAAC;AAG/C,QAAM,KAAK,SAAS,CAAC;AACrB,QAAM,KAAK,SAAS,CAAC;AAErB,QAAM,cAAc,OAAO,CAAC,EAAE,SAAS;AACvC,QAAM,eAAe,OAAO,SAAS;AAErC,QAAM,SAAS,gBAAgB;AAC/B,QAAM,SAAS,iBAAiB;AAGhC,SAAO,SAAS,IAAI,SAAU,SAAS;AACrC,YAAQ,eAAgB,QAAQ,aAAgC;AAAA,MAC9D,SAAU,aAAa;AACrB,eAAO,YAAY,IAAI,SAAU,UAAU;AACzC,iBAAO,SAAS,IAAI,CAAC,UAAoB;AAAA,YACvC,MAAM,CAAC,IAAI,SAAS;AAAA,YACpB,MAAM,CAAC,IAAI,SAAS;AAAA,UACtB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AAWA,SAAS,YAAY,aAAyC;AAC5D,QAAM,gBAAgB,YAAY,IAAI,SAAU,QAAQ;AAEtD,WAAO,EAAE,MAAM,QAAQ,MAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;AAAA,EACvD,CAAC;AACD,gBAAc,KAAK,SAAU,GAAG,GAAG;AAEjC,WAAO,EAAE,OAAO,EAAE;AAAA,EACpB,CAAC;AAED,SAAO,cAAc,IAAI,SAAU,GAAG;AACpC,WAAO,EAAE;AAAA,EACX,CAAC;AACH;AAWA,SAAS,iBAAiB,oBAAkD;AAE1E,QAAM,SAAS,mBAAmB,IAAI,CAAC,OAAO;AAC5C,WAAO,EAAE,eAAe,IAAI,SAAS,MAAM;AAAA,EAC7C,CAAC;AACD,QAAM,2BAA2C,CAAC;AAElD,SAAO,CAAC,WAAW,MAAM,GAAG;AAC1B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,CAAC,OAAO,CAAC,EAAE,SAAS;AAEtB,cAAM,QAAsB,CAAC;AAC7B,cAAM,KAAK,OAAO,CAAC,EAAE,aAAa;AAClC,eAAO,CAAC,EAAE,UAAU;AACpB,cAAM,gBAAgB,QAAQ,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC;AAEvD,iBAAS,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC1C,cAAI,CAAC,OAAO,CAAC,EAAE,SAAS;AACtB,kBAAM,SAAS,QAAQ,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC;AAChD,gBAAI,SAAS,QAAQ,aAAa,GAAG;AACnC,oBAAM,KAAK,OAAO,CAAC,EAAE,aAAa;AAClC,qBAAO,CAAC,EAAE,UAAU;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAEA,iCAAyB,KAAK,KAAK;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQA,SAAS,SACP,aACA,eACS;AACT,QAAM,SAAS,QAAQ,WAAW;AAClC,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAAK;AAC/C,QAAI,CAAC,sBAAsB,OAAO,SAAS,CAAC,GAAG,aAAa,GAAG;AAC7D,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,WACP,MACS;AACT,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,KAAK,CAAC,EAAE,YAAY,OAAO;AAC7B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAO,wBAAQ;", "names": ["collectionOf", "isObject", "isObject", "collectionOf"]}