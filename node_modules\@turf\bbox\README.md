# @turf/bbox

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## bbox

Calculates the bounding box for any GeoJSON object, including FeatureCollection.
Uses geojson.bbox if available and options.recompute is not set.

### Parameters

*   `geojson` **[GeoJSON][1]** any GeoJSON object
*   `options` **[Object][2]** Optional parameters (optional, default `{}`)

    *   `options.recompute` **[boolean][3]?** Whether to ignore an existing bbox property on geojson

### Examples

```javascript
var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]]);
var bbox = turf.bbox(line);
var bboxPolygon = turf.bboxPolygon(bbox);

//addToMap
var addToMap = [line, bboxPolygon]
```

Returns **[BBox][4]** bbox extent in \[minX, minY, maxX, maxY] order

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[4]: https://tools.ietf.org/html/rfc7946#section-5

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/bbox
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
