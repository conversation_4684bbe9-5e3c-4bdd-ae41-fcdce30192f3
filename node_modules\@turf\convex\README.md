# @turf/convex

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## convex

Takes a [Feature][1] or a [FeatureCollection][2] and returns a convex hull [Polygon][3].

Internally this uses
the [convex-hull][4] module that implements a
[monotone chain hull][5].

### Parameters

*   `geo<PERSON>son` **[GeoJSON][6]** input Feature or FeatureCollection
*   `options` **[Object][7]** Optional parameters (optional, default `{}`)

    *   `options.concavity` **[number][8]** 1 - thin shape. Infinity - convex hull. (optional, default `Infinity`)
    *   `options.properties` **[Object][7]** Translate Properties to Feature (optional, default `{}`)

### Examples

```javascript
var points = turf.featureCollection([
  turf.point([10.195312, 43.755225]),
  turf.point([10.404052, 43.8424511]),
  turf.point([10.579833, 43.659924]),
  turf.point([10.360107, 43.516688]),
  turf.point([10.14038, 43.588348]),
  turf.point([10.195312, 43.755225])
]);

var hull = turf.convex(points);

//addToMap
var addToMap = [points, hull]
```

Returns **[Feature][1]<[Polygon][3]>** a convex hull

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[4]: https://github.com/mikolalysenko/convex-hull

[5]: http://en.wikibooks.org/wiki/Algorithm_Implementation/Geometry/Convex_hull/Monotone_chain

[6]: https://tools.ietf.org/html/rfc7946#section-3

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/convex
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
