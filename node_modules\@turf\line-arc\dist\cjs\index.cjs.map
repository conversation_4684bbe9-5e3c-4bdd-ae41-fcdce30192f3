{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-arc/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,sCAAuB;AACvB,gDAA4B;AAC5B,wCAAyC;AA0BzC,SAAS,OAAA,CACP,MAAA,EACA,MAAA,EACA,QAAA,EACA,QAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACgB;AAErB,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,EAAA;AAE/B,EAAA,MAAM,OAAA,EAAS,iBAAA,CAAkB,QAAQ,CAAA;AACzC,EAAA,MAAM,OAAA,EAAS,iBAAA,CAAkB,QAAQ,CAAA;AACzC,EAAA,MAAM,WAAA,EACJ,CAAC,KAAA,CAAM,OAAA,CAAQ,MAAM,EAAA,GAAK,MAAA,CAAO,KAAA,IAAS,UAAA,EACtC,MAAA,CAAO,WAAA,EACP,CAAC,CAAA;AAGP,EAAA,GAAA,CAAI,OAAA,IAAW,MAAA,EAAQ;AACrB,IAAA,OAAO,iCAAA;AAAA,MACL,4BAAA,MAAO,EAAQ,MAAA,EAAQ,OAAO,CAAA,CAAE,QAAA,CAAS,WAAA,CAAY,CAAC,CAAA;AAAA,MACtD;AAAA,IACF,CAAA;AAAA,EACF;AACA,EAAA,MAAM,eAAA,EAAiB,MAAA;AACvB,EAAA,MAAM,aAAA,EAAe,OAAA,EAAS,OAAA,EAAS,OAAA,EAAS,OAAA,EAAS,GAAA;AAEzD,EAAA,IAAI,MAAA,EAAQ,cAAA;AACZ,EAAA,MAAM,YAAA,EAAc,CAAC,CAAA;AACrB,EAAA,IAAI,EAAA,EAAI,CAAA;AAER,EAAA,MAAM,QAAA,EAAA,CAAW,aAAA,EAAe,cAAA,EAAA,EAAkB,KAAA;AAGlD,EAAA,MAAA,CAAO,MAAA,GAAS,YAAA,EAAc;AAC5B,IAAA,WAAA,CAAY,IAAA;AAAA,MACV,sCAAA,MAAY,EAAQ,MAAA,EAAQ,KAAA,EAAO,OAAO,CAAA,CAAE,QAAA,CAAS;AAAA,IACvD,CAAA;AACA,IAAA,CAAA,EAAA;AACA,IAAA,MAAA,EAAQ,eAAA,EAAiB,EAAA,EAAI,OAAA;AAAA,EAC/B;AACA,EAAA,OAAO,iCAAA,WAAW,EAAa,UAAU,CAAA;AAC3C;AAUA,SAAS,iBAAA,CAAkB,KAAA,EAAe;AACxC,EAAA,IAAI,KAAA,EAAO,MAAA,EAAQ,GAAA;AACnB,EAAA,GAAA,CAAI,KAAA,EAAO,CAAA,EAAG;AACZ,IAAA,KAAA,GAAQ,GAAA;AAAA,EACV;AACA,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,sBAAA,EAAQ,OAAA;ADvDf;AACE;AACA;AACF,mEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-arc/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, LineString } from \"geojson\";\nimport { circle } from \"@turf/circle\";\nimport { destination } from \"@turf/destination\";\nimport { Coord, lineString, Units } from \"@turf/helpers\";\n\n/**\n * Creates a circular arc, of a circle of the given radius and center point, between bearing1 and bearing2;\n * 0 bearing is North of center point, positive clockwise.\n *\n * @function\n * @param {Coord} center center point\n * @param {number} radius radius of the circle\n * @param {number} bearing1 angle, in decimal degrees, of the first radius of the arc\n * @param {number} bearing2 angle, in decimal degrees, of the second radius of the arc\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.steps=64] number of steps (straight segments) that will constitute the arc\n * @param {string} [options.units='kilometers'] miles, kilometers, degrees, or radians\n * @returns {Feature<LineString>} line arc\n * @example\n * var center = turf.point([-75, 40]);\n * var radius = 5;\n * var bearing1 = 25;\n * var bearing2 = 47;\n *\n * var arc = turf.lineArc(center, radius, bearing1, bearing2);\n *\n * //addToMap\n * var addToMap = [center, arc]\n */\nfunction lineArc(\n  center: Coord,\n  radius: number,\n  bearing1: number,\n  bearing2: number,\n  options: {\n    steps?: number;\n    units?: Units;\n  } = {}\n): Feature<LineString> {\n  // default params\n  const steps = options.steps || 64;\n\n  const angle1 = convertAngleTo360(bearing1);\n  const angle2 = convertAngleTo360(bearing2);\n  const properties =\n    !Array.isArray(center) && center.type === \"Feature\"\n      ? center.properties\n      : {};\n\n  // handle angle parameters\n  if (angle1 === angle2) {\n    return lineString(\n      circle(center, radius, options).geometry.coordinates[0],\n      properties\n    );\n  }\n  const arcStartDegree = angle1;\n  const arcEndDegree = angle1 < angle2 ? angle2 : angle2 + 360;\n\n  let alpha = arcStartDegree;\n  const coordinates = [];\n  let i = 0;\n  // How many degrees we'll swing around between each step.\n  const arcStep = (arcEndDegree - arcStartDegree) / steps;\n  // Add coords to the list, increasing the angle from our start bearing\n  // (alpha) by arcStep degrees until we reach the end bearing.\n  while (alpha <= arcEndDegree) {\n    coordinates.push(\n      destination(center, radius, alpha, options).geometry.coordinates\n    );\n    i++;\n    alpha = arcStartDegree + i * arcStep;\n  }\n  return lineString(coordinates, properties);\n}\n\n/**\n * Takes any angle in  degrees\n * and returns a valid angle between 0-360 degrees\n *\n * @private\n * @param {number} alpha angle between -180-180 degrees\n * @returns {number} angle between 0-360 degrees\n */\nfunction convertAngleTo360(alpha: number) {\n  let beta = alpha % 360;\n  if (beta < 0) {\n    beta += 360;\n  }\n  return beta;\n}\n\nexport { lineArc };\nexport default lineArc;\n"]}