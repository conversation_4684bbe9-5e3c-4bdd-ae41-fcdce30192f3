# @turf/line-overlap

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineOver<PERSON>

Takes any LineString or Polygon and returns the overlapping lines between both features.

### Parameters

*   `line1` **([Geometry][1] | [Feature][2]<([LineString][3] | [MultiLineString][4] | [Polygon][5] | [MultiPolygon][6])>)** any LineString or Polygon
*   `line2` **([Geometry][1] | [Feature][2]<([LineString][3] | [MultiLineString][4] | [Polygon][5] | [MultiPolygon][6])>)** any LineString or Polygon
*   `options` **[Object][7]** Optional parameters (optional, default `{}`)

    *   `options.tolerance` **[number][8]** Tolerance distance to match overlapping line segments (in kilometers) (optional, default `0`)

### Examples

```javascript
var line1 = turf.lineString([[115, -35], [125, -30], [135, -30], [145, -35]]);
var line2 = turf.lineString([[115, -25], [125, -30], [135, -30], [145, -25]]);

var overlapping = turf.lineOverlap(line1, line2);

//addToMap
var addToMap = [line1, line2, overlapping]
```

Returns **[FeatureCollection][9]<[LineString][3]>** lines(s) that are overlapping between both features

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[9]: https://tools.ietf.org/html/rfc7946#section-3.3

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/line-overlap
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
