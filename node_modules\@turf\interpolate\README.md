# @turf/interpolate

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## interpolate

Takes a set of points and estimates their 'property' values on a grid using the [Inverse Distance Weighting (IDW) method][1].

### Parameters

*   `points` **[FeatureCollection][2]<[Point][3]>** with known value
*   `cellSize` **[number][4]** the distance across each grid point
*   `options` **[Object][5]** Optional parameters (optional, default `{}`)

    *   `options.gridType` **[string][6]** defines the output format based on a Grid Type (options: 'square' | 'point' | 'hex' | 'triangle') (optional, default `'square'`)
    *   `options.property` **[string][6]** the property name in `points` from which z-values will be pulled, zValue fallbacks to 3rd coordinate if no property exists. (optional, default `'elevation'`)
    *   `options.units` **[string][6]** used in calculating cellSize, can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)
    *   `options.weight` **[number][4]** exponent regulating the distance-decay weighting (optional, default `1`)
    *   `options.bbox` **[BBox][7]** Bounding Box Array \[west, south, east, north] associated with the FeatureCollection. (optional, default `bbox(points)`)

### Examples

```javascript
var points = turf.randomPoint(30, {bbox: [50, 30, 70, 50]});

// add a random property to each point
turf.featureEach(points, function(point) {
    point.properties.solRad = Math.random() * 50;
});
var options = {gridType: 'points', property: 'solRad', units: 'miles'};
var grid = turf.interpolate(points, 100, options);

//addToMap
var addToMap = [grid];
```

Returns **[FeatureCollection][2]<([Point][3] | [Polygon][8])>** grid of points or polygons with interpolated 'property'

[1]: https://en.wikipedia.org/wiki/Inverse_distance_weighting

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[7]: https://tools.ietf.org/html/rfc7946#section-5

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.6

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/interpolate
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
