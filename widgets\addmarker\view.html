﻿<!DOCTYPE html>
<html class="no-js css-menubar" lang="zh-cn">

<head>
    <title>弹窗子页面</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 移动设备 viewport -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui">
    <meta name="author" content="火星科技 http://cesium.marsgis.cn ">
    <!-- 360浏览器默认使用Webkit内核 -->
    <meta name="renderer" content="webkit">
    <!-- Chrome浏览器添加桌面快捷方式（安卓） -->
    <link rel="icon" type="image/png" href="../../img/favicon/favicon.png">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Safari浏览器添加到主屏幕（IOS） -->
    <link rel="icon" sizes="192x192" href="img/favicon/apple-touch-icon.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="火星科技MarsGIS">
    <!-- Win8标题栏及ICON图标 -->
    <link rel="apple-touch-icon-precomposed" href="../../img/favicon/apple-touch-icon.png">
    <meta name="msapplication-TileImage" content="../../img/favicon/<EMAIL>">
    <meta name="msapplication-TileColor" content="#62a8ea">

    <!-- 第3方lib引入 -->
    <script type="text/javascript" src="../../lib/include-lib.js?time=20200102" libpath="../../lib/"
            include="jquery,font-awesome,bootstrap,bootstrap-table,admui-frame"></script>

    <link href="../../css/widget-win.css" rel="stylesheet" />
    <link href="view.css?time=20200102" rel="stylesheet" />
</head>
<body>
    <input id="input_plot_file" type="file" accept=".json" style="display:none;" />
    <div class="mp_head">
        <ul>
            <li id="btn_marker_Add"><i class="fa fa-edit" title="添加注记"></i></li>
            <li class="ml10">|</li>
            <li id="btn_plot_openfile"><i class="fa fa-folder-open-o" title="打开文件"></i></li>
            <li id="btn_plot_openfile2"><i class="fa fa-folder-open" title="叠加文件"></i></li>
            <li id="btn_plot_savefile"><i class="fa fa-save" title="保存文件"></i></li>
            <li class="ml10">|</li>
            <li id="btn_plot_delall"><i class="fa fa-trash" title="清空标记"></i></li>
            <li id="btn_plot_isedit"><i class="fa fa-unlock" title="是否可编辑"></i></li>
        </ul>
    </div>
    <!--<div class="mp_head2">
        <div class="btn-group" role="group">
            <button id="btn_marker_Add" type="button" class="btn btn-primary ">
                <span class="fa fa-edit" aria-hidden="true"></span> 添加标记
            </button>
            <button id="btn_plot_savefile" type="button" class="btn btn-danger">
                <span class="fa fa-trash" aria-hidden="true"></span> 保存文件
            </button>
        </div>
    </div>-->
    <table id="table"></table>

    <!--页面js-->
    <script src="view.js?time=20200102"></script>
</body>
</html>
