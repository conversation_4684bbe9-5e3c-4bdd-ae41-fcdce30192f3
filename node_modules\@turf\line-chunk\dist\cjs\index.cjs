"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.js
var _length = require('@turf/length');
var _lineslicealong = require('@turf/line-slice-along');
var _meta = require('@turf/meta');
var _helpers = require('@turf/helpers');
function lineChunk(geojson, segmentLength, options) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  var units = options.units;
  var reverse = options.reverse;
  if (!geojson) throw new Error("geojson is required");
  if (segmentLength <= 0)
    throw new Error("segmentLength must be greater than 0");
  var results = [];
  _meta.flattenEach.call(void 0, geojson, function(feature) {
    if (reverse)
      feature.geometry.coordinates = feature.geometry.coordinates.reverse();
    sliceLineSegments(feature, segmentLength, units, function(segment) {
      results.push(segment);
    });
  });
  return _helpers.featureCollection.call(void 0, results);
}
function sliceLineSegments(line, segmentLength, units, callback) {
  var lineLength = _length.length.call(void 0, line, { units });
  if (lineLength <= segmentLength) return callback(line);
  var numberOfSegments = lineLength / segmentLength;
  if (!Number.isInteger(numberOfSegments)) {
    numberOfSegments = Math.floor(numberOfSegments) + 1;
  }
  for (var i = 0; i < numberOfSegments; i++) {
    var outline = _lineslicealong.lineSliceAlong.call(void 0, 
      line,
      segmentLength * i,
      segmentLength * (i + 1),
      { units }
    );
    callback(outline, i);
  }
}
var turf_line_chunk_default = lineChunk;



exports.default = turf_line_chunk_default; exports.lineChunk = lineChunk;
//# sourceMappingURL=index.cjs.map