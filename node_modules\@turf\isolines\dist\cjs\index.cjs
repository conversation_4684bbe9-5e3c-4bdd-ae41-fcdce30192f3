"use strict";Object.defineProperty(exports, "__esModule", {value: true});var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};

// index.ts
var _bbox = require('@turf/bbox');
var _meta = require('@turf/meta');
var _invariant = require('@turf/invariant');
var _helpers = require('@turf/helpers');
var _marchingsquares = require('marchingsquares');

// lib/grid-to-matrix.js



function gridToMatrix(grid, options) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  var zProperty = options.zProperty || "elevation";
  var flip = options.flip;
  var flags = options.flags;
  _invariant.collectionOf.call(void 0, grid, "Point", "input must contain Points");
  var pointsMatrix = sortPointsByLatLng(grid, flip);
  var matrix = [];
  for (var r = 0; r < pointsMatrix.length; r++) {
    var pointRow = pointsMatrix[r];
    var row = [];
    for (var c = 0; c < pointRow.length; c++) {
      var point = pointRow[c];
      if (point.properties[zProperty]) row.push(point.properties[zProperty]);
      else row.push(0);
      if (flags === true) point.properties.matrixPosition = [r, c];
    }
    matrix.push(row);
  }
  return matrix;
}
function sortPointsByLatLng(points, flip) {
  var pointsByLatitude = {};
  _meta.featureEach.call(void 0, points, function(point) {
    var lat = _invariant.getCoords.call(void 0, point)[1];
    if (!pointsByLatitude[lat]) pointsByLatitude[lat] = [];
    pointsByLatitude[lat].push(point);
  });
  var orderedRowsByLatitude = Object.keys(pointsByLatitude).map(function(lat) {
    var row = pointsByLatitude[lat];
    var rowOrderedByLongitude = row.sort(function(a, b) {
      return _invariant.getCoords.call(void 0, a)[0] - _invariant.getCoords.call(void 0, b)[0];
    });
    return rowOrderedByLongitude;
  });
  var pointMatrix = orderedRowsByLatitude.sort(function(a, b) {
    if (flip) return _invariant.getCoords.call(void 0, a[0])[1] - _invariant.getCoords.call(void 0, b[0])[1];
    else return _invariant.getCoords.call(void 0, b[0])[1] - _invariant.getCoords.call(void 0, a[0])[1];
  });
  return pointMatrix;
}

// index.ts
function isolines(pointGrid, breaks, options) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  const zProperty = options.zProperty || "elevation";
  const commonProperties = options.commonProperties || {};
  const breaksProperties = options.breaksProperties || [];
  _invariant.collectionOf.call(void 0, pointGrid, "Point", "Input must contain Points");
  if (!breaks) throw new Error("breaks is required");
  if (!Array.isArray(breaks)) throw new Error("breaks must be an Array");
  if (!_helpers.isObject.call(void 0, commonProperties))
    throw new Error("commonProperties must be an Object");
  if (!Array.isArray(breaksProperties))
    throw new Error("breaksProperties must be an Array");
  const matrix = gridToMatrix(pointGrid, { zProperty, flip: true });
  const createdIsoLines = createIsoLines(
    matrix,
    breaks,
    zProperty,
    commonProperties,
    breaksProperties
  );
  const scaledIsolines = rescaleIsolines(createdIsoLines, matrix, pointGrid);
  return _helpers.featureCollection.call(void 0, scaledIsolines);
}
function createIsoLines(matrix, breaks, zProperty, commonProperties, breaksProperties) {
  const results = [];
  for (let i = 0; i < breaks.length; i++) {
    const threshold = +breaks[i];
    const properties = __spreadValues(__spreadValues({}, commonProperties), breaksProperties[i]);
    properties[zProperty] = threshold;
    const isoline = _helpers.multiLineString.call(void 0, 
      _marchingsquares.isoContours.call(void 0, matrix, threshold, { linearRing: false, noFrame: true }),
      properties
    );
    results.push(isoline);
  }
  return results;
}
function rescaleIsolines(createdIsoLines, matrix, points) {
  const gridBbox = _bbox.bbox.call(void 0, points);
  const originalWidth = gridBbox[2] - gridBbox[0];
  const originalHeigth = gridBbox[3] - gridBbox[1];
  const x0 = gridBbox[0];
  const y0 = gridBbox[1];
  const matrixWidth = matrix[0].length - 1;
  const matrixHeight = matrix.length - 1;
  const scaleX = originalWidth / matrixWidth;
  const scaleY = originalHeigth / matrixHeight;
  const resize = (point) => {
    point[0] = point[0] * scaleX + x0;
    point[1] = point[1] * scaleY + y0;
  };
  createdIsoLines.forEach((isoline) => {
    _meta.coordEach.call(void 0, isoline, resize);
  });
  return createdIsoLines;
}
var turf_isolines_default = isolines;



exports.default = turf_isolines_default; exports.isolines = isolines;
//# sourceMappingURL=index.cjs.map