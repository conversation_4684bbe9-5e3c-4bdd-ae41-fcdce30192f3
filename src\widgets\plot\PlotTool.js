/**
 * 标绘工具 - 基于原生Cesium实现
 * 移植自widgets/plot功能
 */
class PlotTool {
    constructor(viewer) {
        this.viewer = viewer;
        this.handler = null;
        this.isActive = false;
        this.plotEntities = [];
        this.currentDrawing = null;
        
        this.initHandler();
    }

    initHandler() {
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    }

    // 激活标绘工具
    activate() {
        this.isActive = true;
        this.viewer.cesiumWidget.canvas.style.cursor = 'crosshair';
    }

    // 停用标绘工具
    deactivate() {
        this.isActive = false;
        this.stopDrawing();
        this.viewer.cesiumWidget.canvas.style.cursor = '';
    }

    // 停止当前绘制
    stopDrawing() {
        if (this.handler) {
            this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
            this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        }
        this.currentDrawing = null;
    }

    // 绘制点标记
    drawPoint(options = {}) {
        this.stopDrawing();
        this.activate();
        
        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                const entity = this.viewer.entities.add({
                    position: position,
                    point: {
                        pixelSize: options.pixelSize || 12,
                        color: Cesium.Color.fromCssColorString(options.color || '#ffff00'),
                        outlineColor: Cesium.Color.fromCssColorString(options.outlineColor || '#000000'),
                        outlineWidth: options.outlineWidth || 2,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    },
                    label: options.showLabel ? {
                        text: options.labelText || '标记点',
                        font: '14pt sans-serif',
                        fillColor: Cesium.Color.WHITE,
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                        pixelOffset: new Cesium.Cartesian2(0, -40)
                    } : undefined
                });
                
                this.plotEntities.push(entity);
                this.deactivate();
                
                if (options.callback) {
                    options.callback(entity);
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    // 绘制线
    drawPolyline(options = {}) {
        this.stopDrawing();
        this.activate();
        
        const points = [];
        const dynamicPositions = new Cesium.CallbackProperty(() => points, false);
        
        const polylineEntity = this.viewer.entities.add({
            polyline: {
                positions: dynamicPositions,
                width: options.width || 3,
                material: Cesium.Color.fromCssColorString(options.color || '#ffff00'),
                clampToGround: options.clampToGround || false
            }
        });

        this.currentDrawing = polylineEntity;
        this.plotEntities.push(polylineEntity);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                points.push(position);
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction((event) => {
            if (points.length > 0) {
                const position = this.viewer.camera.pickEllipsoid(event.endPosition, this.viewer.scene.globe.ellipsoid);
                if (position) {
                    if (points.length === 1) {
                        points.push(position);
                    } else {
                        points[points.length - 1] = position;
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        this.handler.setInputAction(() => {
            this.stopDrawing();
            this.deactivate();
            if (options.callback) {
                options.callback(polylineEntity);
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    // 绘制多边形
    drawPolygon(options = {}) {
        this.stopDrawing();
        this.activate();
        
        const points = [];
        const dynamicPositions = new Cesium.CallbackProperty(() => points, false);
        
        const polygonEntity = this.viewer.entities.add({
            polygon: {
                hierarchy: dynamicPositions,
                material: Cesium.Color.fromCssColorString(options.color || '#ffff00').withAlpha(options.alpha || 0.5),
                outline: options.outline !== false,
                outlineColor: Cesium.Color.fromCssColorString(options.outlineColor || '#ffff00'),
                height: options.height || 0,
                extrudedHeight: options.extrudedHeight || 0
            }
        });

        this.currentDrawing = polygonEntity;
        this.plotEntities.push(polygonEntity);

        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                points.push(position);
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction((event) => {
            if (points.length > 0) {
                const position = this.viewer.camera.pickEllipsoid(event.endPosition, this.viewer.scene.globe.ellipsoid);
                if (position) {
                    if (points.length === 1) {
                        points.push(position);
                    } else {
                        points[points.length - 1] = position;
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        this.handler.setInputAction(() => {
            this.stopDrawing();
            this.deactivate();
            if (options.callback) {
                options.callback(polygonEntity);
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    // 绘制圆
    drawCircle(options = {}) {
        this.stopDrawing();
        this.activate();
        
        let centerPoint = null;
        let circleEntity = null;
        
        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                if (!centerPoint) {
                    centerPoint = position;
                    
                    circleEntity = this.viewer.entities.add({
                        position: centerPoint,
                        ellipse: {
                            semiMajorAxis: new Cesium.CallbackProperty(() => {
                                if (this.currentRadius) {
                                    return this.currentRadius;
                                }
                                return 1;
                            }, false),
                            semiMinorAxis: new Cesium.CallbackProperty(() => {
                                if (this.currentRadius) {
                                    return this.currentRadius;
                                }
                                return 1;
                            }, false),
                            material: Cesium.Color.fromCssColorString(options.color || '#ffff00').withAlpha(options.alpha || 0.5),
                            outline: options.outline !== false,
                            outlineColor: Cesium.Color.fromCssColorString(options.outlineColor || '#ffff00'),
                            height: options.height || 0
                        }
                    });
                    
                    this.currentDrawing = circleEntity;
                    this.plotEntities.push(circleEntity);
                } else {
                    // 确定半径，完成绘制
                    this.stopDrawing();
                    this.deactivate();
                    
                    if (options.callback) {
                        options.callback(circleEntity);
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction((event) => {
            if (centerPoint) {
                const position = this.viewer.camera.pickEllipsoid(event.endPosition, this.viewer.scene.globe.ellipsoid);
                if (position) {
                    this.currentRadius = Cesium.Cartesian3.distance(centerPoint, position);
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }

    // 绘制矩形
    drawRectangle(options = {}) {
        this.stopDrawing();
        this.activate();
        
        let startPoint = null;
        let rectangleEntity = null;
        
        this.handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                if (!startPoint) {
                    startPoint = position;
                    
                    rectangleEntity = this.viewer.entities.add({
                        rectangle: {
                            coordinates: new Cesium.CallbackProperty(() => {
                                if (this.currentEndPoint && startPoint) {
                                    const startCart = Cesium.Cartographic.fromCartesian(startPoint);
                                    const endCart = Cesium.Cartographic.fromCartesian(this.currentEndPoint);
                                    
                                    return Cesium.Rectangle.fromCartographicArray([
                                        startCart,
                                        endCart
                                    ]);
                                }
                                return new Cesium.Rectangle();
                            }, false),
                            material: Cesium.Color.fromCssColorString(options.color || '#ffff00').withAlpha(options.alpha || 0.5),
                            outline: options.outline !== false,
                            outlineColor: Cesium.Color.fromCssColorString(options.outlineColor || '#ffff00'),
                            height: options.height || 0
                        }
                    });
                    
                    this.currentDrawing = rectangleEntity;
                    this.plotEntities.push(rectangleEntity);
                } else {
                    // 完成绘制
                    this.stopDrawing();
                    this.deactivate();
                    
                    if (options.callback) {
                        options.callback(rectangleEntity);
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        this.handler.setInputAction((event) => {
            if (startPoint) {
                const position = this.viewer.camera.pickEllipsoid(event.endPosition, this.viewer.scene.globe.ellipsoid);
                if (position) {
                    this.currentEndPoint = position;
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }

    // 添加文字标注
    addLabel(position, text, options = {}) {
        const entity = this.viewer.entities.add({
            position: position,
            label: {
                text: text,
                font: options.font || '16pt sans-serif',
                fillColor: Cesium.Color.fromCssColorString(options.fillColor || '#ffffff'),
                outlineColor: Cesium.Color.fromCssColorString(options.outlineColor || '#000000'),
                outlineWidth: options.outlineWidth || 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new Cesium.Cartesian2(options.pixelOffsetX || 0, options.pixelOffsetY || 0),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            }
        });
        
        this.plotEntities.push(entity);
        return entity;
    }

    // 清除所有标绘
    clearAll() {
        this.plotEntities.forEach(entity => {
            this.viewer.entities.remove(entity);
        });
        this.plotEntities = [];
        this.stopDrawing();
        this.deactivate();
    }

    // 删除指定实体
    removeEntity(entity) {
        const index = this.plotEntities.indexOf(entity);
        if (index > -1) {
            this.plotEntities.splice(index, 1);
            this.viewer.entities.remove(entity);
        }
    }

    // 获取所有标绘实体
    getAllEntities() {
        return this.plotEntities;
    }

    // 销毁
    destroy() {
        this.clearAll();
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }
    }
}

// 导出
window.PlotTool = PlotTool;
