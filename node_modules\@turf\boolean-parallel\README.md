# @turf/boolean-parallel

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanParallel

<PERSON><PERSON>-<PERSON><PERSON><PERSON> returns True if each segment of `line1` is parallel to the correspondent segment of `line2`

### Parameters

*   `line1` **([Geometry][1] | [Feature][2]<[LineString][3]>)** GeoJSON Feature or Geometry
*   `line2` **([Geometry][1] | [Feature][2]<[LineString][3]>)** GeoJSON Feature or Geometry

### Examples

```javascript
var line1 = turf.lineString([[0, 0], [0, 1]]);
var line2 = turf.lineString([[1, 0], [1, 1]]);

turf.booleanParallel(line1, line2);
//=true
```

Returns **[boolean][4]** true/false if the lines are parallel

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-parallel
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
