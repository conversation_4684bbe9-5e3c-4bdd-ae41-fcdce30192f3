import { MainModule, RawGaussianCloud } from './build/main';
export type GaussianCloud = {
    numPoints: number;
    shDegree: number;
    antialiased: boolean;
    positions: Float32Array;
    scales: Float32Array;
    rotations: Float32Array;
    alphas: Float32Array;
    colors: Float32Array;
    sh: Float32Array;
};
/**
 * create new gaussian cloud from raw
 * @param wasmModule emscripten wasm main module
 * @param raw RawGSCloud
 * @returns new Gaussian Cloud
 */
export declare const createGaussianCloudFromRaw: (wasmModule: MainModule, raw: RawGaussianCloud, options?: {
    colorScaleFactor?: number;
}) => GaussianCloud;
/**
 * dispose raw gaussian cloud's vector from heap memory
 * @param raw raw gaussian cloud
 */
export declare const disposeRawGSCloud: (wasmModule: MainModule, raw: RawGaussianCloud) => void;
