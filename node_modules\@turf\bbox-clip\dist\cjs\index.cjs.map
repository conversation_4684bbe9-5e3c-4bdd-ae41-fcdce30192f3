{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-bbox-clip/dist/cjs/index.cjs", "../../index.ts", "../../lib/lineclip.ts"], "names": [], "mappings": "AAAA;ACUA;AACE;AACA;AACA;AACA;AAAA,wCACK;AACP,4CAAwB;ADRxB;AACA;AELO,SAAS,QAAA,CACd,MAAA,EACA,IAAA,EACA,MAAA,EACc;AACd,EAAA,IAAI,IAAA,EAAM,MAAA,CAAO,MAAA,EACf,MAAA,EAAQ,OAAA,CAAQ,MAAA,CAAO,CAAC,CAAA,EAAG,IAAI,CAAA,EAC/B,KAAA,EAAO,CAAC,CAAA,EACR,CAAA,EACA,KAAA,EACA,QAAA;AACF,EAAA,IAAI,CAAA;AACJ,EAAA,IAAI,CAAA;AAEJ,EAAA,GAAA,CAAI,CAAC,MAAA,EAAQ,OAAA,EAAS,CAAC,CAAA;AAEvB,EAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,GAAA,EAAK,CAAA,EAAA,EAAK;AACxB,IAAA,EAAA,EAAI,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA;AAChB,IAAA,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA;AACZ,IAAA,MAAA,EAAQ,SAAA,EAAW,OAAA,CAAQ,CAAA,EAAG,IAAI,CAAA;AAElC,IAAA,MAAA,CAAO,IAAA,EAAM;AACX,MAAA,GAAA,CAAI,CAAA,CAAE,MAAA,EAAQ,KAAA,CAAA,EAAQ;AAEpB,QAAA,IAAA,CAAK,IAAA,CAAK,CAAC,CAAA;AAEX,QAAA,GAAA,CAAI,MAAA,IAAU,QAAA,EAAU;AAEtB,UAAA,IAAA,CAAK,IAAA,CAAK,CAAC,CAAA;AAEX,UAAA,GAAA,CAAI,EAAA,EAAI,IAAA,EAAM,CAAA,EAAG;AAEf,YAAA,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA;AAChB,YAAA,KAAA,EAAO,CAAC,CAAA;AAAA,UACV;AAAA,QACF,EAAA,KAAA,GAAA,CAAW,EAAA,IAAM,IAAA,EAAM,CAAA,EAAG;AACxB,UAAA,IAAA,CAAK,IAAA,CAAK,CAAC,CAAA;AAAA,QACb;AACA,QAAA,KAAA;AAAA,MACF,EAAA,KAAA,GAAA,CAAW,MAAA,EAAQ,KAAA,EAAO;AAExB,QAAA,KAAA;AAAA,MACF,EAAA,KAAA,GAAA,CAAW,KAAA,EAAO;AAEhB,QAAA,EAAA,EAAI,SAAA,CAAU,CAAA,EAAG,CAAA,EAAG,KAAA,EAAO,IAAI,CAAA;AAC/B,QAAA,MAAA,EAAQ,OAAA,CAAQ,CAAA,EAAG,IAAI,CAAA;AAAA,MACzB,EAAA,KAAO;AAEL,QAAA,EAAA,EAAI,SAAA,CAAU,CAAA,EAAG,CAAA,EAAG,KAAA,EAAO,IAAI,CAAA;AAC/B,QAAA,MAAA,EAAQ,OAAA,CAAQ,CAAA,EAAG,IAAI,CAAA;AAAA,MACzB;AAAA,IACF;AAEA,IAAA,MAAA,EAAQ,QAAA;AAAA,EACV;AAEA,EAAA,GAAA,CAAI,IAAA,CAAK,MAAA,EAAQ,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA;AAEjC,EAAA,OAAO,MAAA;AACT;AAIO,SAAS,WAAA,CAAY,MAAA,EAAoB,IAAA,EAAwB;AACtE,EAAA,IAAI,MAAA,EAAoB,IAAA,EAAM,IAAA,EAAM,UAAA,EAAY,CAAA,EAAG,CAAA,EAAG,MAAA;AAGtD,EAAA,IAAA,CAAK,KAAA,EAAO,CAAA,EAAG,KAAA,GAAQ,CAAA,EAAG,KAAA,GAAQ,CAAA,EAAG;AACnC,IAAA,OAAA,EAAS,CAAC,CAAA;AACV,IAAA,KAAA,EAAO,MAAA,CAAO,MAAA,CAAO,OAAA,EAAS,CAAC,CAAA;AAC/B,IAAA,WAAA,EAAa,CAAA,CAAE,OAAA,CAAQ,IAAA,EAAM,IAAI,EAAA,EAAI,IAAA,CAAA;AAErC,IAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AAClC,MAAA,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA;AACZ,MAAA,OAAA,EAAS,CAAA,CAAE,OAAA,CAAQ,CAAA,EAAG,IAAI,EAAA,EAAI,IAAA,CAAA;AAG9B,MAAA,GAAA,CAAI,OAAA,IAAW,UAAA,EAAY,MAAA,CAAO,IAAA,CAAK,SAAA,CAAU,IAAA,EAAM,CAAA,EAAG,IAAA,EAAM,IAAI,CAAE,CAAA;AAEtE,MAAA,GAAA,CAAI,MAAA,EAAQ,MAAA,CAAO,IAAA,CAAK,CAAC,CAAA;AAEzB,MAAA,KAAA,EAAO,CAAA;AACP,MAAA,WAAA,EAAa,MAAA;AAAA,IACf;AAEA,IAAA,OAAA,EAAS,MAAA;AAET,IAAA,GAAA,CAAI,CAAC,MAAA,CAAO,MAAA,EAAQ,KAAA;AAAA,EACtB;AAEA,EAAA,OAAO,MAAA;AACT;AAIA,SAAS,SAAA,CACP,CAAA,EACA,CAAA,EACA,IAAA,EACA,IAAA,EACiB;AACjB,EAAA,OAAO,KAAA,EAAO,EAAA,EACV,CAAC,CAAA,CAAE,CAAC,EAAA,EAAA,CAAM,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAM,IAAA,CAAK,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAO,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,CAAA,EAAI,IAAA,CAAK,CAAC,CAAC,EAAA,EACnE,KAAA,EAAO,EAAA,EACL,CAAC,CAAA,CAAE,CAAC,EAAA,EAAA,CAAM,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAM,IAAA,CAAK,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAO,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,CAAA,EAAI,IAAA,CAAK,CAAC,CAAC,EAAA,EACnE,KAAA,EAAO,EAAA,EACL,CAAC,IAAA,CAAK,CAAC,CAAA,EAAG,CAAA,CAAE,CAAC,EAAA,EAAA,CAAM,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAM,IAAA,CAAK,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAO,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,CAAE,EAAA,EACnE,KAAA,EAAO,EAAA,EACL,CAAC,IAAA,CAAK,CAAC,CAAA,EAAG,CAAA,CAAE,CAAC,EAAA,EAAA,CAAM,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAM,IAAA,CAAK,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,EAAA,EAAA,CAAO,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,CAAE,CAAC,CAAA,CAAE,EAAA,EACnE,IAAA;AACZ;AASA,SAAS,OAAA,CAAQ,CAAA,EAAa,IAAA,EAAY;AACxC,EAAA,IAAI,KAAA,EAAO,CAAA;AAEX,EAAA,GAAA,CAAI,CAAA,CAAE,CAAC,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,EAAG,KAAA,GAAQ,CAAA;AAAA,EAAA,KAAA,GAAA,CAEnB,CAAA,CAAE,CAAC,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,EAAG,KAAA,GAAQ,CAAA;AAEjC,EAAA,GAAA,CAAI,CAAA,CAAE,CAAC,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,EAAG,KAAA,GAAQ,CAAA;AAAA,EAAA,KAAA,GAAA,CAEnB,CAAA,CAAE,CAAC,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,EAAG,KAAA,GAAQ,CAAA;AAEjC,EAAA,OAAO,IAAA;AACT;AF1DA;AACA;ACzCA,SAAS,QAAA,CAGP,OAAA,EAA4B,IAAA,EAAY;AACxC,EAAA,MAAM,KAAA,EAAO,gCAAA,OAAe,CAAA;AAC5B,EAAA,MAAM,KAAA,EAAO,IAAA,CAAK,IAAA;AAClB,EAAA,MAAM,WAAA,EAAa,OAAA,CAAQ,KAAA,IAAS,UAAA,EAAY,OAAA,CAAQ,WAAA,EAAa,CAAC,CAAA;AACtE,EAAA,IAAI,OAAA,EAAgB,IAAA,CAAK,WAAA;AAEzB,EAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,IACZ,KAAK,YAAA;AAAA,IACL,KAAK,iBAAA,EAAmB;AACtB,MAAA,MAAM,MAAA,EAAe,CAAC,CAAA;AACtB,MAAA,GAAA,CAAI,KAAA,IAAS,YAAA,EAAc;AACzB,QAAA,OAAA,EAAS,CAAC,MAAM,CAAA;AAAA,MAClB;AACA,MAAA,MAAA,CAAO,OAAA,CAAQ,CAAC,IAAA,EAAA,GAAS;AACvB,QAAA,QAAA,CAAS,IAAA,EAAM,IAAA,EAAM,KAAK,CAAA;AAAA,MAC5B,CAAC,CAAA;AACD,MAAA,GAAA,CAAI,KAAA,CAAM,OAAA,IAAW,CAAA,EAAG;AACtB,QAAA,OAAO,iCAAA,KAAW,CAAM,CAAC,CAAA,EAAG,UAAU,CAAA;AAAA,MACxC;AACA,MAAA,OAAO,sCAAA,KAAgB,EAAO,UAAU,CAAA;AAAA,IAC1C;AAAA,IACA,KAAK,SAAA;AACH,MAAA,OAAO,8BAAA,WAAQ,CAAY,MAAA,EAAQ,IAAI,CAAA,EAAG,UAAU,CAAA;AAAA,IACtD,KAAK,cAAA;AACH,MAAA,OAAO,mCAAA;AAAA,QACL,MAAA,CAAO,GAAA,CAAI,CAAC,IAAA,EAAA,GAAS;AACnB,UAAA,OAAO,WAAA,CAAY,IAAA,EAAM,IAAI,CAAA;AAAA,QAC/B,CAAC,CAAA;AAAA,QACD;AAAA,MACF,CAAA;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,KAAA,EAAO,gBAAgB,CAAA;AAAA,EACzD;AACF;AAEA,SAAS,WAAA,CAAY,KAAA,EAAqB,IAAA,EAAY;AACpD,EAAA,MAAM,SAAA,EAAW,CAAC,CAAA;AAClB,EAAA,IAAA,CAAA,MAAW,KAAA,GAAQ,KAAA,EAAO;AACxB,IAAA,MAAM,QAAA,EAAU,WAAA,CAAY,IAAA,EAAM,IAAI,CAAA;AACtC,IAAA,GAAA,CAAI,OAAA,CAAQ,OAAA,EAAS,CAAA,EAAG;AACtB,MAAA,GAAA,CACE,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,OAAA,CAAQ,OAAA,CAAQ,OAAA,EAAS,CAAC,CAAA,CAAE,CAAC,EAAA,GAC/C,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,OAAA,CAAQ,OAAA,CAAQ,OAAA,EAAS,CAAC,CAAA,CAAE,CAAC,CAAA,EAC/C;AACA,QAAA,OAAA,CAAQ,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAC,CAAA;AAAA,MACzB;AACA,MAAA,GAAA,CAAI,OAAA,CAAQ,OAAA,GAAU,CAAA,EAAG;AACvB,QAAA,QAAA,CAAS,IAAA,CAAK,OAAO,CAAA;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,QAAA;AACT;AAGA,IAAO,uBAAA,EAAQ,QAAA;ADiCf;AACE;AACA;AACF,sEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-bbox-clip/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  BBox,\n  Feature,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  GeoJsonProperties,\n  Polygon,\n} from \"geojson\";\n\nimport {\n  lineString,\n  multiLineString,\n  multiPolygon,\n  polygon,\n} from \"@turf/helpers\";\nimport { getGeom } from \"@turf/invariant\";\nimport { lineclip, polygonclip } from \"./lib/lineclip.js\";\n\n/**\n * Takes a {@link Feature} and a bbox and clips the feature to the bbox using\n * [lineclip](https://github.com/mapbox/lineclip).\n * May result in degenerate edges when clipping Polygons.\n *\n * @function\n * @param {Feature<LineString|MultiLineString|Polygon|MultiPolygon>} feature feature to clip to the bbox\n * @param {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @returns {Feature<LineString|MultiLineString|Polygon|MultiPolygon>} clipped Feature\n * @example\n * var bbox = [0, 0, 10, 10];\n * var poly = turf.polygon([[[2, 2], [8, 4], [12, 8], [3, 7], [2, 2]]]);\n *\n * var clipped = turf.bboxClip(poly, bbox);\n *\n * //addToMap\n * var addToMap = [bbox, poly, clipped]\n */\nfunction bboxClip<\n  G extends Polygon | MultiPolygon | LineString | MultiLineString,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(feature: Feature<G, P> | G, bbox: BBox) {\n  const geom = getGeom(feature);\n  const type = geom.type;\n  const properties = feature.type === \"Feature\" ? feature.properties : {};\n  let coords: any[] = geom.coordinates;\n\n  switch (type) {\n    case \"LineString\":\n    case \"MultiLineString\": {\n      const lines: any[] = [];\n      if (type === \"LineString\") {\n        coords = [coords];\n      }\n      coords.forEach((line) => {\n        lineclip(line, bbox, lines);\n      });\n      if (lines.length === 1) {\n        return lineString(lines[0], properties);\n      }\n      return multiLineString(lines, properties);\n    }\n    case \"Polygon\":\n      return polygon(clipPolygon(coords, bbox), properties);\n    case \"MultiPolygon\":\n      return multiPolygon(\n        coords.map((poly) => {\n          return clipPolygon(poly, bbox);\n        }),\n        properties\n      );\n    default:\n      throw new Error(\"geometry \" + type + \" not supported\");\n  }\n}\n\nfunction clipPolygon(rings: number[][][], bbox: BBox) {\n  const outRings = [];\n  for (const ring of rings) {\n    const clipped = polygonclip(ring, bbox);\n    if (clipped.length > 0) {\n      if (\n        clipped[0][0] !== clipped[clipped.length - 1][0] ||\n        clipped[0][1] !== clipped[clipped.length - 1][1]\n      ) {\n        clipped.push(clipped[0]);\n      }\n      if (clipped.length >= 4) {\n        outRings.push(clipped);\n      }\n    }\n  }\n  return outRings;\n}\n\nexport { bboxClip };\nexport default bboxClip;\n", "// <PERSON><PERSON> line clipping algorithm, adapted to efficiently\n// handle polylines rather than just segments\nimport { BBox } from \"geojson\";\n\nexport function lineclip(\n  points: number[][],\n  bbox: BBox,\n  result?: number[][][]\n): number[][][] {\n  var len = points.length,\n    codeA = bitCode(points[0], bbox),\n    part = [] as number[][],\n    i,\n    codeB,\n    lastCode;\n  let a: number[];\n  let b: number[];\n\n  if (!result) result = [];\n\n  for (i = 1; i < len; i++) {\n    a = points[i - 1];\n    b = points[i];\n    codeB = lastCode = bitCode(b, bbox);\n\n    while (true) {\n      if (!(codeA | codeB)) {\n        // accept\n        part.push(a);\n\n        if (codeB !== lastCode) {\n          // segment went outside\n          part.push(b);\n\n          if (i < len - 1) {\n            // start a new line\n            result.push(part);\n            part = [];\n          }\n        } else if (i === len - 1) {\n          part.push(b);\n        }\n        break;\n      } else if (codeA & codeB) {\n        // trivial reject\n        break;\n      } else if (codeA) {\n        // a outside, intersect with clip edge\n        a = intersect(a, b, codeA, bbox)!;\n        codeA = bitCode(a, bbox);\n      } else {\n        // b outside\n        b = intersect(a, b, codeB, bbox)!;\n        codeB = bitCode(b, bbox);\n      }\n    }\n\n    codeA = lastCode;\n  }\n\n  if (part.length) result.push(part);\n\n  return result;\n}\n\n// Sutherland-Hodgeman polygon clipping algorithm\n\nexport function polygonclip(points: number[][], bbox: BBox): number[][] {\n  var result: number[][], edge, prev, prevInside, i, p, inside;\n\n  // clip against each side of the clip rectangle\n  for (edge = 1; edge <= 8; edge *= 2) {\n    result = [];\n    prev = points[points.length - 1];\n    prevInside = !(bitCode(prev, bbox) & edge);\n\n    for (i = 0; i < points.length; i++) {\n      p = points[i];\n      inside = !(bitCode(p, bbox) & edge);\n\n      // if segment goes through the clip window, add an intersection\n      if (inside !== prevInside) result.push(intersect(prev, p, edge, bbox)!);\n\n      if (inside) result.push(p); // add a point if it's inside\n\n      prev = p;\n      prevInside = inside;\n    }\n\n    points = result;\n\n    if (!points.length) break;\n  }\n\n  return result!;\n}\n\n// intersect a segment against one of the 4 lines that make up the bbox\n\nfunction intersect(\n  a: number[],\n  b: number[],\n  edge: number,\n  bbox: BBox\n): number[] | null {\n  return edge & 8\n    ? [a[0] + ((b[0] - a[0]) * (bbox[3] - a[1])) / (b[1] - a[1]), bbox[3]] // top\n    : edge & 4\n      ? [a[0] + ((b[0] - a[0]) * (bbox[1] - a[1])) / (b[1] - a[1]), bbox[1]] // bottom\n      : edge & 2\n        ? [bbox[2], a[1] + ((b[1] - a[1]) * (bbox[2] - a[0])) / (b[0] - a[0])] // right\n        : edge & 1\n          ? [bbox[0], a[1] + ((b[1] - a[1]) * (bbox[0] - a[0])) / (b[0] - a[0])] // left\n          : null;\n}\n\n// bit code reflects the point position relative to the bbox:\n\n//         left  mid  right\n//    top  1001  1000  1010\n//    mid  0001  0000  0010\n// bottom  0101  0100  0110\n\nfunction bitCode(p: number[], bbox: BBox) {\n  var code = 0;\n\n  if (p[0] < bbox[0]) code |= 1;\n  // left\n  else if (p[0] > bbox[2]) code |= 2; // right\n\n  if (p[1] < bbox[1]) code |= 4;\n  // bottom\n  else if (p[1] > bbox[3]) code |= 8; // top\n\n  return code;\n}\n"]}