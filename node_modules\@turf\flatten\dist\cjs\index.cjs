"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _meta = require('@turf/meta');
var _helpers = require('@turf/helpers');
function flatten(geojson) {
  if (!geojson) throw new Error("geojson is required");
  var results = [];
  _meta.flattenEach.call(void 0, geojson, function(feature) {
    results.push(feature);
  });
  return _helpers.featureCollection.call(void 0, results);
}
var turf_flatten_default = flatten;



exports.default = turf_flatten_default; exports.flatten = flatten;
//# sourceMappingURL=index.cjs.map