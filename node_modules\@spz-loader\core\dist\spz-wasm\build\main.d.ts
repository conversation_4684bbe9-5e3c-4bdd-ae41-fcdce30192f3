// TypeScript bindings for emscripten-generated code.  Automatically generated at compile time.
declare namespace RuntimeExports {
    let HEAPF32: any;
    let HEAPU8: any;
}
interface WasmModule {
  _malloc(_0: number): number;
  _free(_0: number): void;
}

export interface ClassHandle {
  isAliasOf(other: ClassHandle): boolean;
  delete(): void;
  deleteLater(): this;
  isDeleted(): boolean;
  // @ts-ignore - If targeting lower than ESNext, this symbol might not exist.
  [Symbol.dispose](): void;
  clone(): this;
}
export interface VectorFloat32 extends ClassHandle {
  size(): number;
  get(_0: number): number | undefined;
  push_back(_0: number): void;
  resize(_0: number, _1: number): void;
  set(_0: number, _1: number): boolean;
}

export interface VectorUInt8T extends ClassHandle {
  push_back(_0: number): void;
  resize(_0: number, _1: number): void;
  size(): number;
  get(_0: number): number | undefined;
  set(_0: number, _1: number): boolean;
}

export interface CoordinateSystemValue<T extends number> {
  value: T;
}
export type CoordinateSystem = CoordinateSystemValue<0>|CoordinateSystemValue<1>|CoordinateSystemValue<2>|CoordinateSystemValue<3>|CoordinateSystemValue<4>|CoordinateSystemValue<5>|CoordinateSystemValue<6>|CoordinateSystemValue<7>|CoordinateSystemValue<8>;

export type UnpackOptions = {
  coordinateSystem: CoordinateSystem
};

export type RawGaussianCloud = {
  numPoints: number,
  shDegree: number,
  antialiased: boolean,
  positions: VectorFloat32,
  scales: VectorFloat32,
  rotations: VectorFloat32,
  alphas: VectorFloat32,
  colors: VectorFloat32,
  sh: VectorFloat32
};

interface EmbindModule {
  VectorFloat32: {
    new(): VectorFloat32;
  };
  VectorUInt8T: {
    new(): VectorUInt8T;
  };
  CoordinateSystem: {UNSPECIFIED: CoordinateSystemValue<0>, LDB: CoordinateSystemValue<1>, RDB: CoordinateSystemValue<2>, LUB: CoordinateSystemValue<3>, RUB: CoordinateSystemValue<4>, LDF: CoordinateSystemValue<5>, RDF: CoordinateSystemValue<6>, LUF: CoordinateSystemValue<7>, RUF: CoordinateSystemValue<8>};
  vf32_ptr(_0: VectorFloat32): number;
  load_spz(_0: number, _1: number, _2: UnpackOptions): RawGaussianCloud;
}

export type MainModule = WasmModule & typeof RuntimeExports & EmbindModule;
export default function MainModuleFactory (options?: unknown): Promise<MainModule>;
