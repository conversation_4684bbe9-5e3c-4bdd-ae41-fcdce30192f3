# @turf/boolean-overlap

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanOverlap

Compares two geometries of the same dimension and returns true if their intersection set results in a geometry
different from both but of the same dimension. It applies to Polygon/Polygon, LineString/LineString,
Multipoint/Multipoint, MultiLineString/MultiLineString and MultiPolygon/MultiPolygon.

In other words, it returns true if the two geometries overlap, provided that neither completely contains the other.

### Parameters

*   `feature1` **([Geometry][1] | [Feature][2]<([LineString][3] | [MultiLineString][4] | [Polygon][5] | [MultiPolygon][6])>)** input
*   `feature2` **([Geometry][1] | [Feature][2]<([LineString][3] | [MultiLineString][4] | [Polygon][5] | [MultiPolygon][6])>)** input

### Examples

```javascript
var poly1 = turf.polygon([[[0,0],[0,5],[5,5],[5,0],[0,0]]]);
var poly2 = turf.polygon([[[1,1],[1,6],[6,6],[6,1],[1,1]]]);
var poly3 = turf.polygon([[[10,10],[10,15],[15,15],[15,10],[10,10]]]);

turf.booleanOverlap(poly1, poly2)
//=true
turf.booleanOverlap(poly2, poly3)
//=false
```

Returns **[boolean][7]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-overlap
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```


### Diagrams

![esri-overlaps](diagrams/esri-overlaps.gif)