{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-great-circle/dist/cjs/index.cjs", "../../index.js", "../../lib/arc.js"], "names": [], "mappings": "AAAA;ACAA,wCAA2B;AAC3B,4CAAyB;ADEzB;AACA;AEsBA,IAAI,IAAA,EAAM,IAAA,CAAK,GAAA,EAAK,GAAA;AACpB,IAAI,IAAA,EAAM,IAAA,EAAM,IAAA,CAAK,EAAA;AAErB,IAAI,MAAA,EAAQ,QAAA,CAAU,GAAA,EAAK,GAAA,EAAK;AAC9B,EAAA,IAAA,CAAK,IAAA,EAAM,GAAA;AACX,EAAA,IAAA,CAAK,IAAA,EAAM,GAAA;AACX,EAAA,IAAA,CAAK,EAAA,EAAI,IAAA,EAAM,GAAA;AACf,EAAA,IAAA,CAAK,EAAA,EAAI,IAAA,EAAM,GAAA;AACjB,CAAA;AAEA,KAAA,CAAM,SAAA,CAAU,KAAA,EAAO,QAAA,CAAA,EAAY;AACjC,EAAA,OAAO,MAAA,CAAO,IAAA,CAAK,GAAG,CAAA,CAAE,KAAA,CAAM,CAAA,EAAG,CAAC,EAAA,EAAI,IAAA,EAAM,MAAA,CAAO,IAAA,CAAK,GAAG,CAAA,CAAE,KAAA,CAAM,CAAA,EAAG,CAAC,CAAA;AACzE,CAAA;AAEA,KAAA,CAAM,SAAA,CAAU,SAAA,EAAW,QAAA,CAAA,EAAY;AACrC,EAAA,IAAI,SAAA,EAAW,CAAA,EAAA,EAAK,IAAA,CAAK,GAAA;AACzB,EAAA,IAAI,SAAA,EAAW,IAAA,CAAK,IAAA,EAAM,EAAA,EAAI,IAAA,EAAM,IAAA,CAAK,IAAA,EAAA,CAAO,IAAA,EAAM,IAAA,CAAK,GAAA,EAAA,EAAO,CAAA,CAAA;AAClE,EAAA,OAAO,IAAI,KAAA,CAAM,QAAA,EAAU,QAAQ,CAAA;AACrC,CAAA;AAEA,IAAI,WAAA,EAAa,QAAA,CAAA,EAAY;AAC3B,EAAA,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA;AACf,EAAA,IAAA,CAAK,OAAA,EAAS,CAAA;AAChB,CAAA;AAEA,UAAA,CAAW,SAAA,CAAU,QAAA,EAAU,QAAA,CAAU,KAAA,EAAO;AAC9C,EAAA,IAAA,CAAK,MAAA,EAAA;AACL,EAAA,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,KAAK,CAAA;AACxB,CAAA;AAEA,IAAI,IAAA,EAAM,QAAA,CAAU,UAAA,EAAY;AAC9B,EAAA,IAAA,CAAK,WAAA,EAAa,WAAA,GAAc,CAAC,CAAA;AACjC,EAAA,IAAA,CAAK,WAAA,EAAa,CAAC,CAAA;AACrB,CAAA;AAEA,GAAA,CAAI,SAAA,CAAU,KAAA,EAAO,QAAA,CAAA,EAAY;AAC/B,EAAA,GAAA,CAAI,IAAA,CAAK,UAAA,CAAW,OAAA,GAAU,CAAA,EAAG;AAC/B,IAAA,OAAO;AAAA,MACL,QAAA,EAAU,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAK,CAAA;AAAA,MAClD,IAAA,EAAM,SAAA;AAAA,MACN,UAAA,EAAY,IAAA,CAAK;AAAA,IACnB,CAAA;AAAA,EACF,EAAA,KAAA,GAAA,CAAW,IAAA,CAAK,UAAA,CAAW,OAAA,IAAW,CAAA,EAAG;AACvC,IAAA,OAAO;AAAA,MACL,QAAA,EAAU,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,OAAO,CAAA;AAAA,MACvE,IAAA,EAAM,SAAA;AAAA,MACN,UAAA,EAAY,IAAA,CAAK;AAAA,IACnB,CAAA;AAAA,EACF,EAAA,KAAO;AACL,IAAA,IAAI,UAAA,EAAY,CAAC,CAAA;AACjB,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,UAAA,CAAW,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC/C,MAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,MAAM,CAAA;AAAA,IAC1C;AACA,IAAA,OAAO;AAAA,MACL,QAAA,EAAU,EAAE,IAAA,EAAM,iBAAA,EAAmB,WAAA,EAAa,UAAU,CAAA;AAAA,MAC5D,IAAA,EAAM,SAAA;AAAA,MACN,UAAA,EAAY,IAAA,CAAK;AAAA,IACnB,CAAA;AAAA,EACF;AACF,CAAA;AAGA,GAAA,CAAI,SAAA,CAAU,IAAA,EAAM,QAAA,CAAA,EAAY;AAC9B,EAAA,IAAI,WAAA,EAAa,EAAA;AACjB,EAAA,IAAI,IAAA,EAAM,aAAA;AACV,EAAA,IAAI,QAAA,EAAU,QAAA,CAAU,CAAA,EAAG;AACzB,IAAA,IAAA,GAAO,CAAA,CAAE,CAAC,EAAA,EAAI,IAAA,EAAM,CAAA,CAAE,CAAC,EAAA,EAAI,GAAA;AAAA,EAC7B,CAAA;AACA,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,UAAA,CAAW,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC/C,IAAA,GAAA,CAAI,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,MAAA,CAAO,OAAA,IAAW,CAAA,EAAG;AAC1C,MAAA,OAAO,mBAAA;AAAA,IACT,EAAA,KAAO;AACL,MAAA,IAAI,OAAA,EAAS,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,MAAA;AAChC,MAAA,MAAA,CAAO,OAAA,CAAQ,OAAO,CAAA;AACtB,MAAA,WAAA,GAAc,GAAA,CAAI,SAAA,CAAU,CAAA,EAAG,GAAA,CAAI,OAAA,EAAS,CAAC,EAAA,EAAI,GAAA;AAAA,IACnD;AAAA,EACF;AACA,EAAA,OAAO,UAAA;AACT,CAAA;AAMA,IAAI,YAAA,EAAc,QAAA,CAAU,KAAA,EAAO,GAAA,EAAK,UAAA,EAAY;AAClD,EAAA,GAAA,CAAI,CAAC,MAAA,GAAS,KAAA,CAAM,EAAA,IAAM,KAAA,EAAA,GAAa,KAAA,CAAM,EAAA,IAAM,KAAA,CAAA,EAAW;AAC5D,IAAA,MAAM,IAAI,KAAA;AAAA,MACR;AAAA,IACF,CAAA;AAAA,EACF;AACA,EAAA,GAAA,CAAI,CAAC,IAAA,GAAO,GAAA,CAAI,EAAA,IAAM,KAAA,EAAA,GAAa,GAAA,CAAI,EAAA,IAAM,KAAA,CAAA,EAAW;AACtD,IAAA,MAAM,IAAI,KAAA;AAAA,MACR;AAAA,IACF,CAAA;AAAA,EACF;AACA,EAAA,IAAA,CAAK,MAAA,EAAQ,IAAI,KAAA,CAAM,KAAA,CAAM,CAAA,EAAG,KAAA,CAAM,CAAC,CAAA;AACvC,EAAA,IAAA,CAAK,IAAA,EAAM,IAAI,KAAA,CAAM,GAAA,CAAI,CAAA,EAAG,GAAA,CAAI,CAAC,CAAA;AACjC,EAAA,IAAA,CAAK,WAAA,EAAa,WAAA,GAAc,CAAC,CAAA;AAEjC,EAAA,IAAI,EAAA,EAAI,IAAA,CAAK,KAAA,CAAM,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,CAAA;AAChC,EAAA,IAAI,EAAA,EAAI,IAAA,CAAK,KAAA,CAAM,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,CAAA;AAChC,EAAA,IAAI,EAAA,EACF,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,CAAG,CAAA,EAAG,CAAC,EAAA,EAC7B,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAC,EAAA,EACnB,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,CAAC,EAAA,EACnB,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,CAAG,CAAA,EAAG,CAAC,CAAA;AACjC,EAAA,IAAA,CAAK,EAAA,EAAI,EAAA,EAAM,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,CAAC,CAAC,CAAA;AAErC,EAAA,GAAA,CAAI,IAAA,CAAK,EAAA,IAAM,IAAA,CAAK,EAAA,EAAI;AACtB,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,cAAA,EACE,KAAA,CAAM,IAAA,CAAK,EAAA,EACX,QAAA,EACA,GAAA,CAAI,IAAA,CAAK,EAAA,EACT;AAAA,IACJ,CAAA;AAAA,EACF,EAAA,KAAA,GAAA,CAAW,KAAA,CAAM,IAAA,CAAK,CAAC,CAAA,EAAG;AACxB,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,4CAAA,EAA8C,MAAA,EAAQ,QAAA,EAAU;AAAA,IAClE,CAAA;AAAA,EACF;AACF,CAAA;AAKA,WAAA,CAAY,SAAA,CAAU,YAAA,EAAc,QAAA,CAAU,CAAA,EAAG;AAC/C,EAAA,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAA,CAAK,EAAA,EAAI,CAAA,EAAA,EAAK,IAAA,CAAK,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,CAAC,CAAA;AACpD,EAAA,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,IAAA,CAAK,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,CAAC,CAAA;AAC9C,EAAA,IAAI,EAAA,EACF,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAC,EAAA,EAClD,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,CAAC,CAAA;AAChD,EAAA,IAAI,EAAA,EACF,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAC,EAAA,EAClD,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,CAAC,CAAA;AAChD,EAAA,IAAI,EAAA,EAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAC,EAAA,EAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,CAAC,CAAA;AAC5D,EAAA,IAAI,IAAA,EAAM,IAAA,EAAM,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,GAAA,CAAI,CAAA,EAAG,CAAC,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,CAAA,EAAG,CAAC,CAAC,CAAC,CAAA;AACxE,EAAA,IAAI,IAAA,EAAM,IAAA,EAAM,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,CAAA;AAC/B,EAAA,OAAO,CAAC,GAAA,EAAK,GAAG,CAAA;AAClB,CAAA;AAKA,WAAA,CAAY,SAAA,CAAU,IAAA,EAAM,QAAA,CAAU,OAAA,EAAS,OAAA,EAAS;AACtD,EAAA,IAAI,WAAA,EAAa,CAAC,CAAA;AAClB,EAAA,GAAA,CAAI,CAAC,QAAA,GAAW,QAAA,GAAW,CAAA,EAAG;AAC5B,IAAA,UAAA,CAAW,IAAA,CAAK,CAAC,IAAA,CAAK,KAAA,CAAM,GAAA,EAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAC,CAAA;AAChD,IAAA,UAAA,CAAW,IAAA,CAAK,CAAC,IAAA,CAAK,GAAA,CAAI,GAAA,EAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAC,CAAA;AAAA,EAC9C,EAAA,KAAO;AACL,IAAA,IAAI,MAAA,EAAQ,EAAA,EAAA,CAAO,QAAA,EAAU,CAAA,CAAA;AAC7B,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,OAAA,EAAS,EAAE,CAAA,EAAG;AAChC,MAAA,IAAI,KAAA,EAAO,MAAA,EAAQ,CAAA;AACnB,MAAA,IAAI,KAAA,EAAO,IAAA,CAAK,WAAA,CAAY,IAAI,CAAA;AAChC,MAAA,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;AAAA,IACtB;AAAA,EACF;AAMA,EAAA,IAAI,YAAA,EAAc,KAAA;AAClB,EAAA,IAAI,mBAAA,EAAqB,CAAA;AAIzB,EAAA,IAAI,iBAAA,EAAmB,QAAA,GAAW,OAAA,CAAQ,OAAA,EAAS,OAAA,CAAQ,OAAA,EAAS,EAAA;AACpE,EAAA,IAAI,cAAA,EAAgB,IAAA,EAAM,gBAAA;AAC1B,EAAA,IAAI,eAAA,EAAiB,CAAA,IAAA,EAAO,gBAAA;AAC5B,EAAA,IAAI,YAAA,EAAc,IAAA,EAAM,gBAAA;AAGxB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,MAAA,EAAQ,EAAE,CAAA,EAAG;AAC1C,IAAA,IAAI,QAAA,EAAU,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAA;AACjC,IAAA,IAAI,IAAA,EAAM,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAA;AACzB,IAAA,IAAI,WAAA,EAAa,IAAA,CAAK,GAAA,CAAI,IAAA,EAAM,OAAO,CAAA;AACvC,IAAA,GAAA,CACE,WAAA,EAAa,YAAA,GAAA,CACX,IAAA,EAAM,cAAA,GAAiB,QAAA,EAAU,eAAA,GAChC,QAAA,EAAU,cAAA,GAAiB,IAAA,EAAM,cAAA,CAAA,EACpC;AACA,MAAA,YAAA,EAAc,IAAA;AAAA,IAChB,EAAA,KAAA,GAAA,CAAW,WAAA,EAAa,kBAAA,EAAoB;AAC1C,MAAA,mBAAA,EAAqB,UAAA;AAAA,IACvB;AAAA,EACF;AAEA,EAAA,IAAI,QAAA,EAAU,CAAC,CAAA;AACf,EAAA,GAAA,CAAI,YAAA,GAAe,mBAAA,EAAqB,gBAAA,EAAkB;AACxD,IAAA,IAAI,QAAA,EAAU,CAAC,CAAA;AACf,IAAA,OAAA,CAAQ,IAAA,CAAK,OAAO,CAAA;AACpB,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,MAAA,EAAQ,EAAE,CAAA,EAAG;AAC1C,MAAA,IAAI,KAAA,EAAO,UAAA,CAAW,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;AACtC,MAAA,GAAA,CAAI,EAAA,EAAI,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,KAAA,EAAO,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAC,EAAA,EAAI,WAAA,EAAa;AAChE,QAAA,IAAI,KAAA,EAAO,UAAA,CAAW,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;AAC1C,QAAA,IAAI,KAAA,EAAO,UAAA,CAAW,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;AAC1C,QAAA,IAAI,KAAA,EAAO,UAAA,CAAW,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;AACtC,QAAA,IAAI,KAAA,EAAO,UAAA,CAAW,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;AACtC,QAAA,GAAA,CACE,KAAA,EAAO,CAAA,IAAA,GACP,KAAA,EAAO,eAAA,GACP,KAAA,IAAS,IAAA,GACT,EAAA,EAAI,EAAA,EAAI,UAAA,CAAW,OAAA,GACnB,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,CAAA,IAAA,GACvB,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,cAAA,EACvB;AACA,UAAA,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,GAAA,EAAM,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AACrC,UAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAQ,IAAA,CAAK,CAAC,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAA,EAAG,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AACjD,UAAA,QAAA;AAAA,QACF,EAAA,KAAA,GAAA,CACE,KAAA,EAAO,cAAA,GACP,KAAA,EAAO,IAAA,GACP,KAAA,IAAS,CAAA,IAAA,GACT,EAAA,EAAI,EAAA,EAAI,UAAA,CAAW,OAAA,GACnB,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,cAAA,GACvB,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,GAAA,EACvB;AACA,UAAA,OAAA,CAAQ,IAAA,CAAK,CAAC,GAAA,EAAK,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AACpC,UAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAQ,IAAA,CAAK,CAAC,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAA,EAAG,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AACjD,UAAA,QAAA;AAAA,QACF;AAEA,QAAA,GAAA,CAAI,KAAA,EAAO,eAAA,GAAkB,KAAA,EAAO,aAAA,EAAe;AAEjD,UAAA,IAAI,KAAA,EAAO,IAAA;AACX,UAAA,KAAA,EAAO,IAAA;AACP,UAAA,KAAA,EAAO,IAAA;AAEP,UAAA,IAAI,KAAA,EAAO,IAAA;AACX,UAAA,KAAA,EAAO,IAAA;AACP,UAAA,KAAA,EAAO,IAAA;AAAA,QACT;AACA,QAAA,GAAA,CAAI,KAAA,EAAO,cAAA,GAAiB,KAAA,EAAO,cAAA,EAAgB;AACjD,UAAA,KAAA,GAAQ,GAAA;AAAA,QACV;AAEA,QAAA,GAAA,CAAI,KAAA,GAAQ,IAAA,GAAO,KAAA,GAAQ,IAAA,GAAO,KAAA,EAAO,IAAA,EAAM;AAC7C,UAAA,IAAI,QAAA,EAAA,CAAW,IAAA,EAAM,IAAA,EAAA,EAAA,CAAS,KAAA,EAAO,IAAA,CAAA;AACrC,UAAA,IAAI,IAAA,EAAM,QAAA,EAAU,KAAA,EAAA,CAAQ,EAAA,EAAI,OAAA,EAAA,EAAW,IAAA;AAC3C,UAAA,OAAA,CAAQ,IAAA,CAAK;AAAA,YACX,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,cAAA,EAAgB,IAAA,EAAM,CAAA,GAAA;AAAA,YAC7C;AAAA,UACF,CAAC,CAAA;AACD,UAAA,QAAA,EAAU,CAAC,CAAA;AACX,UAAA,OAAA,CAAQ,IAAA,CAAK;AAAA,YACX,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA,CAAE,CAAC,EAAA,EAAI,cAAA,EAAgB,CAAA,IAAA,EAAO,GAAA;AAAA,YAC9C;AAAA,UACF,CAAC,CAAA;AACD,UAAA,OAAA,CAAQ,IAAA,CAAK,OAAO,CAAA;AAAA,QACtB,EAAA,KAAO;AACL,UAAA,QAAA,EAAU,CAAC,CAAA;AACX,UAAA,OAAA,CAAQ,IAAA,CAAK,OAAO,CAAA;AAAA,QACtB;AACA,QAAA,OAAA,CAAQ,IAAA,CAAK,CAAC,IAAA,EAAM,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AAAA,MACvC,EAAA,KAAO;AACL,QAAA,OAAA,CAAQ,IAAA,CAAK,CAAC,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAA,EAAG,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AAAA,MACnD;AAAA,IACF;AAAA,EACF,EAAA,KAAO;AAEL,IAAA,IAAI,SAAA,EAAW,CAAC,CAAA;AAChB,IAAA,OAAA,CAAQ,IAAA,CAAK,QAAQ,CAAA;AACrB,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,MAAA,EAAQ,EAAE,CAAA,EAAG;AAC1C,MAAA,QAAA,CAAS,IAAA,CAAK,CAAC,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAA,EAAG,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AAAA,IACpD;AAAA,EACF;AAEA,EAAA,IAAI,IAAA,EAAM,IAAI,GAAA,CAAI,IAAA,CAAK,UAAU,CAAA;AACjC,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,OAAA,CAAQ,MAAA,EAAQ,EAAE,CAAA,EAAG;AACvC,IAAA,IAAI,KAAA,EAAO,IAAI,UAAA,CAAW,CAAA;AAC1B,IAAA,GAAA,CAAI,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;AACxB,IAAA,IAAI,OAAA,EAAS,OAAA,CAAQ,CAAC,CAAA;AACtB,IAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,MAAA,CAAO,MAAA,EAAQ,EAAE,EAAA,EAAI;AACzC,MAAA,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,EAAE,CAAC,CAAA;AAAA,IACzB;AAAA,EACF;AACA,EAAA,OAAO,GAAA;AACT,CAAA;AF3FA;AACA;AC5LA,SAAS,WAAA,CAAY,KAAA,EAAO,GAAA,EAAK,OAAA,EAAS;AAExC,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,OAAO,QAAA,IAAY,QAAA,EAAU,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACrE,EAAA,IAAI,WAAA,EAAa,OAAA,CAAQ,UAAA;AACzB,EAAA,IAAI,QAAA,EAAU,OAAA,CAAQ,OAAA;AACtB,EAAA,IAAI,OAAA,EAAS,OAAA,CAAQ,MAAA;AAErB,EAAA,MAAA,EAAQ,iCAAA,KAAc,CAAA;AACtB,EAAA,IAAA,EAAM,iCAAA,GAAY,CAAA;AAElB,EAAA,WAAA,EAAa,WAAA,GAAc,CAAC,CAAA;AAC5B,EAAA,QAAA,EAAU,QAAA,GAAW,GAAA;AAErB,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,IAAM,GAAA,CAAI,CAAC,EAAA,GAAK,KAAA,CAAM,CAAC,EAAA,IAAM,GAAA,CAAI,CAAC,CAAA,EAAG;AAC9C,IAAA,MAAM,IAAA,EAAM,KAAA,CAAM,OAAO,CAAA;AACzB,IAAA,GAAA,CAAI,IAAA,CAAK,CAAC,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAC,CAAC,CAAA;AAC7B,IAAA,OAAO,iCAAA,GAAW,EAAK,UAAU,CAAA;AAAA,EACnC;AAEA,EAAA,OAAA,EAAS,OAAA,GAAU,EAAA;AAEnB,EAAA,IAAI,UAAA,EAAY,IAAI,WAAA;AAAA,IAClB,EAAE,CAAA,EAAG,KAAA,CAAM,CAAC,CAAA,EAAG,CAAA,EAAG,KAAA,CAAM,CAAC,EAAE,CAAA;AAAA,IAC3B,EAAE,CAAA,EAAG,GAAA,CAAI,CAAC,CAAA,EAAG,CAAA,EAAG,GAAA,CAAI,CAAC,EAAE,CAAA;AAAA,IACvB;AAAA,EACF,CAAA;AAEA,EAAA,IAAI,KAAA,EAAO,SAAA,CAAU,GAAA,CAAI,OAAA,EAAS,EAAE,OAAe,CAAC,CAAA;AAEpD,EAAA,OAAO,IAAA,CAAK,IAAA,CAAK,CAAA;AACnB;AAGA,IAAO,0BAAA,EAAQ,WAAA;ADoLf;AACE;AACA;AACF,+EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "/home/<USER>/work/turf/turf/packages/turf-great-circle/dist/cjs/index.cjs", "sourcesContent": [null, "import { lineString } from \"@turf/helpers\";\nimport { getCoord } from \"@turf/invariant\";\nimport { GreatCircle } from \"./lib/arc.js\";\n\n/**\n * Calculate great circles routes as {@link LineString} or {@link MultiLineString}.\n * If the `start` and `end` points span the antimeridian, the resulting feature will\n * be split into a `MultiLineString`. If the `start` and `end` positions are the same\n * then a `LineString` will be returned with duplicate coordinates the length of the `npoints` option.\n *\n * @function\n * @param {Coord} start source point feature\n * @param {Coord} end destination point feature\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] line feature properties\n * @param {number} [options.npoints=100] number of points\n * @param {number} [options.offset=10] offset controls the likelyhood that lines will\n * be split which cross the dateline. The higher the number the more likely.\n * @returns {Feature<LineString | MultiLineString>} great circle line feature\n * @example\n * var start = turf.point([-122, 48]);\n * var end = turf.point([-77, 39]);\n *\n * var greatCircle = turf.greatCircle(start, end, {properties: {name: 'Seattle to DC'}});\n *\n * //addToMap\n * var addToMap = [start, end, greatCircle]\n */\nfunction greatCircle(start, end, options) {\n  // Optional parameters\n  options = options || {};\n  if (typeof options !== \"object\") throw new Error(\"options is invalid\");\n  var properties = options.properties;\n  var npoints = options.npoints;\n  var offset = options.offset;\n\n  start = getCoord(start);\n  end = getCoord(end);\n\n  properties = properties || {};\n  npoints = npoints || 100;\n\n  if (start[0] === end[0] && start[1] === end[1]) {\n    const arr = Array(npoints);\n    arr.fill([start[0], start[1]]);\n    return lineString(arr, properties);\n  }\n\n  offset = offset || 10;\n\n  var generator = new GreatCircle(\n    { x: start[0], y: start[1] },\n    { x: end[0], y: end[1] },\n    properties\n  );\n\n  var line = generator.Arc(npoints, { offset: offset });\n\n  return line.json();\n}\n\nexport { greatCircle };\nexport default greatCircle;\n", "/*!\n * Copyright (c) 2019, <PERSON>\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *     * Redistributions of source code must retain the above copyright\n *       notice, this list of conditions and the following disclaimer.\n *     * Redistributions in binary form must reproduce the above copyright\n *       notice, this list of conditions and the following disclaimer in\n *       the documentation and/or other materials provided with the\n *       distribution.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS\n * IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,\n * THE IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON>ABILITY AND FITNESS FOR A PARTICULAR\n * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR\n * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\n * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\n * PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\n * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\nvar D2R = Math.PI / 180;\nvar R2D = 180 / Math.PI;\n\nvar Coord = function (lon, lat) {\n  this.lon = lon;\n  this.lat = lat;\n  this.x = D2R * lon;\n  this.y = D2R * lat;\n};\n\nCoord.prototype.view = function () {\n  return String(this.lon).slice(0, 4) + \",\" + String(this.lat).slice(0, 4);\n};\n\nCoord.prototype.antipode = function () {\n  var anti_lat = -1 * this.lat;\n  var anti_lon = this.lon < 0 ? 180 + this.lon : (180 - this.lon) * -1;\n  return new Coord(anti_lon, anti_lat);\n};\n\nvar LineString = function () {\n  this.coords = [];\n  this.length = 0;\n};\n\nLineString.prototype.move_to = function (coord) {\n  this.length++;\n  this.coords.push(coord);\n};\n\nvar Arc = function (properties) {\n  this.properties = properties || {};\n  this.geometries = [];\n};\n\nArc.prototype.json = function () {\n  if (this.geometries.length <= 0) {\n    return {\n      geometry: { type: \"LineString\", coordinates: null },\n      type: \"Feature\",\n      properties: this.properties,\n    };\n  } else if (this.geometries.length === 1) {\n    return {\n      geometry: { type: \"LineString\", coordinates: this.geometries[0].coords },\n      type: \"Feature\",\n      properties: this.properties,\n    };\n  } else {\n    var multiline = [];\n    for (var i = 0; i < this.geometries.length; i++) {\n      multiline.push(this.geometries[i].coords);\n    }\n    return {\n      geometry: { type: \"MultiLineString\", coordinates: multiline },\n      type: \"Feature\",\n      properties: this.properties,\n    };\n  }\n};\n\n// TODO - output proper multilinestring\nArc.prototype.wkt = function () {\n  var wkt_string = \"\";\n  var wkt = \"LINESTRING(\";\n  var collect = function (c) {\n    wkt += c[0] + \" \" + c[1] + \",\";\n  };\n  for (var i = 0; i < this.geometries.length; i++) {\n    if (this.geometries[i].coords.length === 0) {\n      return \"LINESTRING(empty)\";\n    } else {\n      var coords = this.geometries[i].coords;\n      coords.forEach(collect);\n      wkt_string += wkt.substring(0, wkt.length - 1) + \")\";\n    }\n  }\n  return wkt_string;\n};\n\n/*\n * http://en.wikipedia.org/wiki/Great-circle_distance\n *\n */\nvar GreatCircle = function (start, end, properties) {\n  if (!start || start.x === undefined || start.y === undefined) {\n    throw new Error(\n      \"GreatCircle constructor expects two args: start and end objects with x and y properties\"\n    );\n  }\n  if (!end || end.x === undefined || end.y === undefined) {\n    throw new Error(\n      \"GreatCircle constructor expects two args: start and end objects with x and y properties\"\n    );\n  }\n  this.start = new Coord(start.x, start.y);\n  this.end = new Coord(end.x, end.y);\n  this.properties = properties || {};\n\n  var w = this.start.x - this.end.x;\n  var h = this.start.y - this.end.y;\n  var z =\n    Math.pow(Math.sin(h / 2.0), 2) +\n    Math.cos(this.start.y) *\n      Math.cos(this.end.y) *\n      Math.pow(Math.sin(w / 2.0), 2);\n  this.g = 2.0 * Math.asin(Math.sqrt(z));\n\n  if (this.g === Math.PI) {\n    throw new Error(\n      \"it appears \" +\n        start.view() +\n        \" and \" +\n        end.view() +\n        \" are 'antipodal', e.g diametrically opposite, thus there is no single route but rather infinite\"\n    );\n  } else if (isNaN(this.g)) {\n    throw new Error(\n      \"could not calculate great circle between \" + start + \" and \" + end\n    );\n  }\n};\n\n/*\n * http://williams.best.vwh.net/avform.htm#Intermediate\n */\nGreatCircle.prototype.interpolate = function (f) {\n  var A = Math.sin((1 - f) * this.g) / Math.sin(this.g);\n  var B = Math.sin(f * this.g) / Math.sin(this.g);\n  var x =\n    A * Math.cos(this.start.y) * Math.cos(this.start.x) +\n    B * Math.cos(this.end.y) * Math.cos(this.end.x);\n  var y =\n    A * Math.cos(this.start.y) * Math.sin(this.start.x) +\n    B * Math.cos(this.end.y) * Math.sin(this.end.x);\n  var z = A * Math.sin(this.start.y) + B * Math.sin(this.end.y);\n  var lat = R2D * Math.atan2(z, Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2)));\n  var lon = R2D * Math.atan2(y, x);\n  return [lon, lat];\n};\n\n/*\n * Generate points along the great circle\n */\nGreatCircle.prototype.Arc = function (npoints, options) {\n  var first_pass = [];\n  if (!npoints || npoints <= 2) {\n    first_pass.push([this.start.lon, this.start.lat]);\n    first_pass.push([this.end.lon, this.end.lat]);\n  } else {\n    var delta = 1.0 / (npoints - 1);\n    for (var i = 0; i < npoints; ++i) {\n      var step = delta * i;\n      var pair = this.interpolate(step);\n      first_pass.push(pair);\n    }\n  }\n  /* partial port of dateline handling from:\n      gdal/ogr/ogrgeometryfactory.cpp\n\n      TODO - does not handle all wrapping scenarios yet\n    */\n  var bHasBigDiff = false;\n  var dfMaxSmallDiffLong = 0;\n  // from http://www.gdal.org/ogr2ogr.html\n  // -datelineoffset:\n  // (starting with GDAL 1.10) offset from dateline in degrees (default long. = +/- 10deg, geometries within 170deg to -170deg will be splited)\n  var dfDateLineOffset = options && options.offset ? options.offset : 10;\n  var dfLeftBorderX = 180 - dfDateLineOffset;\n  var dfRightBorderX = -180 + dfDateLineOffset;\n  var dfDiffSpace = 360 - dfDateLineOffset;\n\n  // https://github.com/OSGeo/gdal/blob/7bfb9c452a59aac958bff0c8386b891edf8154ca/gdal/ogr/ogrgeometryfactory.cpp#L2342\n  for (var j = 1; j < first_pass.length; ++j) {\n    var dfPrevX = first_pass[j - 1][0];\n    var dfX = first_pass[j][0];\n    var dfDiffLong = Math.abs(dfX - dfPrevX);\n    if (\n      dfDiffLong > dfDiffSpace &&\n      ((dfX > dfLeftBorderX && dfPrevX < dfRightBorderX) ||\n        (dfPrevX > dfLeftBorderX && dfX < dfRightBorderX))\n    ) {\n      bHasBigDiff = true;\n    } else if (dfDiffLong > dfMaxSmallDiffLong) {\n      dfMaxSmallDiffLong = dfDiffLong;\n    }\n  }\n\n  var poMulti = [];\n  if (bHasBigDiff && dfMaxSmallDiffLong < dfDateLineOffset) {\n    var poNewLS = [];\n    poMulti.push(poNewLS);\n    for (var k = 0; k < first_pass.length; ++k) {\n      var dfX0 = parseFloat(first_pass[k][0]);\n      if (k > 0 && Math.abs(dfX0 - first_pass[k - 1][0]) > dfDiffSpace) {\n        var dfX1 = parseFloat(first_pass[k - 1][0]);\n        var dfY1 = parseFloat(first_pass[k - 1][1]);\n        var dfX2 = parseFloat(first_pass[k][0]);\n        var dfY2 = parseFloat(first_pass[k][1]);\n        if (\n          dfX1 > -180 &&\n          dfX1 < dfRightBorderX &&\n          dfX2 === 180 &&\n          k + 1 < first_pass.length &&\n          first_pass[k - 1][0] > -180 &&\n          first_pass[k - 1][0] < dfRightBorderX\n        ) {\n          poNewLS.push([-180, first_pass[k][1]]);\n          k++;\n          poNewLS.push([first_pass[k][0], first_pass[k][1]]);\n          continue;\n        } else if (\n          dfX1 > dfLeftBorderX &&\n          dfX1 < 180 &&\n          dfX2 === -180 &&\n          k + 1 < first_pass.length &&\n          first_pass[k - 1][0] > dfLeftBorderX &&\n          first_pass[k - 1][0] < 180\n        ) {\n          poNewLS.push([180, first_pass[k][1]]);\n          k++;\n          poNewLS.push([first_pass[k][0], first_pass[k][1]]);\n          continue;\n        }\n\n        if (dfX1 < dfRightBorderX && dfX2 > dfLeftBorderX) {\n          // swap dfX1, dfX2\n          var tmpX = dfX1;\n          dfX1 = dfX2;\n          dfX2 = tmpX;\n          // swap dfY1, dfY2\n          var tmpY = dfY1;\n          dfY1 = dfY2;\n          dfY2 = tmpY;\n        }\n        if (dfX1 > dfLeftBorderX && dfX2 < dfRightBorderX) {\n          dfX2 += 360;\n        }\n\n        if (dfX1 <= 180 && dfX2 >= 180 && dfX1 < dfX2) {\n          var dfRatio = (180 - dfX1) / (dfX2 - dfX1);\n          var dfY = dfRatio * dfY2 + (1 - dfRatio) * dfY1;\n          poNewLS.push([\n            first_pass[k - 1][0] > dfLeftBorderX ? 180 : -180,\n            dfY,\n          ]);\n          poNewLS = [];\n          poNewLS.push([\n            first_pass[k - 1][0] > dfLeftBorderX ? -180 : 180,\n            dfY,\n          ]);\n          poMulti.push(poNewLS);\n        } else {\n          poNewLS = [];\n          poMulti.push(poNewLS);\n        }\n        poNewLS.push([dfX0, first_pass[k][1]]);\n      } else {\n        poNewLS.push([first_pass[k][0], first_pass[k][1]]);\n      }\n    }\n  } else {\n    // add normally\n    var poNewLS0 = [];\n    poMulti.push(poNewLS0);\n    for (var l = 0; l < first_pass.length; ++l) {\n      poNewLS0.push([first_pass[l][0], first_pass[l][1]]);\n    }\n  }\n\n  var arc = new Arc(this.properties);\n  for (var m = 0; m < poMulti.length; ++m) {\n    var line = new LineString();\n    arc.geometries.push(line);\n    var points = poMulti[m];\n    for (var j0 = 0; j0 < points.length; ++j0) {\n      line.move_to(points[j0]);\n    }\n  }\n  return arc;\n};\n\nexport { Coord, Arc, GreatCircle };\n\nexport default {\n  Coord,\n  Arc,\n  GreatCircle,\n};\n"]}