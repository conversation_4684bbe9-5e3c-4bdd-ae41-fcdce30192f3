{"version": 3, "sources": ["../../index.js"], "sourcesContent": ["import { bearing } from \"@turf/bearing\";\nimport { distance } from \"@turf/distance\";\nimport { destination } from \"@turf/destination\";\nimport { lineString, isObject } from \"@turf/helpers\";\n\n/**\n * Takes a {@link LineString|line}, a specified distance along the line to a start {@link Point},\n * and a specified  distance along the line to a stop point\n * and returns a subsection of the line in-between those points.\n *\n * This can be useful for extracting only the part of a route between two distances.\n *\n * @function\n * @param {Feature<LineString>|LineString} line input line\n * @param {number} startDist distance along the line to starting point\n * @param {number} stopDist distance along the line to ending point\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] can be degrees, radians, miles, or kilometers\n * @returns {Feature<LineString>} sliced line\n * @example\n * var line = turf.lineString([[7, 45], [9, 45], [14, 40], [14, 41]]);\n * var start = 12.5;\n * var stop = 25;\n * var sliced = turf.lineSliceAlong(line, start, stop, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [line, start, stop, sliced]\n */\nfunction lineSliceAlong(line, startDist, stopDist, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n\n  var coords;\n  var slice = [];\n\n  // Validation\n  if (line.type === \"Feature\") coords = line.geometry.coordinates;\n  else if (line.type === \"LineString\") coords = line.coordinates;\n  else throw new Error(\"input must be a LineString Feature or Geometry\");\n  var origCoordsLength = coords.length;\n  var travelled = 0;\n  var overshot, direction, interpolated;\n  for (var i = 0; i < coords.length; i++) {\n    if (startDist >= travelled && i === coords.length - 1) break;\n    else if (travelled > startDist && slice.length === 0) {\n      overshot = startDist - travelled;\n      if (!overshot) {\n        slice.push(coords[i]);\n        return lineString(slice);\n      }\n      direction = bearing(coords[i], coords[i - 1]) - 180;\n      interpolated = destination(coords[i], overshot, direction, options);\n      slice.push(interpolated.geometry.coordinates);\n    }\n\n    if (travelled >= stopDist) {\n      overshot = stopDist - travelled;\n      if (!overshot) {\n        slice.push(coords[i]);\n        return lineString(slice);\n      }\n      direction = bearing(coords[i], coords[i - 1]) - 180;\n      interpolated = destination(coords[i], overshot, direction, options);\n      slice.push(interpolated.geometry.coordinates);\n      return lineString(slice);\n    }\n\n    if (travelled >= startDist) {\n      slice.push(coords[i]);\n    }\n\n    if (i === coords.length - 1) {\n      return lineString(slice);\n    }\n\n    travelled += distance(coords[i], coords[i + 1], options);\n  }\n\n  if (travelled < startDist && coords.length === origCoordsLength)\n    throw new Error(\"Start position is beyond line\");\n\n  var last = coords[coords.length - 1];\n  return lineString([last, last]);\n}\n\nexport { lineSliceAlong };\nexport default lineSliceAlong;\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,gBAAgB;AACzB,SAAS,mBAAmB;AAC5B,SAAS,YAAY,gBAAgB;AAyBrC,SAAS,eAAe,MAAM,WAAW,UAAU,SAAS;AAE1D,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAE5D,MAAI;AACJ,MAAI,QAAQ,CAAC;AAGb,MAAI,KAAK,SAAS,UAAW,UAAS,KAAK,SAAS;AAAA,WAC3C,KAAK,SAAS,aAAc,UAAS,KAAK;AAAA,MAC9C,OAAM,IAAI,MAAM,gDAAgD;AACrE,MAAI,mBAAmB,OAAO;AAC9B,MAAI,YAAY;AAChB,MAAI,UAAU,WAAW;AACzB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,aAAa,aAAa,MAAM,OAAO,SAAS,EAAG;AAAA,aAC9C,YAAY,aAAa,MAAM,WAAW,GAAG;AACpD,iBAAW,YAAY;AACvB,UAAI,CAAC,UAAU;AACb,cAAM,KAAK,OAAO,CAAC,CAAC;AACpB,eAAO,WAAW,KAAK;AAAA,MACzB;AACA,kBAAY,QAAQ,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,IAAI;AAChD,qBAAe,YAAY,OAAO,CAAC,GAAG,UAAU,WAAW,OAAO;AAClE,YAAM,KAAK,aAAa,SAAS,WAAW;AAAA,IAC9C;AAEA,QAAI,aAAa,UAAU;AACzB,iBAAW,WAAW;AACtB,UAAI,CAAC,UAAU;AACb,cAAM,KAAK,OAAO,CAAC,CAAC;AACpB,eAAO,WAAW,KAAK;AAAA,MACzB;AACA,kBAAY,QAAQ,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,IAAI;AAChD,qBAAe,YAAY,OAAO,CAAC,GAAG,UAAU,WAAW,OAAO;AAClE,YAAM,KAAK,aAAa,SAAS,WAAW;AAC5C,aAAO,WAAW,KAAK;AAAA,IACzB;AAEA,QAAI,aAAa,WAAW;AAC1B,YAAM,KAAK,OAAO,CAAC,CAAC;AAAA,IACtB;AAEA,QAAI,MAAM,OAAO,SAAS,GAAG;AAC3B,aAAO,WAAW,KAAK;AAAA,IACzB;AAEA,iBAAa,SAAS,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO;AAAA,EACzD;AAEA,MAAI,YAAY,aAAa,OAAO,WAAW;AAC7C,UAAM,IAAI,MAAM,+BAA+B;AAEjD,MAAI,OAAO,OAAO,OAAO,SAAS,CAAC;AACnC,SAAO,WAAW,CAAC,MAAM,IAAI,CAAC;AAChC;AAGA,IAAO,gCAAQ;", "names": []}