{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-intersect/dist/cjs/index.cjs", "../../index.ts", "../../lib/sweepline-intersections-export.ts"], "names": [], "mappings": "AAAA;ACAA,wCAAkD;ADElD;AACA;AECA,iJAAgB;AAET,IAAM,uBAAA,EAAyB,gCAAA;AFAtC;AACA;ACwBA,SAAS,aAAA,CAIP,KAAA,EACA,KAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACqB;AAC1B,EAAA,MAAM,EAAE,iBAAA,EAAmB,IAAA,EAAM,wBAAA,EAA0B,KAAK,EAAA,EAAI,OAAA;AACpE,EAAA,IAAI,SAAA,EAA+B,CAAC,CAAA;AACpC,EAAA,GAAA,CAAI,KAAA,CAAM,KAAA,IAAS,mBAAA;AACjB,IAAA,SAAA,EAAW,QAAA,CAAS,MAAA,CAAO,KAAA,CAAM,QAAQ,CAAA;AAAA,EAAA,KAAA,GAAA,CAClC,KAAA,CAAM,KAAA,IAAS,SAAA,EAAW,QAAA,CAAS,IAAA,CAAK,KAAK,CAAA;AAAA,EAAA,KAAA,GAAA,CAEpD,KAAA,CAAM,KAAA,IAAS,aAAA,GACf,KAAA,CAAM,KAAA,IAAS,UAAA,GACf,KAAA,CAAM,KAAA,IAAS,kBAAA,GACf,KAAA,CAAM,KAAA,IAAS,cAAA,EACf;AACA,IAAA,QAAA,CAAS,IAAA,CAAK,8BAAA,KAAa,CAAC,CAAA;AAAA,EAC9B;AAEA,EAAA,GAAA,CAAI,KAAA,CAAM,KAAA,IAAS,mBAAA;AACjB,IAAA,SAAA,EAAW,QAAA,CAAS,MAAA,CAAO,KAAA,CAAM,QAAQ,CAAA;AAAA,EAAA,KAAA,GAAA,CAClC,KAAA,CAAM,KAAA,IAAS,SAAA,EAAW,QAAA,CAAS,IAAA,CAAK,KAAK,CAAA;AAAA,EAAA,KAAA,GAAA,CAEpD,KAAA,CAAM,KAAA,IAAS,aAAA,GACf,KAAA,CAAM,KAAA,IAAS,UAAA,GACf,KAAA,CAAM,KAAA,IAAS,kBAAA,GACf,KAAA,CAAM,KAAA,IAAS,cAAA,EACf;AACA,IAAA,QAAA,CAAS,IAAA,CAAK,8BAAA,KAAa,CAAC,CAAA;AAAA,EAC9B;AAEA,EAAA,MAAM,cAAA,EAAgB,sBAAA;AAAA,IACpB,wCAAA,QAA0B,CAAA;AAAA,IAC1B;AAAA,EACF,CAAA;AAEA,EAAA,IAAI,QAAA,EAA0B,CAAC,CAAA;AAC/B,EAAA,GAAA,CAAI,gBAAA,EAAkB;AACpB,IAAA,MAAM,OAAA,EAAkC,CAAC,CAAA;AACzC,IAAA,aAAA,CAAc,OAAA,CAAQ,CAAC,YAAA,EAAA,GAAiB;AACtC,MAAA,MAAM,IAAA,EAAM,YAAA,CAAa,IAAA,CAAK,GAAG,CAAA;AACjC,MAAA,GAAA,CAAI,CAAC,MAAA,CAAO,GAAG,CAAA,EAAG;AAChB,QAAA,MAAA,CAAO,GAAG,EAAA,EAAI,IAAA;AACd,QAAA,OAAA,CAAQ,IAAA,CAAK,YAAY,CAAA;AAAA,MAC3B;AAAA,IACF,CAAC,CAAA;AAAA,EACH,EAAA,KAAO;AACL,IAAA,QAAA,EAAU,aAAA;AAAA,EACZ;AACA,EAAA,OAAO,wCAAA,OAAkB,CAAQ,GAAA,CAAI,CAAC,CAAA,EAAA,GAAM,4BAAA,CAAO,CAAC,CAAC,CAAA;AACvD;AAGA,IAAO,4BAAA,EAAQ,aAAA;AD/Cf;AACE;AACA;AACF,qFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-intersect/dist/cjs/index.cjs", "sourcesContent": [null, "import { feature, featureCollection, point } from \"@turf/helpers\";\nimport {\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport type { Intersection } from \"sweepline-intersections\";\nimport { sweeplineIntersections as findIntersections } from \"./lib/sweepline-intersections-export.js\";\n\n/**\n * Takes any LineString or Polygon GeoJSON and returns the intersecting point(s).\n *\n * @function\n * @param {GeoJSON} line1 any LineString or Polygon\n * @param {GeoJSON} line2 any LineString or Polygon\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.removeDuplicates=true] remove duplicate intersections\n * @param {boolean} [options.ignoreSelfIntersections=true] ignores self-intersections on input features\n * @returns {FeatureCollection<Point>} point(s) that intersect both\n * @example\n * var line1 = turf.lineString([[126, -11], [129, -21]]);\n * var line2 = turf.lineString([[123, -18], [131, -14]]);\n * var intersects = turf.lineIntersect(line1, line2);\n *\n * //addToMap\n * var addToMap = [line1, line2, intersects]\n */\nfunction lineIntersect<\n  G1 extends LineString | MultiLineString | Polygon | MultiPolygon,\n  G2 extends LineString | MultiLineString | Polygon | MultiPolygon,\n>(\n  line1: FeatureCollection<G1> | Feature<G1> | G1,\n  line2: FeatureCollection<G2> | Feature<G2> | G2,\n  options: {\n    removeDuplicates?: boolean;\n    ignoreSelfIntersections?: boolean;\n  } = {}\n): FeatureCollection<Point> {\n  const { removeDuplicates = true, ignoreSelfIntersections = true } = options;\n  let features: Feature<G1 | G2>[] = [];\n  if (line1.type === \"FeatureCollection\")\n    features = features.concat(line1.features);\n  else if (line1.type === \"Feature\") features.push(line1);\n  else if (\n    line1.type === \"LineString\" ||\n    line1.type === \"Polygon\" ||\n    line1.type === \"MultiLineString\" ||\n    line1.type === \"MultiPolygon\"\n  ) {\n    features.push(feature(line1));\n  }\n\n  if (line2.type === \"FeatureCollection\")\n    features = features.concat(line2.features);\n  else if (line2.type === \"Feature\") features.push(line2);\n  else if (\n    line2.type === \"LineString\" ||\n    line2.type === \"Polygon\" ||\n    line2.type === \"MultiLineString\" ||\n    line2.type === \"MultiPolygon\"\n  ) {\n    features.push(feature(line2));\n  }\n\n  const intersections = findIntersections(\n    featureCollection(features),\n    ignoreSelfIntersections\n  );\n\n  let results: Intersection[] = [];\n  if (removeDuplicates) {\n    const unique: Record<string, boolean> = {};\n    intersections.forEach((intersection) => {\n      const key = intersection.join(\",\");\n      if (!unique[key]) {\n        unique[key] = true;\n        results.push(intersection);\n      }\n    });\n  } else {\n    results = intersections;\n  }\n  return featureCollection(results.map((r) => point(r)));\n}\n\nexport { lineIntersect };\nexport default lineIntersect;\n", "// Get around problems with moduleResolution node16 and some older libraries.\n// Manifests as \"This expression is not callable ... has no call signatures\"\n// https://stackoverflow.com/a/74709714\n\nimport lib from \"sweepline-intersections\";\n\nexport const sweeplineIntersections = lib as unknown as typeof lib.default;\n"]}