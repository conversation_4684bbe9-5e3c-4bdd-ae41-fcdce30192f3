import { Feature, LineString, Point } from 'geojson';
import { Units } from '@turf/helpers';

/**
 * Takes a {@link LineString} and returns a {@link Point} at a specified distance along the line.
 *
 * @function
 * @param {Feature<LineString>|LineString} line input line
 * @param {number} distance distance along the line
 * @param {Object} [options] Optional parameters
 * @param {Units} [options.units="kilometers"] can be degrees, radians, miles, or kilometers
 * @returns {Feature<Point>} Point `distance` `units` along the line
 * @example
 * var line = turf.lineString([[-83, 30], [-84, 36], [-78, 41]]);
 * var options = {units: 'miles'};
 *
 * var along = turf.along(line, 200, options);
 *
 * //addToMap
 * var addToMap = [along, line]
 */
declare function along(line: Feature<LineString> | LineString, distance: number, options?: {
    units?: Units;
}): Feature<Point>;

export { along, along as default };
