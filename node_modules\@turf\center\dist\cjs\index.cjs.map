{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-center/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,kCAAqB;AACrB,wCAAsC;AA0BtC,SAAS,MAAA,CACP,OAAA,EACA,QAAA,EAAoD,CAAC,CAAA,EAClC;AACnB,EAAA,MAAM,IAAA,EAAM,wBAAA,OAAY,CAAA;AACxB,EAAA,MAAM,EAAA,EAAA,CAAK,GAAA,CAAI,CAAC,EAAA,EAAI,GAAA,CAAI,CAAC,CAAA,EAAA,EAAK,CAAA;AAC9B,EAAA,MAAM,EAAA,EAAA,CAAK,GAAA,CAAI,CAAC,EAAA,EAAI,GAAA,CAAI,CAAC,CAAA,EAAA,EAAK,CAAA;AAC9B,EAAA,OAAO,4BAAA,CAAO,CAAA,EAAG,CAAC,CAAA,EAAG,OAAA,CAAQ,UAAA,EAAY,OAAO,CAAA;AAClD;AAGA,IAAO,oBAAA,EAAQ,MAAA;AD7Bf;AACE;AACA;AACF,+DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-center/dist/cjs/index.cjs", "sourcesContent": [null, "import { BBox, Feature, GeoJsonProperties, Point } from \"geojson\";\nimport { bbox } from \"@turf/bbox\";\nimport { point, Id, AllGeoJSON } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Feature} or {@link FeatureCollection} and returns the absolute center point of all features.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] Translate GeoJSON Properties to Point\n * @param {Object} [options.bbox={}] Translate GeoJSON BBox to Point\n * @param {Object} [options.id={}] Translate GeoJSON Id to Point\n * @returns {Feature<Point>} a Point feature at the absolute center point of all input features\n * @example\n * var features = turf.points([\n *   [-97.522259, 35.4691],\n *   [-97.502754, 35.463455],\n *   [-97.508269, 35.463245]\n * ]);\n *\n * var center = turf.center(features);\n *\n * //addToMap\n * var addToMap = [features, center]\n * center.properties['marker-size'] = 'large';\n * center.properties['marker-color'] = '#000';\n */\nfunction center<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: AllGeoJSON,\n  options: { properties?: P; bbox?: BBox; id?: Id } = {}\n): Feature<Point, P> {\n  const ext = bbox(geojson);\n  const x = (ext[0] + ext[2]) / 2;\n  const y = (ext[1] + ext[3]) / 2;\n  return point([x, y], options.properties, options);\n}\n\nexport { center };\nexport default center;\n"]}