{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-touches/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,iEAAmC;AACnC,uEAAsC;AACtC,4CAAwB;AAiBxB,SAAS,cAAA,CACP,QAAA,EACA,QAAA,EACS;AACT,EAAA,IAAI,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC5B,EAAA,IAAI,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC5B,EAAA,IAAI,MAAA,EAAQ,KAAA,CAAM,IAAA;AAClB,EAAA,IAAI,MAAA,EAAQ,KAAA,CAAM,IAAA;AAElB,EAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,IACb,KAAK,OAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,OAAO,gBAAA,CAAiB,KAAA,EAAO,KAAK,CAAA;AAAA,QACtC,KAAK,iBAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,YAAA,GAAA,CACE,gBAAA,CAAiB,KAAA,EAAO;AAAA,cACtB,IAAA,EAAM,YAAA;AAAA,cACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE;AAAA,YACnC,CAAC,CAAA;AAED,cAAA,mBAAA,EAAqB,IAAA;AAAA,UACzB;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,SAAA;AACH,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CACE,oDAAA,KAAmB,EAAO;AAAA,cACxB,IAAA,EAAM,YAAA;AAAA,cACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC;AAAA,YAClC,CAAC,CAAA;AAED,cAAA,OAAO,IAAA;AAAA,UACX;AACA,UAAA,OAAO,KAAA;AAAA,QACT,KAAK,cAAA;AACH,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,EAAA,EAAA,EAAM;AACvD,cAAA,GAAA,CACE,oDAAA,KAAmB,EAAO;AAAA,gBACxB,IAAA,EAAM,YAAA;AAAA,gBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE;AAAA,cACtC,CAAC,CAAA;AAED,gBAAA,OAAO,IAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,KAAA;AAAA,QACT,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,cAAA,GAAA,CACE,gBAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD;AAAA,cACF,CAAA;AAEA,gBAAA,mBAAA,EAAqB,IAAA;AAAA,YACzB;AACA,YAAA,GAAA,CACE,oDAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,iBAAA,EAAmB,KAAK;AAAA,YAC5B,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,iBAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,gBAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,kBACnD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE;AAAA,gBAC3D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACzD,EAAE,iBAAA,EAAmB,KAAK;AAAA,cAC5B,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,SAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,cAC1D,CAAA;AAEA,gBAAA,mBAAA,EAAqB,IAAA;AAAA,YACzB;AACA,YAAA,GAAA,CACE,0DAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,cAAA,EAAgB,KAAK;AAAA,YACzB,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,cAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,kBACnD;AAAA,oBACE,IAAA,EAAM,YAAA;AAAA,oBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,CAAC;AAAA,kBACtC;AAAA,gBACF,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,0DAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACtD,EAAE,cAAA,EAAgB,KAAK;AAAA,cACzB,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,OAAO,gBAAA,CAAiB,KAAA,EAAO,KAAK,CAAA;AAAA,QACtC,KAAK,YAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,cAAA,GAAA,CACE,gBAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD;AAAA,cACF,CAAA;AAEA,gBAAA,mBAAA,EAAqB,IAAA;AAAA,YACzB;AACA,YAAA,GAAA,CACE,oDAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,iBAAA,EAAmB,KAAK;AAAA,YAC5B,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,YAAA;AACH,UAAA,IAAI,SAAA,EAAW,KAAA;AACf,UAAA,GAAA,CACE,gBAAA;AAAA,YACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,YACnD;AAAA,UACF,CAAA;AAEA,YAAA,SAAA,EAAW,IAAA;AACb,UAAA,GAAA,CACE,gBAAA;AAAA,YACE;AAAA,cACE,IAAA,EAAM,OAAA;AAAA,cACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,KAAA,CAAM,WAAA,CAAY,OAAA,EAAS,CAAC;AAAA,YAC7D,CAAA;AAAA,YACA;AAAA,UACF,CAAA;AAEA,YAAA,SAAA,EAAW,IAAA;AACb,UAAA,GAAA,CAAI,SAAA,IAAa,KAAA,EAAO,OAAO,KAAA;AAC/B,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CACE,oDAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,iBAAA,EAAmB,KAAK;AAAA,YAC5B,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,QAAA;AAAA,QACT,KAAK,iBAAA;AACH,UAAA,IAAI,SAAA,EAAW,KAAA;AACf,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CACE,gBAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,YAC1D,CAAA;AAEA,cAAA,SAAA,EAAW,IAAA;AACb,YAAA,GAAA,CACE,gBAAA;AAAA,cACE;AAAA,gBACE,IAAA,EAAM,OAAA;AAAA,gBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,KAAA,CAAM,WAAA,CAAY,OAAA,EAAS,CAAC;AAAA,cAC7D,CAAA;AAAA,cACA,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,YAC1D,CAAA;AAEA,cAAA,SAAA,EAAW,IAAA;AACb,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,EAAA,EAAA,EAAM;AACvD,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACpD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACxD,EAAE,iBAAA,EAAmB,KAAK;AAAA,cAC5B,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,QAAA;AAAA,QACT,KAAK,SAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,cAC1D,CAAA;AAEA,gBAAA,mBAAA,EAAqB,IAAA;AAAA,YACzB;AACA,YAAA,GAAA,CACE,0DAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,cAAA,EAAgB,KAAK;AAAA,YACzB,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,cAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,kBACnD;AAAA,oBACE,IAAA,EAAM,YAAA;AAAA,oBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,CAAC;AAAA,kBACtC;AAAA,gBACF,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AAAA,YACF;AACA,YAAA,GAAA,CACE,0DAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,cAAA,EAAgB,KAAK;AAAA,YACzB,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,iBAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CACE,gBAAA,CAAiB,KAAA,EAAO;AAAA,cACtB,IAAA,EAAM,YAAA;AAAA,cACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC;AAAA,YAClC,CAAC,CAAA;AAED,cAAA,OAAO,IAAA;AAAA,UACX;AACA,UAAA,OAAO,KAAA;AAAA,QACT,KAAK,YAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,gBAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,kBACpD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE;AAAA,gBAC3D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACpD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACzD,EAAE,iBAAA,EAAmB,KAAK;AAAA,cAC5B,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,YAAA;AACH,UAAA,IAAI,SAAA,EAAW,KAAA;AACf,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CACE,gBAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE,CAAA;AAAA,cACtD;AAAA,YACF,CAAA;AAEA,cAAA,SAAA,EAAW,IAAA;AACb,YAAA,GAAA,CACE,gBAAA;AAAA,cACE;AAAA,gBACE,IAAA,EAAM,OAAA;AAAA,gBACN,WAAA,EACE,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,OAAA,EAAS,CAAC;AAAA,cACxD,CAAA;AAAA,cACA;AAAA,YACF,CAAA;AAEA,cAAA,SAAA,EAAW,IAAA;AACb,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACpD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACxD,EAAE,iBAAA,EAAmB,KAAK;AAAA,cAC5B,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,QAAA;AAAA,QACT,KAAK,iBAAA;AACH,UAAA,IAAI,SAAA,EAAW,KAAA;AACf,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CACE,gBAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE,CAAA;AAAA,gBACtD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE;AAAA,cAC3D,CAAA;AAEA,gBAAA,SAAA,EAAW,IAAA;AACb,cAAA,GAAA,CACE,gBAAA;AAAA,gBACE;AAAA,kBACE,IAAA,EAAM,OAAA;AAAA,kBACN,WAAA,EACE,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,OAAA,EAAS,CAAC;AAAA,gBACxD,CAAA;AAAA,gBACA,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE;AAAA,cAC3D,CAAA;AAEA,gBAAA,SAAA,EAAW,IAAA;AACb,cAAA,IAAA,CAAA,IAAS,IAAA,EAAM,CAAA,EAAG,IAAA,EAAM,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,GAAA,EAAA,EAAO;AAC1D,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,GAAG,EAAE,CAAA;AAAA,kBACxD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,kBACzD,EAAE,iBAAA,EAAmB,KAAK;AAAA,gBAC5B,CAAA;AAEA,kBAAA,OAAO,KAAA;AAAA,cACX;AAAA,YACF;AAAA,UACF;AACA,UAAA,OAAO,QAAA;AAAA,QACT,KAAK,SAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,kBACvD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,gBAC1D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,0DAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,gBACvD,KAAA;AAAA,gBACA,EAAE,cAAA,EAAgB,KAAK;AAAA,cACzB,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,cAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,IAAA,CAAA,IAAS,IAAA,EAAM,CAAA,EAAG,IAAA,EAAM,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,MAAA,EAAQ,GAAA,EAAA,EAAO;AAC3D,gBAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,kBAAA,GAAA,CACE,oDAAA;AAAA,oBACE;AAAA,sBACE,IAAA,EAAM,OAAA;AAAA,sBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,GAAG;AAAA,oBACxC,CAAA;AAAA,oBACA;AAAA,sBACE,IAAA,EAAM,YAAA;AAAA,sBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC;AAAA,oBACrC;AAAA,kBACF,CAAA;AAEA,oBAAA,mBAAA,EAAqB,IAAA;AAAA,gBACzB;AACA,gBAAA,GAAA,CACE,0DAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,GAAG,EAAE,CAAA;AAAA,kBACzD,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,CAAC,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAC,EAAE,CAAA;AAAA,kBAC1D,EAAE,cAAA,EAAgB,KAAK;AAAA,gBACzB,CAAA;AAEA,kBAAA,OAAO,KAAA;AAAA,cACX;AAAA,YACF;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,SAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CACE,oDAAA,KAAmB,EAAO;AAAA,cACxB,IAAA,EAAM,YAAA;AAAA,cACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC;AAAA,YAClC,CAAC,CAAA;AAED,cAAA,OAAO,IAAA;AAAA,UACX;AACA,UAAA,OAAO,KAAA;AAAA,QACT,KAAK,YAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,cAC1D,CAAA;AAEA,gBAAA,mBAAA,EAAqB,IAAA;AAAA,YACzB;AACA,YAAA,GAAA,CACE,0DAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,cAAA,EAAgB,KAAK;AAAA,YACzB,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,YAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,gBACnD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,cAC1D,CAAA;AAEA,gBAAA,mBAAA,EAAqB,IAAA;AAAA,YACzB;AACA,YAAA,GAAA,CACE,0DAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE,CAAA;AAAA,cACnD,KAAA;AAAA,cACA,EAAE,cAAA,EAAgB,KAAK;AAAA,YACzB,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,iBAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,EAAA,EAAA,EAAM;AACvD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,kBACvD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,gBAC1D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,0DAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,gBACvD,KAAA;AAAA,gBACA,EAAE,cAAA,EAAgB,KAAK;AAAA,cACzB,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,SAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,cAAA,GAAA,CACE,oDAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE,CAAA;AAAA,gBACtD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,cAC1D,CAAA;AAEA,gBAAA,mBAAA,EAAqB,IAAA;AAAA,YACzB;AACA,YAAA,GAAA,CACE,0DAAA;AAAA,cACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE,CAAA;AAAA,cACtD,KAAA;AAAA,cACA,EAAE,cAAA,EAAgB,KAAK;AAAA,YACzB,CAAA;AAEA,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,cAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,EAAA,EAAA,EAAM;AACvD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,kBACvD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE;AAAA,gBAC7D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,0DAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,gBACvD,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE,CAAA;AAAA,gBACxD,EAAE,cAAA,EAAgB,KAAK;AAAA,cACzB,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,cAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,GAAA,CACE,oDAAA,KAAmB,EAAO;AAAA,cACxB,IAAA,EAAM,YAAA;AAAA,cACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC;AAAA,YACrC,CAAC,CAAA;AAED,cAAA,OAAO,IAAA;AAAA,UACX;AACA,UAAA,OAAO,KAAA;AAAA,QACT,KAAK,YAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,kBACpD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE;AAAA,gBAC7D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,0DAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACpD,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE,CAAA;AAAA,gBACxD,EAAE,cAAA,EAAgB,KAAK;AAAA,cACzB,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,YAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,kBACpD,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE;AAAA,gBAC7D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,0DAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,EAAE,CAAA;AAAA,gBACpD,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,EAAE,CAAA;AAAA,gBACxD,EAAE,cAAA,EAAgB,KAAK;AAAA,cACzB,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,iBAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AACpD,cAAA,IAAA,CAAA,IAAS,IAAA,EAAM,CAAA,EAAG,IAAA,EAAM,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,MAAA,EAAQ,GAAA,EAAA,EAAO;AAC3D,gBAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,kBAAA,GAAA,CACE,oDAAA;AAAA,oBACE;AAAA,sBACE,IAAA,EAAM,OAAA;AAAA,sBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,GAAG;AAAA,oBACxC,CAAA;AAAA,oBACA;AAAA,sBACE,IAAA,EAAM,YAAA;AAAA,sBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC;AAAA,oBACrC;AAAA,kBACF,CAAA;AAEA,oBAAA,mBAAA,EAAqB,IAAA;AAAA,gBACzB;AACA,gBAAA,GAAA,CACE,0DAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,EAAE,CAAA,CAAE,GAAG,EAAE,CAAA;AAAA,kBACzD,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,CAAC,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAC,EAAE,CAAA;AAAA,kBAC1D,EAAE,cAAA,EAAgB,KAAK;AAAA,gBACzB,CAAA;AAEA,kBAAA,OAAO,KAAA;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAEA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,SAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,MAAA,EAAQ,EAAA,EAAA,EAAM;AAC1D,cAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,gBAAA,GAAA,CACE,oDAAA;AAAA,kBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,kBAC1D,EAAE,IAAA,EAAM,YAAA,EAAc,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,EAAE;AAAA,gBAC1D,CAAA;AAEA,kBAAA,mBAAA,EAAqB,IAAA;AAAA,cACzB;AACA,cAAA,GAAA,CACE,0DAAA;AAAA,gBACE,EAAE,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,gBAC1D,KAAA;AAAA,gBACA,EAAE,cAAA,EAAgB,KAAK;AAAA,cACzB,CAAA;AAEA,gBAAA,OAAO,KAAA;AAAA,YACX;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,KAAK,cAAA;AACH,UAAA,IAAI,mBAAA,EAAqB,KAAA;AACzB,UAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpD,YAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,EAAA,EAAA,EAAM;AACvD,cAAA,IAAA,CAAA,IAAS,IAAA,EAAM,CAAA,EAAG,IAAA,EAAM,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,GAAA,EAAA,EAAO;AAC1D,gBAAA,GAAA,CAAI,CAAC,kBAAA,EAAoB;AACvB,kBAAA,GAAA,CACE,oDAAA;AAAA,oBACE;AAAA,sBACE,IAAA,EAAM,OAAA;AAAA,sBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,GAAG;AAAA,oBAC1C,CAAA;AAAA,oBACA;AAAA,sBACE,IAAA,EAAM,YAAA;AAAA,sBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE;AAAA,oBACtC;AAAA,kBACF,CAAA;AAEA,oBAAA,mBAAA,EAAqB,IAAA;AAAA,gBACzB;AACA,gBAAA,GAAA,CACE,0DAAA;AAAA,kBACE;AAAA,oBACE,IAAA,EAAM,OAAA;AAAA,oBACN,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,GAAG;AAAA,kBAC1C,CAAA;AAAA,kBACA,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,EAAE,EAAE,CAAA;AAAA,kBACzD,EAAE,cAAA,EAAgB,KAAK;AAAA,gBACzB,CAAA;AAEA,kBAAA,OAAO,KAAA;AAAA,cACX;AAAA,YACF;AAAA,UACF;AACA,UAAA,OAAO,kBAAA;AAAA,QACT,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,EACnE;AACF;AAEA,SAAS,gBAAA,CAAiB,KAAA,EAAc,IAAA,EAAkB;AACxD,EAAA,GAAA,CAAI,aAAA,CAAc,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA,EAAG,KAAA,CAAM,WAAW,CAAA,EAAG,OAAO,IAAA;AAClE,EAAA,GAAA,CACE,aAAA;AAAA,IACE,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,WAAA,CAAY,OAAA,EAAS,CAAC,CAAA;AAAA,IAC5C,KAAA,CAAM;AAAA,EACR,CAAA;AAEA,IAAA,OAAO,IAAA;AACT,EAAA,OAAO,KAAA;AACT;AAUA,SAAS,aAAA,CAAc,KAAA,EAAiB,KAAA,EAAiB;AACvD,EAAA,OAAO,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,EAAA,GAAK,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,CAAA;AACtD;AAGA,IAAO,6BAAA,EAAQ,cAAA;ADxJf;AACE;AACA;AACF,wFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-touches/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Geometry, LineString, Point } from \"geojson\";\nimport { booleanPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Boolean-touches true if none of the points common to both geometries\n * intersect the interiors of both geometries.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n * var point = turf.point([1, 1]);\n *\n * turf.booleanTouches(point, line);\n * //=true\n */\nfunction booleanTouches(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  var geom1 = getGeom(feature1);\n  var geom2 = getGeom(feature2);\n  var type1 = geom1.type;\n  var type2 = geom2.type;\n\n  switch (type1) {\n    case \"Point\":\n      switch (type2) {\n        case \"LineString\":\n          return isPointOnLineEnd(geom1, geom2);\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n            if (\n              isPointOnLineEnd(geom1, {\n                type: \"LineString\",\n                coordinates: geom2.coordinates[ii],\n              })\n            )\n              foundTouchingPoint = true;\n          }\n          return foundTouchingPoint;\n        case \"Polygon\":\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (\n              booleanPointOnLine(geom1, {\n                type: \"LineString\",\n                coordinates: geom2.coordinates[i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPolygon\":\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates[i].length; ii++) {\n              if (\n                booleanPointOnLine(geom1, {\n                  type: \"LineString\",\n                  coordinates: geom2.coordinates[i][ii],\n                })\n              )\n                return true;\n            }\n          }\n          return false;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"LineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                isPointOnLineEnd(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  geom2\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointOnLine(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreEndVertices: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  isPointOnLineEnd(\n                    { type: \"Point\", coordinates: geom1.coordinates[i] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[ii] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[ii] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i] },\n                    {\n                      type: \"LineString\",\n                      coordinates: geom2.coordinates[ii][0],\n                    }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"Polygon\", coordinates: geom2.coordinates[ii] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"Point\":\n          return isPointOnLineEnd(geom2, geom1);\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                isPointOnLineEnd(\n                  { type: \"Point\", coordinates: geom2.coordinates[i] },\n                  geom1\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointOnLine(\n                { type: \"Point\", coordinates: geom2.coordinates[i] },\n                geom1,\n                { ignoreEndVertices: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var endMatch = false;\n          if (\n            isPointOnLineEnd(\n              { type: \"Point\", coordinates: geom1.coordinates[0] },\n              geom2\n            )\n          )\n            endMatch = true;\n          if (\n            isPointOnLineEnd(\n              {\n                type: \"Point\",\n                coordinates: geom1.coordinates[geom1.coordinates.length - 1],\n              },\n              geom2\n            )\n          )\n            endMatch = true;\n          if (endMatch === false) return false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              booleanPointOnLine(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreEndVertices: true }\n              )\n            )\n              return false;\n          }\n          return endMatch;\n        case \"MultiLineString\":\n          var endMatch = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (\n              isPointOnLineEnd(\n                { type: \"Point\", coordinates: geom1.coordinates[0] },\n                { type: \"LineString\", coordinates: geom2.coordinates[i] }\n              )\n            )\n              endMatch = true;\n            if (\n              isPointOnLineEnd(\n                {\n                  type: \"Point\",\n                  coordinates: geom1.coordinates[geom1.coordinates.length - 1],\n                },\n                { type: \"LineString\", coordinates: geom2.coordinates[i] }\n              )\n            )\n              endMatch = true;\n            for (var ii = 0; ii < geom1.coordinates[i].length; ii++) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[ii] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[i] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return endMatch;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i] },\n                    {\n                      type: \"LineString\",\n                      coordinates: geom2.coordinates[ii][0],\n                    }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiLineString\":\n      switch (type2) {\n        case \"Point\":\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              isPointOnLineEnd(geom2, {\n                type: \"LineString\",\n                coordinates: geom1.coordinates[i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  isPointOnLineEnd(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[ii] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[ii] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var endMatch = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              isPointOnLineEnd(\n                { type: \"Point\", coordinates: geom1.coordinates[i][0] },\n                geom2\n              )\n            )\n              endMatch = true;\n            if (\n              isPointOnLineEnd(\n                {\n                  type: \"Point\",\n                  coordinates:\n                    geom1.coordinates[i][geom1.coordinates[i].length - 1],\n                },\n                geom2\n              )\n            )\n              endMatch = true;\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[i] },\n                  { ignoreEndVertices: true }\n                )\n              )\n                return false;\n            }\n          }\n          return endMatch;\n        case \"MultiLineString\":\n          var endMatch = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (\n                isPointOnLineEnd(\n                  { type: \"Point\", coordinates: geom1.coordinates[i][0] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[ii] }\n                )\n              )\n                endMatch = true;\n              if (\n                isPointOnLineEnd(\n                  {\n                    type: \"Point\",\n                    coordinates:\n                      geom1.coordinates[i][geom1.coordinates[i].length - 1],\n                  },\n                  { type: \"LineString\", coordinates: geom2.coordinates[ii] }\n                )\n              )\n                endMatch = true;\n              for (var iii = 0; iii < geom1.coordinates[i].length; iii++) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i][iii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[ii] },\n                    { ignoreEndVertices: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n          return endMatch;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom1.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[i][ii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[i][ii] },\n                  geom2,\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom1.coordinates.length; ii++) {\n              for (var iii = 0; iii < geom1.coordinates[ii].length; iii++) {\n                if (!foundTouchingPoint) {\n                  if (\n                    booleanPointOnLine(\n                      {\n                        type: \"Point\",\n                        coordinates: geom1.coordinates[ii][iii],\n                      },\n                      {\n                        type: \"LineString\",\n                        coordinates: geom2.coordinates[0][i],\n                      }\n                    )\n                  )\n                    foundTouchingPoint = true;\n                }\n                if (\n                  booleanPointInPolygon(\n                    { type: \"Point\", coordinates: geom1.coordinates[ii][iii] },\n                    { type: \"Polygon\", coordinates: [geom2.coordinates[0][i]] },\n                    { ignoreBoundary: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"Point\":\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            if (\n              booleanPointOnLine(geom2, {\n                type: \"LineString\",\n                coordinates: geom1.coordinates[i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom2.coordinates[i] },\n                geom1,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom2.coordinates[i] },\n                  { type: \"LineString\", coordinates: geom1.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom2.coordinates[i] },\n                geom1,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates[i].length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom2.coordinates[i][ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[0] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom2.coordinates[i][ii] },\n                  geom1,\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            if (!foundTouchingPoint) {\n              if (\n                booleanPointOnLine(\n                  { type: \"Point\", coordinates: geom1.coordinates[0][i] },\n                  { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                )\n              )\n                foundTouchingPoint = true;\n            }\n            if (\n              booleanPointInPolygon(\n                { type: \"Point\", coordinates: geom1.coordinates[0][i] },\n                geom2,\n                { ignoreBoundary: true }\n              )\n            )\n              return false;\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom2.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom1.coordinates[0].length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[0][ii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[0][i] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[0][ii] },\n                  { type: \"Polygon\", coordinates: geom2.coordinates[0][i] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPolygon\":\n      switch (type2) {\n        case \"Point\":\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            if (\n              booleanPointOnLine(geom2, {\n                type: \"LineString\",\n                coordinates: geom1.coordinates[0][i],\n              })\n            )\n              return true;\n          }\n          return false;\n        case \"MultiPoint\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[0][i] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"Polygon\", coordinates: geom1.coordinates[0][i] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"LineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                    { type: \"LineString\", coordinates: geom1.coordinates[0][i] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom2.coordinates[ii] },\n                  { type: \"Polygon\", coordinates: geom1.coordinates[0][i] },\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"MultiLineString\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates.length; i++) {\n            for (var ii = 0; ii < geom2.coordinates.length; ii++) {\n              for (var iii = 0; iii < geom2.coordinates[ii].length; iii++) {\n                if (!foundTouchingPoint) {\n                  if (\n                    booleanPointOnLine(\n                      {\n                        type: \"Point\",\n                        coordinates: geom2.coordinates[ii][iii],\n                      },\n                      {\n                        type: \"LineString\",\n                        coordinates: geom1.coordinates[i][0],\n                      }\n                    )\n                  )\n                    foundTouchingPoint = true;\n                }\n                if (\n                  booleanPointInPolygon(\n                    { type: \"Point\", coordinates: geom2.coordinates[ii][iii] },\n                    { type: \"Polygon\", coordinates: [geom1.coordinates[i][0]] },\n                    { ignoreBoundary: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n\n          return foundTouchingPoint;\n        case \"Polygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom1.coordinates[0][i].length; ii++) {\n              if (!foundTouchingPoint) {\n                if (\n                  booleanPointOnLine(\n                    { type: \"Point\", coordinates: geom1.coordinates[0][i][ii] },\n                    { type: \"LineString\", coordinates: geom2.coordinates[0] }\n                  )\n                )\n                  foundTouchingPoint = true;\n              }\n              if (\n                booleanPointInPolygon(\n                  { type: \"Point\", coordinates: geom1.coordinates[0][i][ii] },\n                  geom2,\n                  { ignoreBoundary: true }\n                )\n              )\n                return false;\n            }\n          }\n          return foundTouchingPoint;\n        case \"MultiPolygon\":\n          var foundTouchingPoint = false;\n          for (var i = 0; i < geom1.coordinates[0].length; i++) {\n            for (var ii = 0; ii < geom2.coordinates[0].length; ii++) {\n              for (var iii = 0; iii < geom1.coordinates[0].length; iii++) {\n                if (!foundTouchingPoint) {\n                  if (\n                    booleanPointOnLine(\n                      {\n                        type: \"Point\",\n                        coordinates: geom1.coordinates[0][i][iii],\n                      },\n                      {\n                        type: \"LineString\",\n                        coordinates: geom2.coordinates[0][ii],\n                      }\n                    )\n                  )\n                    foundTouchingPoint = true;\n                }\n                if (\n                  booleanPointInPolygon(\n                    {\n                      type: \"Point\",\n                      coordinates: geom1.coordinates[0][i][iii],\n                    },\n                    { type: \"Polygon\", coordinates: geom2.coordinates[0][ii] },\n                    { ignoreBoundary: true }\n                  )\n                )\n                  return false;\n              }\n            }\n          }\n          return foundTouchingPoint;\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction isPointOnLineEnd(point: Point, line: LineString) {\n  if (compareCoords(line.coordinates[0], point.coordinates)) return true;\n  if (\n    compareCoords(\n      line.coordinates[line.coordinates.length - 1],\n      point.coordinates\n    )\n  )\n    return true;\n  return false;\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true/false if coord pairs match\n */\nfunction compareCoords(pair1: number[], pair2: number[]) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\nexport { booleanTouches };\nexport default booleanTouches;\n"]}