{"version": 3, "sources": ["../../index.js"], "sourcesContent": ["import { getCoords, getType } from \"@turf/invariant\";\nimport { lineString as linestring } from \"@turf/helpers\";\nimport { nearestPointOnLine } from \"@turf/nearest-point-on-line\";\n\n/**\n * Takes a {@link LineString|line}, a start {@link Point}, and a stop point\n * and returns a subsection of the line in-between those points.\n * The start & stop points don't need to fall exactly on the line.\n *\n * This can be useful for extracting only the part of a route between waypoints.\n *\n * @function\n * @param {Coord} startPt starting point\n * @param {Coord} stopPt stopping point\n * @param {Feature<LineString>|LineString} line line to slice\n * @returns {Feature<LineString>} sliced line\n * @example\n * var line = turf.lineString([\n *     [-77.031669, 38.878605],\n *     [-77.029609, 38.881946],\n *     [-77.020339, 38.884084],\n *     [-77.025661, 38.885821],\n *     [-77.021884, 38.889563],\n *     [-77.019824, 38.892368]\n * ]);\n * var start = turf.point([-77.029609, 38.881946]);\n * var stop = turf.point([-77.021884, 38.889563]);\n *\n * var sliced = turf.lineSlice(start, stop, line);\n *\n * //addToMap\n * var addToMap = [start, stop, line]\n */\nfunction lineSlice(startPt, stopPt, line) {\n  // Validation\n  var coords = getCoords(line);\n  if (getType(line) !== \"LineString\")\n    throw new Error(\"line must be a LineString\");\n\n  var startVertex = nearestPointOnLine(line, startPt);\n  var stopVertex = nearestPointOnLine(line, stopPt);\n  var ends;\n  if (startVertex.properties.index <= stopVertex.properties.index) {\n    ends = [startVertex, stopVertex];\n  } else {\n    ends = [stopVertex, startVertex];\n  }\n  var clipCoords = [ends[0].geometry.coordinates];\n  for (\n    var i = ends[0].properties.index + 1;\n    i < ends[1].properties.index + 1;\n    i++\n  ) {\n    clipCoords.push(coords[i]);\n  }\n  clipCoords.push(ends[1].geometry.coordinates);\n  return linestring(clipCoords, line.properties);\n}\n\nexport { lineSlice };\nexport default lineSlice;\n"], "mappings": ";AAAA,SAAS,WAAW,eAAe;AACnC,SAAS,cAAc,kBAAkB;AACzC,SAAS,0BAA0B;AA+BnC,SAAS,UAAU,SAAS,QAAQ,MAAM;AAExC,MAAI,SAAS,UAAU,IAAI;AAC3B,MAAI,QAAQ,IAAI,MAAM;AACpB,UAAM,IAAI,MAAM,2BAA2B;AAE7C,MAAI,cAAc,mBAAmB,MAAM,OAAO;AAClD,MAAI,aAAa,mBAAmB,MAAM,MAAM;AAChD,MAAI;AACJ,MAAI,YAAY,WAAW,SAAS,WAAW,WAAW,OAAO;AAC/D,WAAO,CAAC,aAAa,UAAU;AAAA,EACjC,OAAO;AACL,WAAO,CAAC,YAAY,WAAW;AAAA,EACjC;AACA,MAAI,aAAa,CAAC,KAAK,CAAC,EAAE,SAAS,WAAW;AAC9C,WACM,IAAI,KAAK,CAAC,EAAE,WAAW,QAAQ,GACnC,IAAI,KAAK,CAAC,EAAE,WAAW,QAAQ,GAC/B,KACA;AACA,eAAW,KAAK,OAAO,CAAC,CAAC;AAAA,EAC3B;AACA,aAAW,KAAK,KAAK,CAAC,EAAE,SAAS,WAAW;AAC5C,SAAO,WAAW,YAAY,KAAK,UAAU;AAC/C;AAGA,IAAO,0BAAQ;", "names": []}