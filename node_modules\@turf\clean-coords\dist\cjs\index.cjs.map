{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-clean-coords/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wCAAwB;AACxB,4CAAmC;AAsBnC,SAAS,WAAA,CACP,OAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACL;AAEA,EAAA,IAAI,OAAA,EAAS,OAAO,QAAA,IAAY,SAAA,EAAW,OAAA,CAAQ,OAAA,EAAS,OAAA;AAC5D,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AACnD,EAAA,IAAI,KAAA,EAAO,gCAAA,OAAe,CAAA;AAG1B,EAAA,IAAI,UAAA,EAAY,CAAC,CAAA;AAEjB,EAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,IACZ,KAAK,YAAA;AACH,MAAA,UAAA,EAAY,SAAA,CAAU,OAAA,EAAS,IAAI,CAAA;AACnC,MAAA,KAAA;AAAA,IACF,KAAK,iBAAA;AAAA,IACL,KAAK,SAAA;AACH,MAAA,kCAAA,OAAiB,CAAA,CAAE,OAAA,CAAQ,QAAA,CAAU,IAAA,EAAM;AACzC,QAAA,SAAA,CAAU,IAAA,CAAK,SAAA,CAAU,IAAA,EAAM,IAAI,CAAC,CAAA;AAAA,MACtC,CAAC,CAAA;AACD,MAAA,KAAA;AAAA,IACF,KAAK,cAAA;AACH,MAAA,kCAAA,OAAiB,CAAA,CAAE,OAAA,CAAQ,QAAA,CAAU,QAAA,EAAe;AAClD,QAAA,IAAI,WAAA,EAAyB,CAAC,CAAA;AAC9B,QAAA,QAAA,CAAS,OAAA,CAAQ,QAAA,CAAU,IAAA,EAAkB;AAC3C,UAAA,UAAA,CAAW,IAAA,CAAK,SAAA,CAAU,IAAA,EAAM,IAAI,CAAC,CAAA;AAAA,QACvC,CAAC,CAAA;AACD,QAAA,SAAA,CAAU,IAAA,CAAK,UAAU,CAAA;AAAA,MAC3B,CAAC,CAAA;AACD,MAAA,KAAA;AAAA,IACF,KAAK,OAAA;AACH,MAAA,OAAO,OAAA;AAAA,IACT,KAAK,YAAA;AACH,MAAA,IAAI,SAAA,EAAiC,CAAC,CAAA;AACtC,MAAA,kCAAA,OAAiB,CAAA,CAAE,OAAA,CAAQ,QAAA,CAAU,KAAA,EAAY;AAC/C,QAAA,IAAI,IAAA,EAAM,KAAA,CAAM,IAAA,CAAK,GAAG,CAAA;AACxB,QAAA,GAAA,CAAI,CAAC,MAAA,CAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAA,EAAU,GAAG,CAAA,EAAG;AACxD,UAAA,SAAA,CAAU,IAAA,CAAK,KAAK,CAAA;AACpB,UAAA,QAAA,CAAS,GAAG,EAAA,EAAI,IAAA;AAAA,QAClB;AAAA,MACF,CAAC,CAAA;AACD,MAAA,KAAA;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,KAAA,EAAO,yBAAyB,CAAA;AAAA,EACpD;AAGA,EAAA,GAAA,CAAI,OAAA,CAAQ,WAAA,EAAa;AACvB,IAAA,GAAA,CAAI,OAAA,IAAW,IAAA,EAAM;AACnB,MAAA,OAAA,CAAQ,YAAA,EAAc,SAAA;AACtB,MAAA,OAAO,OAAA;AAAA,IACT;AACA,IAAA,OAAO,EAAE,IAAA,EAAY,WAAA,EAAa,UAAU,CAAA;AAAA,EAC9C,EAAA,KAAO;AACL,IAAA,GAAA,CAAI,OAAA,IAAW,IAAA,EAAM;AACnB,MAAA,OAAA,CAAQ,QAAA,CAAS,YAAA,EAAc,SAAA;AAC/B,MAAA,OAAO,OAAA;AAAA,IACT;AACA,IAAA,OAAO,8BAAA,EAAU,IAAA,EAAY,WAAA,EAAa,UAAU,CAAA,EAAG,OAAA,CAAQ,UAAA,EAAY;AAAA,MACzE,IAAA,EAAM,OAAA,CAAQ,IAAA;AAAA,MACd,EAAA,EAAI,OAAA,CAAQ;AAAA,IACd,CAAC,CAAA;AAAA,EACH;AACF;AAUA,SAAS,SAAA,CAAU,IAAA,EAAkB,IAAA,EAAc;AACjD,EAAA,IAAI,OAAA,EAAS,kCAAA,IAAc,CAAA;AAE3B,EAAA,GAAA,CAAI,MAAA,CAAO,OAAA,IAAW,EAAA,GAAK,CAAC,MAAA,CAAO,MAAA,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,CAAC,CAAC,CAAA,EAAG,OAAO,MAAA;AAEjE,EAAA,IAAI,UAAA,EAAY,CAAC,CAAA;AACjB,EAAA,IAAI,aAAA,EAAe,MAAA,CAAO,OAAA,EAAS,CAAA;AACnC,EAAA,IAAI,gBAAA,EAAkB,SAAA,CAAU,MAAA;AAEhC,EAAA,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AACxB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,YAAA,EAAc,CAAA,EAAA,EAAK;AACrC,IAAA,IAAI,eAAA,EAAiB,SAAA,CAAU,SAAA,CAAU,OAAA,EAAS,CAAC,CAAA;AACnD,IAAA,GAAA,CACE,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,cAAA,CAAe,CAAC,EAAA,GACjC,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,cAAA,CAAe,CAAC,CAAA;AAEjC,MAAA,QAAA;AAAA,IAAA,KACG;AACH,MAAA,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AACxB,MAAA,gBAAA,EAAkB,SAAA,CAAU,MAAA;AAC5B,MAAA,GAAA,CAAI,gBAAA,EAAkB,CAAA,EAAG;AACvB,QAAA,GAAA,CACE,oBAAA;AAAA,UACE,SAAA,CAAU,gBAAA,EAAkB,CAAC,CAAA;AAAA,UAC7B,SAAA,CAAU,gBAAA,EAAkB,CAAC,CAAA;AAAA,UAC7B,SAAA,CAAU,gBAAA,EAAkB,CAAC;AAAA,QAC/B,CAAA;AAEA,UAAA,SAAA,CAAU,MAAA,CAAO,SAAA,CAAU,OAAA,EAAS,CAAA,EAAG,CAAC,CAAA;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACA,EAAA,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,OAAA,EAAS,CAAC,CAAC,CAAA;AACxC,EAAA,gBAAA,EAAkB,SAAA,CAAU,MAAA;AAG5B,EAAA,GAAA,CAAA,CACG,KAAA,IAAS,UAAA,GAAa,KAAA,IAAS,cAAA,EAAA,GAChC,MAAA,CAAO,MAAA,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,MAAA,CAAO,OAAA,EAAS,CAAC,CAAC,EAAA,GAC3C,gBAAA,EAAkB,CAAA,EAClB;AACA,IAAA,MAAM,IAAI,KAAA,CAAM,iBAAiB,CAAA;AAAA,EACnC;AAEA,EAAA,GAAA,CAAI,KAAA,IAAS,aAAA,GAAgB,gBAAA,EAAkB,CAAA,EAAG;AAChD,IAAA,OAAO,SAAA;AAAA,EACT;AAEA,EAAA,GAAA,CACE,oBAAA;AAAA,IACE,SAAA,CAAU,gBAAA,EAAkB,CAAC,CAAA;AAAA,IAC7B,SAAA,CAAU,gBAAA,EAAkB,CAAC,CAAA;AAAA,IAC7B,SAAA,CAAU,gBAAA,EAAkB,CAAC;AAAA,EAC/B,CAAA;AAEA,IAAA,SAAA,CAAU,MAAA,CAAO,SAAA,CAAU,OAAA,EAAS,CAAA,EAAG,CAAC,CAAA;AAE1C,EAAA,OAAO,SAAA;AACT;AAUA,SAAS,MAAA,CAAO,GAAA,EAAe,GAAA,EAAe;AAC5C,EAAA,OAAO,GAAA,CAAI,CAAC,EAAA,IAAM,GAAA,CAAI,CAAC,EAAA,GAAK,GAAA,CAAI,CAAC,EAAA,IAAM,GAAA,CAAI,CAAC,CAAA;AAC9C;AAYA,SAAS,oBAAA,CAAqB,KAAA,EAAiB,GAAA,EAAe,KAAA,EAAiB;AAC7E,EAAA,IAAI,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EACb,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA;AACb,EAAA,IAAI,OAAA,EAAS,KAAA,CAAM,CAAC,CAAA,EAClB,OAAA,EAAS,KAAA,CAAM,CAAC,CAAA;AAClB,EAAA,IAAI,KAAA,EAAO,GAAA,CAAI,CAAC,CAAA,EACd,KAAA,EAAO,GAAA,CAAI,CAAC,CAAA;AAEd,EAAA,IAAI,IAAA,EAAM,EAAA,EAAI,MAAA;AACd,EAAA,IAAI,IAAA,EAAM,EAAA,EAAI,MAAA;AACd,EAAA,IAAI,IAAA,EAAM,KAAA,EAAO,MAAA;AACjB,EAAA,IAAI,IAAA,EAAM,KAAA,EAAO,MAAA;AACjB,EAAA,IAAI,MAAA,EAAQ,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,GAAA;AAE9B,EAAA,GAAA,CAAI,MAAA,IAAU,CAAA,EAAG,OAAO,KAAA;AAAA,EAAA,KAAA,GAAA,CACf,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA;AACpC,IAAA,OAAO,IAAA,EAAM,EAAA,EAAI,OAAA,GAAU,EAAA,GAAK,EAAA,GAAK,KAAA,EAAO,KAAA,GAAQ,EAAA,GAAK,EAAA,GAAK,MAAA;AAAA,EAAA,KAC3D,OAAO,IAAA,EAAM,EAAA,EAAI,OAAA,GAAU,EAAA,GAAK,EAAA,GAAK,KAAA,EAAO,KAAA,GAAQ,EAAA,GAAK,EAAA,GAAK,MAAA;AACrE;AAGA,IAAO,0BAAA,EAAQ,WAAA;ADtFf;AACE;AACA;AACF,+EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-clean-coords/dist/cjs/index.cjs", "sourcesContent": [null, "import { Position } from \"geojson\";\nimport { feature } from \"@turf/helpers\";\nimport { getCoords, getType } from \"@turf/invariant\";\n\n// To-Do => Improve Typescript GeoJSON handling\n\n/**\n * Removes redundant coordinates from any GeoJSON Geometry.\n *\n * @function\n * @param {Geometry|Feature} geojson Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.mutate=false] allows GeoJSON input to be mutated\n * @returns {Geometry|Feature} the cleaned input Feature/Geometry\n * @example\n * var line = turf.lineString([[0, 0], [0, 2], [0, 5], [0, 8], [0, 8], [0, 10]]);\n * var multiPoint = turf.multiPoint([[0, 0], [0, 0], [2, 2]]);\n *\n * turf.cleanCoords(line).geometry.coordinates;\n * //= [[0, 0], [0, 10]]\n *\n * turf.cleanCoords(multiPoint).geometry.coordinates;\n * //= [[0, 0], [2, 2]]\n */\nfunction cleanCoords(\n  geojson: any,\n  options: {\n    mutate?: boolean;\n  } = {}\n) {\n  // Backwards compatible with v4.0\n  var mutate = typeof options === \"object\" ? options.mutate : options;\n  if (!geojson) throw new Error(\"geojson is required\");\n  var type = getType(geojson);\n\n  // Store new \"clean\" points in this Array\n  var newCoords = [];\n\n  switch (type) {\n    case \"LineString\":\n      newCoords = cleanLine(geojson, type);\n      break;\n    case \"MultiLineString\":\n    case \"Polygon\":\n      getCoords(geojson).forEach(function (line) {\n        newCoords.push(cleanLine(line, type));\n      });\n      break;\n    case \"MultiPolygon\":\n      getCoords(geojson).forEach(function (polygons: any) {\n        var polyPoints: Position[] = [];\n        polygons.forEach(function (ring: Position[]) {\n          polyPoints.push(cleanLine(ring, type));\n        });\n        newCoords.push(polyPoints);\n      });\n      break;\n    case \"Point\":\n      return geojson;\n    case \"MultiPoint\":\n      var existing: Record<string, true> = {};\n      getCoords(geojson).forEach(function (coord: any) {\n        var key = coord.join(\"-\");\n        if (!Object.prototype.hasOwnProperty.call(existing, key)) {\n          newCoords.push(coord);\n          existing[key] = true;\n        }\n      });\n      break;\n    default:\n      throw new Error(type + \" geometry not supported\");\n  }\n\n  // Support input mutation\n  if (geojson.coordinates) {\n    if (mutate === true) {\n      geojson.coordinates = newCoords;\n      return geojson;\n    }\n    return { type: type, coordinates: newCoords };\n  } else {\n    if (mutate === true) {\n      geojson.geometry.coordinates = newCoords;\n      return geojson;\n    }\n    return feature({ type: type, coordinates: newCoords }, geojson.properties, {\n      bbox: geojson.bbox,\n      id: geojson.id,\n    });\n  }\n}\n\n/**\n * Clean Coords\n *\n * @private\n * @param {Array<number>|LineString} line Line\n * @param {string} type Type of geometry\n * @returns {Array<number>} Cleaned coordinates\n */\nfunction cleanLine(line: Position[], type: string) {\n  var points = getCoords(line);\n  // handle \"clean\" segment\n  if (points.length === 2 && !equals(points[0], points[1])) return points;\n\n  var newPoints = [];\n  var secondToLast = points.length - 1;\n  var newPointsLength = newPoints.length;\n\n  newPoints.push(points[0]);\n  for (var i = 1; i < secondToLast; i++) {\n    var prevAddedPoint = newPoints[newPoints.length - 1];\n    if (\n      points[i][0] === prevAddedPoint[0] &&\n      points[i][1] === prevAddedPoint[1]\n    )\n      continue;\n    else {\n      newPoints.push(points[i]);\n      newPointsLength = newPoints.length;\n      if (newPointsLength > 2) {\n        if (\n          isPointOnLineSegment(\n            newPoints[newPointsLength - 3],\n            newPoints[newPointsLength - 1],\n            newPoints[newPointsLength - 2]\n          )\n        )\n          newPoints.splice(newPoints.length - 2, 1);\n      }\n    }\n  }\n  newPoints.push(points[points.length - 1]);\n  newPointsLength = newPoints.length;\n\n  // (Multi)Polygons must have at least 4 points, but a closed LineString with only 3 points is acceptable\n  if (\n    (type === \"Polygon\" || type === \"MultiPolygon\") &&\n    equals(points[0], points[points.length - 1]) &&\n    newPointsLength < 4\n  ) {\n    throw new Error(\"invalid polygon\");\n  }\n\n  if (type === \"LineString\" && newPointsLength < 3) {\n    return newPoints;\n  }\n\n  if (\n    isPointOnLineSegment(\n      newPoints[newPointsLength - 3],\n      newPoints[newPointsLength - 1],\n      newPoints[newPointsLength - 2]\n    )\n  )\n    newPoints.splice(newPoints.length - 2, 1);\n\n  return newPoints;\n}\n\n/**\n * Compares two points and returns if they are equals\n *\n * @private\n * @param {Position} pt1 point\n * @param {Position} pt2 point\n * @returns {boolean} true if they are equals\n */\nfunction equals(pt1: Position, pt2: Position) {\n  return pt1[0] === pt2[0] && pt1[1] === pt2[1];\n}\n\n/**\n * Returns if `point` is on the segment between `start` and `end`.\n * Borrowed from `@turf/boolean-point-on-line` to speed up the evaluation (instead of using the module as dependency)\n *\n * @private\n * @param {Position} start coord pair of start of line\n * @param {Position} end coord pair of end of line\n * @param {Position} point coord pair of point to check\n * @returns {boolean} true/false\n */\nfunction isPointOnLineSegment(start: Position, end: Position, point: Position) {\n  var x = point[0],\n    y = point[1];\n  var startX = start[0],\n    startY = start[1];\n  var endX = end[0],\n    endY = end[1];\n\n  var dxc = x - startX;\n  var dyc = y - startY;\n  var dxl = endX - startX;\n  var dyl = endY - startY;\n  var cross = dxc * dyl - dyc * dxl;\n\n  if (cross !== 0) return false;\n  else if (Math.abs(dxl) >= Math.abs(dyl))\n    return dxl > 0 ? startX <= x && x <= endX : endX <= x && x <= startX;\n  else return dyl > 0 ? startY <= y && y <= endY : endY <= y && y <= startY;\n}\n\nexport { cleanCoords };\nexport default cleanCoords;\n"]}