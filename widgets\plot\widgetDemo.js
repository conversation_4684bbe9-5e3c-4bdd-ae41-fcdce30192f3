//模块：
mars3d.widget.bindClass(mars3d.widget.BaseWidget.extend({
    options: {
        resources: [
            //字体标号使用到的div转img库，无此标号时可以删除
            "./lib/dom2img/dom-to-image.js",
        ],

    },

    getServerURL: function () {
        return this.viewer.mars.config.serverURL;
    },

    getDefStyle: function (type) {
        return mars3d.draw.util.getDefStyle(type)
    },

    //初始化[仅执行1次]
    create: function () {
        var that = this;

        var inthtml =
            '<div class="infoview">' +
            '   <div class="checkbox checkbox-primary ">' +
            '        <input id="zdwxy" class="styled" type="checkbox" checked>' +
            '        <label for="zdwxy">' +
            '            重大危险源' +
            '        </label>' +
            '   </div>' +
            '   <div class="checkbox checkbox-primary ">' +
            '        <input id="ssjc" class="styled" type="checkbox" checked>' +
            '        <label for="ssjc">' +
            '            实时监测' +
            '        </label>' +
            '   </div>' +
            '   <div class="checkbox checkbox-primary ">' +
            '        <input id="spjk" class="styled" type="checkbox" checked>' +
            '        <label for="spjk">' +
            '            视频监控' +
            '        </label>' +
            '   </div>' +
            '   <div class="checkbox checkbox-primary ">' +
            '        <input id="wzck" class="styled" type="checkbox" checked>' +
            '        <label for="wzck">' +
            '            物资仓库' +
            '        </label>' +
            '   </div>' +
            '   <div class="checkbox checkbox-primary ">' +
            '        <input id="gf" class="styled" type="checkbox" checked>' +
            '        <label for="gf">' +
            '            光伏发电' +
            '        </label>' +
            '   </div>' +
            '</div>';
        $("body").append(inthtml);
        that.initWork();
    },

    //当前页面业务相关
    initWork :function(){
        var jsonZdwxy = JSON.parse("[{\"id\":1,\"text\":\"一号危险源\",\"grade\":\"一级\",\"r_value\":\"300\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"一号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.735843,\"lat\":36.111217,\"z\":33.2},{\"id\":2,\"text\":\"二号危险源\",\"grade\":\"二级\",\"r_value\":\"200\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"二号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.735911,\"lat\":36.111223,\"z\":32.55},{\"id\":3,\"text\":\"三号危险源\",\"grade\":\"三级\",\"r_value\":\"100\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"三号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736006,\"lat\":36.111192,\"z\":36.28},{\"id\":4,\"text\":\"四号危险源\",\"grade\":\"一级\",\"r_value\":\"300\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"四号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736065,\"lat\":36.111186,\"z\":34.7},{\"id\":5,\"text\":\"五号危险源\",\"grade\":\"二级\",\"r_value\":\"200\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"五号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736715,\"lat\":36.111444,\"z\":39.86},{\"id\":6,\"text\":\"六号危险源\",\"grade\":\"三级\",\"r_value\":\"100\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"六号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736809,\"lat\":36.111452,\"z\":39.65},{\"id\":7,\"text\":\"七号危险源\",\"grade\":\"一级\",\"r_value\":\"300\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"七号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736714,\"lat\":36.111514,\"z\":38.69},{\"id\":8,\"text\":\"八号危险源\",\"grade\":\"二级\",\"r_value\":\"200\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"八号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736819,\"lat\":36.111517,\"z\":42.25},{\"id\":9,\"text\":\"九号危险源\",\"grade\":\"三级\",\"r_value\":\"100\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"九号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736514,\"lat\":36.11152,\"z\":39.62},{\"id\":10,\"text\":\"十号危险源\",\"grade\":\"三级\",\"r_value\":\"100\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"十号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.73652,\"lat\":36.111423,\"z\":42.06},{\"id\":11,\"text\":\"十一号危险源\",\"grade\":\"一级\",\"r_value\":\"300\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"十一号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736407,\"lat\":36.111447,\"z\":39.83},{\"id\":12,\"text\":\"十二号危险源\",\"grade\":\"二级\",\"r_value\":\"200\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"十二号罐区\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.73641,\"lat\":36.111515,\"z\":41.03}]");
        var jsonSsjc = JSON.parse("[{\"id\":4,\"text\":\"汽油1号罐\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"一号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736005,\"lat\":36.111244,\"z\":41.47},{\"id\":5,\"text\":\"汽油2号罐\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"一号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736005,\"lat\":36.11133,\"z\":42.46}]");
        var jsonSpjk = JSON.parse("[{\"id\":11,\"text\":\"视频监控1\",\"address\":\"二号罐区\",\"lng\":115.739038,\"lat\":36.112115,\"z\":38.04},{\"id\":12,\"text\":\"视频监控2\",\"address\":\"二号罐区\",\"lng\":115.738508,\"lat\":36.112099,\"z\":35.47},{\"id\":13,\"text\":\"视频监控3\",\"address\":\"二号罐区\",\"lng\":115.738365,\"lat\":36.110807,\"z\":33.65},{\"id\":14,\"text\":\"视频监控4\",\"address\":\"二号罐区\",\"lng\":115.738567,\"lat\":36.11072,\"z\":33.71},{\"id\":15,\"text\":\"视频监控5\",\"address\":\"二号罐区\",\"lng\":115.74141,\"lat\":36.10998,\"z\":37.33},{\"id\":16,\"text\":\"视频监控6\",\"address\":\"二号罐区\",\"lng\":115.740002,\"lat\":36.110564,\"z\":33.28}]");
        var jsonWzck = JSON.parse("[{\"id\":1,\"text\":\"物资仓库1\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"一号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736218,\"lat\":36.111532,\"z\":32.29},{\"id\":2,\"text\":\"物资仓库2\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"一号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.738955,\"lat\":36.110141,\"z\":34.31},{\"id\":3,\"text\":\"物资仓库3\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"一号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.740381,\"lat\":36.111715,\"z\":50.02},{\"id\":4,\"text\":\"物资仓库4\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"四号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.737095,\"lat\":36.110034,\"z\":36.49},{\"id\":5,\"text\":\"物资仓库5\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"五号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.738652,\"lat\":36.11144,\"z\":39.4},{\"id\":6,\"text\":\"物资仓库6\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"六号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.738719,\"lat\":36.111792,\"z\":36.78},{\"id\":7,\"text\":\"物资仓库7\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"七号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.73619,\"lat\":36.112392,\"z\":42.65},{\"id\":8,\"text\":\"物资仓库8\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"八号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736503,\"lat\":36.112273,\"z\":50.91},{\"id\":9,\"text\":\"物资仓库9\",\"grade\":\"一级\",\"type\":\"存储单位\",\"item\":\"汽油\",\"address\":\"九号罐区\",\"yw\":\"液位\",\"yl\":\"压力\",\"fzr\":\"张三\",\"phone\":\"0531-XXXXXXXX\",\"lng\":115.736783,\"lat\":36.112357,\"z\":42.53}]");
        var jsonGf = JSON.parse("[{\"id\":9,\"text\":\"光伏发电1\",\"value\":\"123\",\"address\":\"一号光伏区\",\"lng\":115.736388,\"lat\":36.109023,\"z\":36.72},{\"id\":10,\"text\":\"光伏发电2\",\"value\":\"343\",\"address\":\"二号光伏区\",\"lng\":115.736382,\"lat\":36.108234,\"z\":36.83}]");
        this.managerZdwxy(jsonZdwxy);
        this.managerSsjc(jsonSsjc);
        this.managerSpjk(jsonSpjk);
        this.managerWzck(jsonWzck);
        this.managerGf(jsonGf);
        $("#zdwxy").click(function () {
            var val = $(this).is(':checked');
            for (var i = 0; i < billboardCollectionZdwxy.length; i++) {
                billboardCollectionZdwxy._billboards[i].show = val;
            }
            for (var i = 0; i < labelCollectionZdwxy.length; i++) {
                labelCollectionZdwxy._labels[i].show = val;
            }
        });
        $("#ssjc").click(function () {
            var val = $(this).is(':checked');
            for (var i = 0; i < billboardCollectionSsjc.length; i++) {
                billboardCollectionSsjc._billboards[i].show = val;
            }
            for (var i = 0; i < labelCollectionSsjc.length; i++) {
                labelCollectionSsjc._labels[i].show = val;
            }
        });
        $("#spjk").click(function () {
            var val = $(this).is(':checked');
            for (var i = 0; i < billboardCollectionSpjk.length; i++) {
                billboardCollectionSpjk._billboards[i].show = val;
            }
            for (var i = 0; i < labelCollectionSpjk.length; i++) {
                labelCollectionSpjk._labels[i].show = val;
            }
        });
        $("#wzck").click(function () {
            var val = $(this).is(':checked');
            for (var i = 0; i < billboardCollectionWzck.length; i++) {
                billboardCollectionWzck._billboards[i].show = val;
            }
            for (var i = 0; i < labelCollectionWzck.length; i++) {
                labelCollectionWzck._labels[i].show = val;
            }
        });
        $("#gf").click(function () {
            var val = $(this).is(':checked');
            for (var i = 0; i < billboardCollectionGf.length; i++) {
                billboardCollectionGf._billboards[i].show = val;
            }
            for (var i = 0; i < labelCollectionGf.length; i++) {
                labelCollectionGf._labels[i].show = val;
            }
        });
    },

    //重点危险源
    labelCollectionZdwxy:{},
    billboardCollectionZdwxy:{},
    managerZdwxy:function(data) {
        labelCollectionZdwxy = new Cesium.LabelCollection(data);
        billboardCollectionZdwxy = new Cesium.BillboardCollection(data);

        for (var i = 0, len = data.length; i < len; i++) {
            var item = data[i];
            var position = Cesium.Cartesian3.fromDegrees(+item.lng, +item.lat, item.z || 0);
            var inthtml =
                '<table style="width: auto;"><tr>' +
                '<th scope="col" colspan="4"  style="text-align:center;font-size:15px;">' +
                item.text +
                "</th></tr><tr>" +
                "<td >级别：</td><td >" + item.grade + "</td></tr><tr>" +
                "<td >R值：</td><td >" + item.r_value + "</td></tr><tr>" +
                "<td >类型：</td><td >" + item.type + "</td></tr><tr>" +
                "<td >存储：</td><td >" + item.item + "</td></tr><tr>" +
                "<td >地址：</td><td >" + item.address + "</td></tr><tr>" +
                "<td >联系人：</td><td >" + item.fzr + "</td></tr><tr>" +
                "<td >联系电话：</td><td >" + item.phone + "</td></tr><tr>" +
                '<td colspan="4" style="text-align:right;"><a href="javascript:showXQ(\'' + item.ID + '\')">历史</a></td></tr></table>';

            var primitive = labelCollectionZdwxy.add({
                position: position,
                text: item.text,
                font: "normal small-caps normal 16px 楷体",
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                fillColor: Cesium.Color.AZURE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(10, 0), //偏移量
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 80000)
            });
            // primitive.popup = inthtml;

            primitive = billboardCollectionZdwxy.add({
                position: position,
                image: 'img/marker/mark2.png',
                scale: 0.6,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6, 0.2)
            });

            primitive.tooltip = {
                html: inthtml,
                anchor: [0, -12],
            };

            // primitive.popup = inthtml;
            primitive.click = function (entity) {//单击
                if (viewer.camera.positionCartographic.height > 90000) {
                    viewer.mars.popup.close();//关闭popup

                    var position = entity.position;
                    viewer.mars.centerPoint(position, {
                        radius: 5000,   //距离目标点的距离
                        pitch: -50,     //相机方向
                        duration: 4,
                        complete: function (e) {//飞行完成回调方法
                            viewer.mars.popup.show(entity,position);//显示popup

                        }
                    });

                }
            }
        }

        viewer.scene.primitives.add(labelCollectionZdwxy);
        viewer.scene.primitives.add(billboardCollectionZdwxy);

    },
    //光伏
    labelCollectionGf:{},
    billboardCollectionGf:{},
    managerGf:function(data) {
        labelCollectionGf = new Cesium.LabelCollection(data);
        billboardCollectionGf = new Cesium.BillboardCollection(data);

        for (var i = 0, len = data.length; i < len; i++) {
            var item = data[i];
            var position = Cesium.Cartesian3.fromDegrees(+item.lng, +item.lat, item.z || 0);
            var inthtml =
                '<table style="width: auto;"><tr>' +
                '<th scope="col" colspan="4"  style="text-align:center;font-size:15px;">' +
                item.text +
                "</th></tr><tr>" +
                "<td >发电量：</td><td >" + item.value + "</td></tr><tr>" +
                "<td >地址：</td><td >" + item.address + "</td></tr><tr>" +
                '<td colspan="4" style="text-align:right;"><a href="javascript:showXQ(\'' + item.ID + '\')">历史</a></td></tr></table>';

            var primitive = labelCollectionGf.add({
                position: position,
                text: item.text,
                font: "normal small-caps normal 16px 楷体",
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                fillColor: Cesium.Color.AZURE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(10, 0), //偏移量
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 80000)
            });
            // primitive.popup = inthtml;

            primitive = billboardCollectionGf.add({
                position: position,
                image: 'img/marker/mark3.png',
                scale: 0.6,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6, 0.2)
            });

            primitive.tooltip = {
                html: inthtml,
                anchor: [0, -12],
            };
            // primitive.popup = inthtml;
            primitive.click = function (entity) {//单击
                if (viewer.camera.positionCartographic.height > 90000) {
                    viewer.mars.popup.close();//关闭popup

                    var position = entity.position;
                    viewer.mars.centerPoint(position, {
                        radius: 5000,   //距离目标点的距离
                        pitch: -50,     //相机方向
                        duration: 4,
                        complete: function (e) {//飞行完成回调方法
                            viewer.mars.popup.show(entity,position);//显示popup

                        }
                    });

                }
            }
        }

        viewer.scene.primitives.add(labelCollectionGf);
        viewer.scene.primitives.add(billboardCollectionGf);

    },
    // 实时监测
    labelCollectionSsjc:{},
    billboardCollectionSsjc:{},
    managerSsjc:function(data) {
        labelCollectionSsjc = new Cesium.LabelCollection(data);
        billboardCollectionSsjc = new Cesium.BillboardCollection(data);

        for (var i = 0, len = data.length; i < len; i++) {
            var item = data[i];
            var position = Cesium.Cartesian3.fromDegrees(+item.lng, +item.lat, item.z || 0);
            var inthtml =
                '<table style="width: auto;"><tr>' +
                '<th scope="col" colspan="4"  style="text-align:center;font-size:15px;">' +
                item.text +
                "</th></tr><tr>" +
                "<td >级别：</td><td >" + item.grade + "</td></tr><tr>" +
                "<td >类型：</td><td >" + item.type + "</td></tr><tr>" +
                "<td >存储：</td><td >" + item.item + "</td></tr><tr>" +
                "<td >地址：</td><td >" + item.address + "</td></tr><tr>" +
                "<td >联系人：</td><td >" + item.fzr + "</td></tr><tr>" +
                "<td >联系电话：</td><td >" + item.phone + "</td></tr><tr>" +
                '<td colspan="4" style="text-align:right;"><a href="javascript:showXQ(\'' + item.ID + '\')">历史</a></td></tr></table>';

            var primitive = labelCollectionSsjc.add({
                position: position,
                text: item.text,
                font: "normal small-caps normal 16px 楷体",
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                fillColor: Cesium.Color.AZURE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(10, 0), //偏移量
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 80000)
            });
            // primitive.popup = inthtml;

            primitive = billboardCollectionSsjc.add({
                position: position,
                image: 'img/marker/mark4.png',
                scale: 0.6,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6, 0.2)
            });

            primitive.tooltip = {
                html: inthtml,
                anchor: [0, -12],
            };
            // primitive.popup = inthtml;
            primitive.click = function (entity) {//单击
                if (viewer.camera.positionCartographic.height > 90000) {
                    viewer.mars.popup.close();//关闭popup

                    var position = entity.position;
                    viewer.mars.centerPoint(position, {
                        radius: 5000,   //距离目标点的距离
                        pitch: -50,     //相机方向
                        duration: 4,
                        complete: function (e) {//飞行完成回调方法
                            viewer.mars.popup.show(entity,position);//显示popup

                        }
                    });

                }
            }
        }

        viewer.scene.primitives.add(labelCollectionSsjc);
        viewer.scene.primitives.add(billboardCollectionSsjc);

    },
    // 视频监控
    labelCollectionSpjk:{},
    billboardCollectionSpjk:{},
    managerSpjk:function(data) {
        labelCollectionSpjk = new Cesium.LabelCollection(data);
        billboardCollectionSpjk = new Cesium.BillboardCollection(data);

        for (var i = 0, len = data.length; i < len; i++) {
            var item = data[i];
            var position = Cesium.Cartesian3.fromDegrees(+item.lng, +item.lat, item.z || 0);
            var inthtml ="<video   width='300'  autoplay loop='true' ><source src='img/movie.mp4'  type='video/mp4'> </video>";
                // '<table style="width: auto;"><tr>' +
                // '<th scope="col" colspan="4"  style="text-align:center;font-size:15px;">' +   controls
                // item.text +
                // "</th></tr><tr>" +
                // "<td >级别：</td><td >" + item.grade + "</td></tr><tr>" +
                // "<td >R值：</td><td >" + item.r_value + "</td></tr><tr>" +
                // "<td >类型：</td><td >" + item.type + "</td></tr><tr>" +
                // "<td >存储：</td><td >" + item.item + "</td></tr><tr>" +
                // "<td >地址：</td><td >" + item.address + "</td></tr><tr>" +
                // "<td >联系人：</td><td >" + item.fzr + "</td></tr><tr>" +
                // "<td >联系电话：</td><td >" + item.phone + "</td></tr><tr>" +
                // '<td colspan="4" style="text-align:right;"></td></tr></table>';

            var primitive = labelCollectionSpjk.add({
                position: position,
                text: item.text,
                font: "normal small-caps normal 16px 楷体",
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                fillColor: Cesium.Color.AZURE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(10, 0), //偏移量
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 80000)
            });
            // primitive.popup = inthtml;

            primitive = billboardCollectionSpjk.add({
                position: position,
                image: 'img/marker/mark5.png',
                scale: 0.6,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6, 0.2),

            });

            primitive.popup = inthtml;
            primitive.click = function (entity) {//单击
                if (viewer.camera.positionCartographic.height > 90000) {
                    viewer.mars.popup.close();//关闭popup

                    var position = entity.position;
                    viewer.mars.centerPoint(position, {
                        radius: 5000,   //距离目标点的距离
                        pitch: -50,     //相机方向
                        duration: 4,
                        complete: function (e) {//飞行完成回调方法
                            viewer.mars.popup.show(entity,position);//显示popup

                        }
                    });

                }
            }
        }

        viewer.scene.primitives.add(labelCollectionSpjk);
        viewer.scene.primitives.add(billboardCollectionSpjk);

    },
    // 物资仓库
    labelCollectionWzck:{},
    billboardCollectionWzck:{},
    managerWzck:function(data) {
        labelCollectionWzck = new Cesium.LabelCollection(data);
        billboardCollectionWzck = new Cesium.BillboardCollection(data);

        for (var i = 0, len = data.length; i < len; i++) {
            var item = data[i];
            var position = Cesium.Cartesian3.fromDegrees(+item.lng, +item.lat, item.z || 0);
            var inthtml =
                '<table style="width: auto;"><tr>' +
                '<th scope="col" colspan="4"  style="text-align:center;font-size:15px;">' +
                item.text +
                "</th></tr><tr>" +
                "<td >级别：</td><td >" + item.grade + "</td></tr><tr>" +
                "<td >类型：</td><td >" + item.type + "</td></tr><tr>" +
                "<td >存储：</td><td >" + item.item + "</td></tr><tr>" +
                "<td >地址：</td><td >" + item.address + "</td></tr><tr>" +
                "<td >联系人：</td><td >" + item.fzr + "</td></tr><tr>" +
                "<td >联系电话：</td><td >" + item.phone + "</td></tr><tr>" +
                '<td colspan="4" style="text-align:right;"></td></tr></table>';

            var primitive = labelCollectionWzck.add({
                position: position,
                text: item.text,
                font: "normal small-caps normal 16px 楷体",
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                fillColor: Cesium.Color.AZURE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(10, 0), //偏移量
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 80000)
            });
            // primitive.popup = inthtml;

            primitive = billboardCollectionWzck.add({
                position: position,
                image: 'img/marker/mark1.png',
                scale: 0.6,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6, 0.2)
            });

            primitive.tooltip = {
                html: inthtml,
                anchor: [0, -12],
            };
            // primitive.popup = inthtml;
            primitive.click = function (entity) {//单击
                if (viewer.camera.positionCartographic.height > 90000) {
                    viewer.mars.popup.close();//关闭popup

                    var position = entity.position;
                    viewer.mars.centerPoint(position, {
                        radius: 5000,   //距离目标点的距离
                        pitch: -50,     //相机方向
                        duration: 4,
                        complete: function (e) {//飞行完成回调方法
                            viewer.mars.popup.show(entity,position);//显示popup

                        }
                    });

                }
            }
        }

        viewer.scene.primitives.add(labelCollectionWzck);
        viewer.scene.primitives.add(billboardCollectionWzck);

    },
}));

function showXQ(id) {
    // 只是为了演示，可以单击详情
    layer.open({
        type: 2,
        title: '历史数据',
        fix: true,
        shadeClose: true,
        maxmin: true,
        area: ["50%", "50%"],
        // content: "widgetsTS/ts_showView/view3.html",
        content: "widgets/plot/viewChat.html",
        skin: "layer-mars-dialog2 animation-scale-up",
        // success: function (layero) {
        // }
    });
}
//     window.open ('widgets/plot/viewChat.html',"_blank", 'height=200, width=400, top=300, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=no, status=no')
// }
