"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { newObj[key] = obj[key]; } } } newObj.default = obj; return newObj; } }// index.ts
var _helpers = require('@turf/helpers');
var _invariant = require('@turf/invariant');
var _meta = require('@turf/meta');
var _flatten = require('@turf/flatten');
var _polyclipts = require('polyclip-ts'); var polyclip = _interopRequireWildcard(_polyclipts);
function dissolve(fc, options = {}) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  const { propertyName } = options;
  _invariant.collectionOf.call(void 0, fc, "Polygon", "dissolve");
  const outFeatures = [];
  if (!propertyName) {
    return _flatten.flatten.call(void 0, 
      _helpers.multiPolygon.call(void 0, 
        polyclip.union.apply(
          null,
          // List of polygons expressed as Position[][][] a.k.a. Geom[]
          fc.features.map(function(f) {
            return f.geometry.coordinates;
          })
        )
      )
    );
  } else {
    const uniquePropertyVals = {};
    _meta.featureEach.call(void 0, fc, function(feature) {
      if (feature.properties) {
        if (!Object.prototype.hasOwnProperty.call(
          uniquePropertyVals,
          feature.properties[propertyName]
        )) {
          uniquePropertyVals[feature.properties[propertyName]] = [];
        }
        uniquePropertyVals[feature.properties[propertyName]].push(feature);
      }
    });
    const vals = Object.keys(uniquePropertyVals);
    for (let i = 0; i < vals.length; i++) {
      const mp = _helpers.multiPolygon.call(void 0, 
        polyclip.union.apply(
          null,
          // List of polygons expressed as Position[][][] a.k.a. Geom[]
          uniquePropertyVals[vals[i]].map(function(f) {
            return f.geometry.coordinates;
          })
        )
      );
      if (mp && mp.properties) {
        mp.properties[propertyName] = vals[i];
        outFeatures.push(mp);
      }
    }
  }
  return _flatten.flatten.call(void 0, _helpers.featureCollection.call(void 0, outFeatures));
}
var turf_dissolve_default = dissolve;



exports.default = turf_dissolve_default; exports.dissolve = dissolve;
//# sourceMappingURL=index.cjs.map