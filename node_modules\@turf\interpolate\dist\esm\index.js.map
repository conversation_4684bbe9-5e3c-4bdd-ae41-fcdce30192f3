{"version": 3, "sources": ["../../index.js"], "sourcesContent": ["import { bbox } from \"@turf/bbox\";\nimport { hexGrid } from \"@turf/hex-grid\";\nimport { pointGrid } from \"@turf/point-grid\";\nimport { distance } from \"@turf/distance\";\nimport { centroid } from \"@turf/centroid\";\nimport { squareGrid } from \"@turf/square-grid\";\nimport { triangleGrid } from \"@turf/triangle-grid\";\nimport { clone } from \"@turf/clone\";\nimport { featureCollection, validateBBox } from \"@turf/helpers\";\nimport { featureEach } from \"@turf/meta\";\nimport { collectionOf } from \"@turf/invariant\";\n\n/**\n * Takes a set of points and estimates their 'property' values on a grid using the [Inverse Distance Weighting (IDW) method](https://en.wikipedia.org/wiki/Inverse_distance_weighting).\n *\n * @function\n * @param {FeatureCollection<Point>} points with known value\n * @param {number} cellSize the distance across each grid point\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.gridType='square'] defines the output format based on a Grid Type (options: 'square' | 'point' | 'hex' | 'triangle')\n * @param {string} [options.property='elevation'] the property name in `points` from which z-values will be pulled, zValue fallbacks to 3rd coordinate if no property exists.\n * @param {string} [options.units='kilometers'] used in calculating cellSize, can be degrees, radians, miles, or kilometers\n * @param {number} [options.weight=1] exponent regulating the distance-decay weighting\n * @param {BBox}   [options.bbox=bbox(points)] Bounding Box Array [west, south, east, north] associated with the FeatureCollection.\n * @returns {FeatureCollection<Point|Polygon>} grid of points or polygons with interpolated 'property'\n * @example\n * var points = turf.randomPoint(30, {bbox: [50, 30, 70, 50]});\n *\n * // add a random property to each point\n * turf.featureEach(points, function(point) {\n *     point.properties.solRad = Math.random() * 50;\n * });\n * var options = {gridType: 'points', property: 'solRad', units: 'miles'};\n * var grid = turf.interpolate(points, 100, options);\n *\n * //addToMap\n * var addToMap = [grid];\n */\nfunction interpolate(points, cellSize, options) {\n  // Optional parameters\n  options = options || {};\n  if (typeof options !== \"object\") throw new Error(\"options is invalid\");\n  var gridType = options.gridType;\n  var property = options.property;\n  var weight = options.weight;\n  var box = options.bbox;\n\n  // validation\n  if (!points) throw new Error(\"points is required\");\n  collectionOf(points, \"Point\", \"input must contain Points\");\n  if (!cellSize) throw new Error(\"cellSize is required\");\n  if (weight !== undefined && typeof weight !== \"number\")\n    throw new Error(\"weight must be a number\");\n\n  // default values\n  property = property || \"elevation\";\n  gridType = gridType || \"square\";\n  weight = weight || 1;\n\n  box = box ?? bbox(points);\n  validateBBox(box);\n  var grid;\n  switch (gridType) {\n    case \"point\":\n    case \"points\":\n      grid = pointGrid(box, cellSize, options);\n      break;\n    case \"square\":\n    case \"squares\":\n      grid = squareGrid(box, cellSize, options);\n      break;\n    case \"hex\":\n    case \"hexes\":\n      grid = hexGrid(box, cellSize, options);\n      break;\n    case \"triangle\":\n    case \"triangles\":\n      grid = triangleGrid(box, cellSize, options);\n      break;\n    default:\n      throw new Error(\"invalid gridType\");\n  }\n  var results = [];\n  featureEach(grid, function (gridFeature) {\n    var zw = 0;\n    var sw = 0;\n    // calculate the distance from each input point to the grid points\n    featureEach(points, function (point) {\n      var gridPoint =\n        gridType === \"point\" ? gridFeature : centroid(gridFeature);\n      var d = distance(gridPoint, point, options);\n      var zValue;\n      // property has priority for zValue, fallbacks to 3rd coordinate from geometry\n      if (property !== undefined) zValue = point.properties[property];\n      if (zValue === undefined) zValue = point.geometry.coordinates[2];\n      if (zValue === undefined) throw new Error(\"zValue is missing\");\n      if (d === 0) zw = zValue;\n      var w = 1.0 / Math.pow(d, weight);\n      sw += w;\n      zw += w * zValue;\n    });\n    // write interpolated value for each grid point\n    var newFeature = clone(gridFeature);\n    newFeature.properties[property] = zw / sw;\n    results.push(newFeature);\n  });\n  return featureCollection(results);\n}\n\nexport { interpolate };\nexport default interpolate;\n"], "mappings": ";AAAA,SAAS,YAAY;AACrB,SAAS,eAAe;AACxB,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AACzB,SAAS,gBAAgB;AACzB,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B,SAAS,aAAa;AACtB,SAAS,mBAAmB,oBAAoB;AAChD,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;AA4B7B,SAAS,YAAY,QAAQ,UAAU,SAAS;AAE9C,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,YAAY,SAAU,OAAM,IAAI,MAAM,oBAAoB;AACrE,MAAI,WAAW,QAAQ;AACvB,MAAI,WAAW,QAAQ;AACvB,MAAI,SAAS,QAAQ;AACrB,MAAI,MAAM,QAAQ;AAGlB,MAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,oBAAoB;AACjD,eAAa,QAAQ,SAAS,2BAA2B;AACzD,MAAI,CAAC,SAAU,OAAM,IAAI,MAAM,sBAAsB;AACrD,MAAI,WAAW,UAAa,OAAO,WAAW;AAC5C,UAAM,IAAI,MAAM,yBAAyB;AAG3C,aAAW,YAAY;AACvB,aAAW,YAAY;AACvB,WAAS,UAAU;AAEnB,QAAM,oBAAO,KAAK,MAAM;AACxB,eAAa,GAAG;AAChB,MAAI;AACJ,UAAQ,UAAU;AAAA,IAChB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK,UAAU,OAAO;AACvC;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,aAAO,WAAW,KAAK,UAAU,OAAO;AACxC;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,aAAO,QAAQ,KAAK,UAAU,OAAO;AACrC;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,aAAO,aAAa,KAAK,UAAU,OAAO;AAC1C;AAAA,IACF;AACE,YAAM,IAAI,MAAM,kBAAkB;AAAA,EACtC;AACA,MAAI,UAAU,CAAC;AACf,cAAY,MAAM,SAAU,aAAa;AACvC,QAAI,KAAK;AACT,QAAI,KAAK;AAET,gBAAY,QAAQ,SAAU,OAAO;AACnC,UAAI,YACF,aAAa,UAAU,cAAc,SAAS,WAAW;AAC3D,UAAI,IAAI,SAAS,WAAW,OAAO,OAAO;AAC1C,UAAI;AAEJ,UAAI,aAAa,OAAW,UAAS,MAAM,WAAW,QAAQ;AAC9D,UAAI,WAAW,OAAW,UAAS,MAAM,SAAS,YAAY,CAAC;AAC/D,UAAI,WAAW,OAAW,OAAM,IAAI,MAAM,mBAAmB;AAC7D,UAAI,MAAM,EAAG,MAAK;AAClB,UAAI,IAAI,IAAM,KAAK,IAAI,GAAG,MAAM;AAChC,YAAM;AACN,YAAM,IAAI;AAAA,IACZ,CAAC;AAED,QAAI,aAAa,MAAM,WAAW;AAClC,eAAW,WAAW,QAAQ,IAAI,KAAK;AACvC,YAAQ,KAAK,UAAU;AAAA,EACzB,CAAC;AACD,SAAO,kBAAkB,OAAO;AAClC;AAGA,IAAO,2BAAQ;", "names": []}