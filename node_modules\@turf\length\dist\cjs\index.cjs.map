{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-length/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,0CAAyB;AAEzB,kCAA8B;AAkB9B,SAAS,MAAA,CACP,OAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACG;AAER,EAAA,OAAO,iCAAA;AAAA,IACL,OAAA;AAAA,IACA,CAAC,aAAA,EAAe,OAAA,EAAA,GAAY;AAC1B,MAAA,MAAM,OAAA,EAAS,OAAA,CAAS,QAAA,CAAS,WAAA;AACjC,MAAA,OAAO,cAAA,EAAiB,gCAAA,MAAS,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,CAAC,CAAA,EAAG,OAAO,CAAA;AAAA,IAChE,CAAA;AAAA,IACA;AAAA,EACF,CAAA;AACF;AAGA,IAAO,oBAAA,EAAQ,MAAA;ADzBf;AACE;AACA;AACF,+DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-length/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, FeatureCollection, GeometryCollection } from \"geojson\";\nimport { distance } from \"@turf/distance\";\nimport { Units } from \"@turf/helpers\";\nimport { segmentReduce } from \"@turf/meta\";\n\n/**\n * Takes a {@link GeoJSON} and measures its length in the specified units, {@link (Multi)Point}'s distance are ignored.\n *\n * @function\n * @param {Feature<LineString|MultiLineString>} geojson GeoJSON to measure\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units=kilometers] can be degrees, radians, miles, or kilometers\n * @returns {number} length of GeoJSON\n * @example\n * var line = turf.lineString([[115, -32], [131, -22], [143, -25], [150, -34]]);\n * var length = turf.length(line, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [line];\n * line.properties.distance = length;\n */\nfunction length(\n  geojson: Feature<any> | FeatureCollection<any> | GeometryCollection,\n  options: {\n    units?: Units;\n  } = {}\n): number {\n  // Calculate distance from 2-vertex line segments\n  return segmentReduce(\n    geojson,\n    (previousValue, segment) => {\n      const coords = segment!.geometry.coordinates;\n      return previousValue! + distance(coords[0], coords[1], options);\n    },\n    0\n  );\n}\n\nexport { length };\nexport default length;\n"]}