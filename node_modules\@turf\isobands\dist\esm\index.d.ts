import { FeatureCollection, Point, GeoJsonProperties, MultiPolygon } from 'geojson';

/**
 * Takes a square or rectangular grid {@link FeatureCollection} of {@link Point} features with z-values and an array of
 * value breaks and generates filled contour isobands.
 *
 * @function
 * @param {FeatureCollection<Point>} pointGrid input points - must be square or rectangular
 * @param {Array<number>} breaks where to draw contours
 * @param {Object} [options={}] options on output
 * @param {string} [options.zProperty='elevation'] the property name in `points` from which z-values will be pulled
 * @param {Object} [options.commonProperties={}] GeoJSON properties passed to ALL isobands
 * @param {Array<Object>} [options.breaksProperties=[]] GeoJSON properties passed, in order, to the correspondent isoband (order defined by breaks)
 * @returns {FeatureCollection<MultiPolygon>} a FeatureCollection of {@link MultiPolygon} features representing isobands
 */
declare function isobands(pointGrid: FeatureCollection<Point>, breaks: number[], options?: {
    zProperty?: string;
    commonProperties?: GeoJsonProperties;
    breaksProperties?: GeoJsonProperties[];
}): FeatureCollection<MultiPolygon>;

export { isobands as default, isobands };
