{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, FeatureCollection, Polygon } from \"geojson\";\nimport { featureCollection, isObject, multiPolygon } from \"@turf/helpers\";\nimport { collectionOf } from \"@turf/invariant\";\nimport { featureEach } from \"@turf/meta\";\nimport { flatten } from \"@turf/flatten\";\nimport * as polyclip from \"polyclip-ts\";\n\n/**\n * Dissolves a FeatureCollection of {@link Polygon} features, filtered by an optional property name:value.\n * Note that {@link MultiPolygon} features within the collection are not supported\n *\n * @function\n * @param {FeatureCollection<Polygon>} featureCollection input feature collection to be dissolved\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.propertyName] features with the same `propertyName` value will be dissolved.\n * @returns {FeatureCollection<Polygon>} a FeatureCollection containing the dissolved polygons\n * @example\n * var features = turf.featureCollection([\n *   turf.polygon([[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]], {combine: 'yes'}),\n *   turf.polygon([[[0, -1], [0, 0], [1, 0], [1, -1], [0,-1]]], {combine: 'yes'}),\n *   turf.polygon([[[1,-1],[1, 0], [2, 0], [2, -1], [1, -1]]], {combine: 'no'}),\n * ]);\n *\n * var dissolved = turf.dissolve(features, {propertyName: 'combine'});\n *\n * //addToMap\n * var addToMap = [features, dissolved]\n */\nfunction dissolve(\n  fc: FeatureCollection<Polygon>,\n  options: {\n    propertyName?: string;\n  } = {}\n): FeatureCollection<Polygon> {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const { propertyName } = options;\n\n  // Input validation\n  collectionOf(fc, \"Polygon\", \"dissolve\");\n\n  // Main\n  const outFeatures = [];\n  if (!propertyName) {\n    return flatten(\n      multiPolygon(\n        polyclip.union.apply(\n          null,\n          // List of polygons expressed as Position[][][] a.k.a. Geom[]\n          fc.features.map(function (f) {\n            return f.geometry.coordinates;\n          }) as [polyclip.Geom, ...polyclip.Geom[]]\n        )\n      )\n    );\n  } else {\n    // Group polygons by the value of their property named by propertyName\n    const uniquePropertyVals: { [key: string]: Feature[] } = {};\n    featureEach(fc, function (feature) {\n      if (feature.properties) {\n        if (\n          !Object.prototype.hasOwnProperty.call(\n            uniquePropertyVals,\n            feature.properties[propertyName]\n          )\n        ) {\n          uniquePropertyVals[feature.properties[propertyName]] =\n            [] as Feature[];\n        }\n        uniquePropertyVals[feature.properties[propertyName]].push(feature);\n      }\n    });\n    const vals = Object.keys(uniquePropertyVals);\n\n    // Export each group of polygons as a separate feature.\n    for (let i = 0; i < vals.length; i++) {\n      const mp = multiPolygon(\n        polyclip.union.apply(\n          null,\n          // List of polygons expressed as Position[][][] a.k.a. Geom[]\n          (uniquePropertyVals[vals[i]] as Feature<Polygon>[]).map(function (f) {\n            return f.geometry.coordinates;\n          }) as [polyclip.Geom, ...polyclip.Geom[]]\n        )\n      );\n      if (mp && mp.properties) {\n        mp.properties[propertyName] = vals[i];\n        outFeatures.push(mp);\n      }\n    }\n  }\n\n  return flatten(featureCollection(outFeatures));\n}\n\nexport { dissolve };\nexport default dissolve;\n"], "mappings": ";AACA,SAAS,mBAAmB,UAAU,oBAAoB;AAC1D,SAAS,oBAAoB;AAC7B,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,YAAY,cAAc;AAuB1B,SAAS,SACP,IACA,UAEI,CAAC,GACuB;AAE5B,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,QAAM,EAAE,aAAa,IAAI;AAGzB,eAAa,IAAI,WAAW,UAAU;AAGtC,QAAM,cAAc,CAAC;AACrB,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,MACL;AAAA,QACW,eAAM;AAAA,UACb;AAAA;AAAA,UAEA,GAAG,SAAS,IAAI,SAAU,GAAG;AAC3B,mBAAO,EAAE,SAAS;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AAEL,UAAM,qBAAmD,CAAC;AAC1D,gBAAY,IAAI,SAAU,SAAS;AACjC,UAAI,QAAQ,YAAY;AACtB,YACE,CAAC,OAAO,UAAU,eAAe;AAAA,UAC/B;AAAA,UACA,QAAQ,WAAW,YAAY;AAAA,QACjC,GACA;AACA,6BAAmB,QAAQ,WAAW,YAAY,CAAC,IACjD,CAAC;AAAA,QACL;AACA,2BAAmB,QAAQ,WAAW,YAAY,CAAC,EAAE,KAAK,OAAO;AAAA,MACnE;AAAA,IACF,CAAC;AACD,UAAM,OAAO,OAAO,KAAK,kBAAkB;AAG3C,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,KAAK;AAAA,QACA,eAAM;AAAA,UACb;AAAA;AAAA,UAEC,mBAAmB,KAAK,CAAC,CAAC,EAAyB,IAAI,SAAU,GAAG;AACnE,mBAAO,EAAE,SAAS;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,MAAM,GAAG,YAAY;AACvB,WAAG,WAAW,YAAY,IAAI,KAAK,CAAC;AACpC,oBAAY,KAAK,EAAE;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,QAAQ,kBAAkB,WAAW,CAAC;AAC/C;AAGA,IAAO,wBAAQ;", "names": []}