{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-clusters/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACMA,kCAA4B;AAC5B,wCAAkC;AA+BlC,SAAS,UAAA,CAGP,OAAA,EAAkC,MAAA,EAAsC;AAExE,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AACnD,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,mBAAA;AACnB,IAAA,MAAM,IAAI,KAAA,CAAM,qCAAqC,CAAA;AACvD,EAAA,GAAA,CAAI,OAAA,IAAW,KAAA,EAAA,GAAa,OAAA,IAAW,IAAA;AACrC,IAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAGtC,EAAA,IAAI,SAAA,EAA4B,CAAC,CAAA;AACjC,EAAA,+BAAA,OAAkB,EAAS,QAAA,CAAU,OAAA,EAAS;AAC5C,IAAA,GAAA,CAAI,WAAA,CAAY,OAAA,CAAQ,UAAA,EAAY,MAAM,CAAA,EAAG,QAAA,CAAS,IAAA,CAAK,OAAO,CAAA;AAAA,EACpE,CAAC,CAAA;AACD,EAAA,OAAO,wCAAA,QAA0B,CAAA;AACnC;AAmDA,SAAS,WAAA,CAIP,OAAA,EACA,QAAA,EACA,QAAA,EAKM;AAEN,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AACnD,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,mBAAA;AACnB,IAAA,MAAM,IAAI,KAAA,CAAM,qCAAqC,CAAA;AACvD,EAAA,GAAA,CAAI,SAAA,IAAa,KAAA,EAAA,GAAa,SAAA,IAAa,IAAA;AACzC,IAAA,MAAM,IAAI,KAAA,CAAM,sBAAsB,CAAA;AAGxC,EAAA,IAAI,KAAA,EAAO,UAAA,CAAW,OAAA,EAAS,QAAQ,CAAA;AACvC,EAAA,IAAI,OAAA,EAAS,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA;AAC7B,EAAA,IAAA,CAAA,IAAS,MAAA,EAAQ,CAAA,EAAG,MAAA,EAAQ,MAAA,CAAO,MAAA,EAAQ,KAAA,EAAA,EAAS;AAClD,IAAA,IAAI,MAAA,EAAQ,MAAA,CAAO,KAAK,CAAA;AACxB,IAAA,IAAI,IAAA,EAAM,IAAA,CAAK,KAAK,CAAA;AACpB,IAAA,IAAI,SAAA,EAAW,CAAC,CAAA;AAChB,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,GAAA,CAAI,MAAA,EAAQ,CAAA,EAAA,EAAK;AACnC,MAAA,QAAA,CAAS,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAS,GAAA,CAAI,CAAC,CAAC,CAAC,CAAA;AAAA,IACxC;AACA,IAAA,QAAA,CAAS,wCAAA,QAA0B,CAAA,EAAG,KAAA,EAAO,KAAK,CAAA;AAAA,EACpD;AACF;AAmEA,SAAS,aAAA,CAIP,OAAA,EACA,QAAA,EACA,QAAA,EAMA,YAAA,EACM;AACN,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,WAAA;AAAA,IACE,OAAA;AAAA,IACA,QAAA;AAAA,IACA,QAAA,CAAU,OAAA,EAAS,YAAA,EAAc,YAAA,EAAc;AAC7C,MAAA,GAAA,CAAI,aAAA,IAAiB,EAAA,GAAK,aAAA,IAAiB,KAAA,CAAA;AACzC,QAAA,cAAA,EAAgB,OAAA;AAAA,MAAA;AAEhB,QAAA,cAAA,EAAgB,QAAA;AAAA,UACd,aAAA;AAAA,UACA,OAAA;AAAA,UACA,YAAA;AAAA,UACA;AAAA,QACF,CAAA;AAAA,IACJ;AAAA,EACF,CAAA;AACA,EAAA,OAAO,aAAA;AACT;AAmBA,SAAS,UAAA,CACP,OAAA,EACA,QAAA,EACA;AACA,EAAA,IAAI,KAAA,EAAiC,CAAC,CAAA;AAEtC,EAAA,+BAAA,OAAY,EAAS,QAAA,CAAU,OAAA,EAAS,CAAA,EAAG;AACzC,IAAA,IAAI,WAAA,EAAa,OAAA,CAAQ,WAAA,GAAc,CAAC,CAAA;AACxC,IAAA,GAAA,CAAI,MAAA,CAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,UAAA,EAAY,MAAA,CAAO,QAAQ,CAAC,CAAA,EAAG;AACtE,MAAA,IAAI,MAAA,EAAQ,UAAA,CAAW,QAAQ,CAAA;AAC/B,MAAA,GAAA,CAAI,MAAA,CAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,IAAA,EAAM,KAAK,CAAA;AAClD,QAAA,IAAA,CAAK,KAAK,CAAA,CAAE,IAAA,CAAK,CAAC,CAAA;AAAA,MAAA,KACf,IAAA,CAAK,KAAK,EAAA,EAAI,CAAC,CAAC,CAAA;AAAA,IACvB;AAAA,EACF,CAAC,CAAA;AACD,EAAA,OAAO,IAAA;AACT;AAUA,SAAS,WAAA,CAAY,UAAA,EAAiB,MAAA,EAAa;AACjD,EAAA,GAAA,CAAI,WAAA,IAAe,KAAA,CAAA,EAAW,OAAO,KAAA;AACrC,EAAA,IAAI,WAAA,EAAa,OAAO,MAAA;AAGxB,EAAA,GAAA,CAAI,WAAA,IAAe,SAAA,GAAY,WAAA,IAAe,QAAA;AAC5C,IAAA,OAAO,MAAA,CAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,UAAA,EAAY,MAAM,CAAA;AAAA,EAAA,KAAA,GAAA,CAEvD,KAAA,CAAM,OAAA,CAAQ,MAAM,CAAA,EAAG;AAC9B,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,MAAA,GAAA,CAAI,CAAC,WAAA,CAAY,UAAA,EAAY,MAAA,CAAO,CAAC,CAAC,CAAA,EAAG,OAAO,KAAA;AAAA,IAClD;AACA,IAAA,OAAO,IAAA;AAAA,EAET,EAAA,KAAO;AACL,IAAA,OAAO,wBAAA,CAAyB,UAAA,EAAY,MAAM,CAAA;AAAA,EACpD;AACF;AAeA,SAAS,wBAAA,CAAyB,UAAA,EAAiB,MAAA,EAAsB;AACvE,EAAA,IAAI,KAAA,EAAO,MAAA,CAAO,IAAA,CAAK,MAAM,CAAA;AAC7B,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpC,IAAA,IAAI,IAAA,EAAM,IAAA,CAAK,CAAC,CAAA;AAChB,IAAA,GAAA,CAAI,UAAA,CAAW,GAAG,EAAA,IAAM,MAAA,CAAO,GAAG,CAAA,EAAG,OAAO,KAAA;AAAA,EAC9C;AACA,EAAA,OAAO,IAAA;AACT;AAaA,SAAS,gBAAA,CACP,UAAA,EACA,IAAA,EACK;AACL,EAAA,GAAA,CAAI,CAAC,IAAA,EAAM,OAAO,CAAC,CAAA;AACnB,EAAA,GAAA,CAAI,CAAC,IAAA,CAAK,MAAA,EAAQ,OAAO,CAAC,CAAA;AAE1B,EAAA,IAAI,cAAA,EAAqC,CAAC,CAAA;AAC1C,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,MAAA,EAAQ,CAAA,EAAA,EAAK;AACpC,IAAA,IAAI,IAAA,EAAM,IAAA,CAAK,CAAC,CAAA;AAChB,IAAA,GAAA,CAAI,MAAA,CAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,UAAA,EAAY,GAAG,CAAA;AACtD,MAAA,aAAA,CAAc,GAAG,EAAA,EAAI,UAAA,CAAW,GAAG,CAAA;AAAA,EACvC;AACA,EAAA,OAAO,aAAA;AACT;ADxPA;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACF,wRAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-clusters/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  Feature,\n  FeatureCollection,\n  GeoJsonProperties,\n  GeometryObject,\n} from \"geojson\";\nimport { featureEach } from \"@turf/meta\";\nimport { featureCollection } from \"@turf/helpers\";\n\n/**\n * Get Cluster\n *\n * @function\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {*} filter Filter used on GeoJSON properties to get Cluster\n * @returns {FeatureCollection} Single Cluster filtered by GeoJSON Properties\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0], {'marker-symbol': 'circle'}),\n *     turf.point([2, 4], {'marker-symbol': 'star'}),\n *     turf.point([3, 6], {'marker-symbol': 'star'}),\n *     turf.point([5, 1], {'marker-symbol': 'square'}),\n *     turf.point([4, 2], {'marker-symbol': 'circle'})\n * ]);\n *\n * // Create a cluster using K-Means (adds `cluster` to GeoJSON properties)\n * var clustered = turf.clustersKmeans(geojson);\n *\n * // Retrieve first cluster (0)\n * var cluster = turf.getCluster(clustered, {cluster: 0});\n * //= cluster\n *\n * // Retrieve cluster based on custom properties\n * turf.getCluster(clustered, {'marker-symbol': 'circle'}).length;\n * //= 2\n * turf.getCluster(clustered, {'marker-symbol': 'square'}).length;\n * //= 1\n */\nfunction getCluster<\n  G extends GeometryObject,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(geojson: FeatureCollection<G, P>, filter: any): FeatureCollection<G, P> {\n  // Validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (geojson.type !== \"FeatureCollection\")\n    throw new Error(\"geojson must be a FeatureCollection\");\n  if (filter === undefined || filter === null)\n    throw new Error(\"filter is required\");\n\n  // Filter Features\n  var features: Feature<G, P>[] = [];\n  featureEach<G, P>(geojson, function (feature) {\n    if (applyFilter(feature.properties, filter)) features.push(feature);\n  });\n  return featureCollection(features);\n}\n\n/**\n * Callback for clusterEach\n *\n * @callback clusterEachCallback\n * @param {FeatureCollection} [cluster] The current cluster being processed.\n * @param {any} [clusterValue] Value used to create cluster being processed.\n * @param {number} [currentIndex] The index of the current element being processed in the array.Starts at index 0\n * @returns {void}\n */\n\n/**\n * clusterEach\n *\n * @function\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {string|number} property GeoJSON property key/value used to create clusters\n * @param {clusterEachCallback} callback a method that takes (cluster, clusterValue, currentIndex)\n * @returns {void}\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0]),\n *     turf.point([2, 4]),\n *     turf.point([3, 6]),\n *     turf.point([5, 1]),\n *     turf.point([4, 2])\n * ]);\n *\n * // Create a cluster using K-Means (adds `cluster` to GeoJSON properties)\n * var clustered = turf.clustersKmeans(geojson);\n *\n * // Iterate over each cluster\n * turf.clusterEach(clustered, 'cluster', function (cluster, clusterValue, currentIndex) {\n *     //= cluster\n *     //= clusterValue\n *     //= currentIndex\n * })\n *\n * // Calculate the total number of clusters\n * var total = 0\n * turf.clusterEach(clustered, 'cluster', function () {\n *     total++;\n * });\n *\n * // Create an Array of all the values retrieved from the 'cluster' property\n * var values = []\n * turf.clusterEach(clustered, 'cluster', function (cluster, clusterValue) {\n *     values.push(clusterValue);\n * });\n */\nfunction clusterEach<\n  G extends GeometryObject,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  geojson: FeatureCollection<G, P>,\n  property: number | string,\n  callback: (\n    cluster: FeatureCollection<G, P>,\n    clusterValue?: any,\n    currentIndex?: number\n  ) => void\n): void {\n  // Validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (geojson.type !== \"FeatureCollection\")\n    throw new Error(\"geojson must be a FeatureCollection\");\n  if (property === undefined || property === null)\n    throw new Error(\"property is required\");\n\n  // Create clusters based on property values\n  var bins = createBins(geojson, property);\n  var values = Object.keys(bins);\n  for (var index = 0; index < values.length; index++) {\n    var value = values[index];\n    var bin = bins[value];\n    var features = [];\n    for (var i = 0; i < bin.length; i++) {\n      features.push(geojson.features[bin[i]]);\n    }\n    callback(featureCollection(features), value, index);\n  }\n}\n\n/**\n * Callback for clusterReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback clusterReduceCallback\n * @param {*} [previousValue] The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {FeatureCollection} [cluster] The current cluster being processed.\n * @param {*} [clusterValue] Value used to create cluster being processed.\n * @param {number} [currentIndex] The index of the current element being processed in the\n * array. Starts at index 0, if an initialValue is provided, and at index 1 otherwise.\n * @returns {void}\n */\n\n/**\n * Reduce clusters in GeoJSON Features, similar to Array.reduce()\n *\n * @function\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {string|number} property GeoJSON property key/value used to create clusters\n * @param {clusterReduceCallback} callback a method that takes (previousValue, cluster, clusterValue, currentIndex)\n * @param {any} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {any} The value that results from the reduction.\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0]),\n *     turf.point([2, 4]),\n *     turf.point([3, 6]),\n *     turf.point([5, 1]),\n *     turf.point([4, 2])\n * ]);\n *\n * // Create a cluster using K-Means (adds `cluster` to GeoJSON properties)\n * var clustered = turf.clustersKmeans(geojson);\n *\n * // Iterate over each cluster and perform a calculation\n * var initialValue = 0\n * turf.clusterReduce(clustered, 'cluster', function (previousValue, cluster, clusterValue, currentIndex) {\n *     //=previousValue\n *     //=cluster\n *     //=clusterValue\n *     //=currentIndex\n *     return previousValue++;\n * }, initialValue);\n *\n * // Calculate the total number of clusters\n * var total = turf.clusterReduce(clustered, 'cluster', function (previousValue) {\n *     return previousValue++;\n * }, 0);\n *\n * // Create an Array of all the values retrieved from the 'cluster' property\n * var values = turf.clusterReduce(clustered, 'cluster', function (previousValue, cluster, clusterValue) {\n *     return previousValue.concat(clusterValue);\n * }, []);\n */\nfunction clusterReduce<\n  G extends GeometryObject,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  geojson: FeatureCollection<G, P>,\n  property: number | string,\n  callback: (\n    previousValue: any | undefined,\n    cluster: FeatureCollection<G, P>,\n    clusterValue?: any,\n    currentIndex?: number\n  ) => void,\n  initialValue?: any\n): void {\n  var previousValue = initialValue;\n  clusterEach(\n    geojson,\n    property,\n    function (cluster, clusterValue, currentIndex) {\n      if (currentIndex === 0 && initialValue === undefined)\n        previousValue = cluster;\n      else\n        previousValue = callback(\n          previousValue,\n          cluster,\n          clusterValue,\n          currentIndex\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Create Bins\n *\n * @private\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {string|number} property Property values are used to create bins\n * @returns {Object} bins with Feature IDs\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0], {cluster: 0, foo: 'null'}),\n *     turf.point([2, 4], {cluster: 1, foo: 'bar'}),\n *     turf.point([5, 1], {0: 'foo'}),\n *     turf.point([3, 6], {cluster: 1}),\n * ]);\n * createBins(geojson, 'cluster');\n * //= { '0': [ 0 ], '1': [ 1, 3 ] }\n */\nfunction createBins(\n  geojson: FeatureCollection<any>,\n  property: string | number\n) {\n  var bins: Record<string, number[]> = {};\n\n  featureEach(geojson, function (feature, i) {\n    var properties = feature.properties || {};\n    if (Object.prototype.hasOwnProperty.call(properties, String(property))) {\n      var value = properties[property];\n      if (Object.prototype.hasOwnProperty.call(bins, value))\n        bins[value].push(i);\n      else bins[value] = [i];\n    }\n  });\n  return bins;\n}\n\n/**\n * Apply Filter\n *\n * @private\n * @param {*} properties Properties\n * @param {*} filter Filter\n * @returns {boolean} applied Filter to properties\n */\nfunction applyFilter(properties: any, filter: any) {\n  if (properties === undefined) return false;\n  var filterType = typeof filter;\n\n  // String & Number\n  if (filterType === \"number\" || filterType === \"string\")\n    return Object.prototype.hasOwnProperty.call(properties, filter);\n  // Array\n  else if (Array.isArray(filter)) {\n    for (var i = 0; i < filter.length; i++) {\n      if (!applyFilter(properties, filter[i])) return false;\n    }\n    return true;\n    // Object\n  } else {\n    return propertiesContainsFilter(properties, filter);\n  }\n}\n\n/**\n * Properties contains filter (does not apply deepEqual operations)\n *\n * @private\n * @param {*} properties Properties\n * @param {Object} filter Filter\n * @returns {boolean} does filter equal Properties\n * @example\n * propertiesContainsFilter({foo: 'bar', cluster: 0}, {cluster: 0})\n * //= true\n * propertiesContainsFilter({foo: 'bar', cluster: 0}, {cluster: 1})\n * //= false\n */\nfunction propertiesContainsFilter(properties: any, filter: any): boolean {\n  var keys = Object.keys(filter);\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (properties[key] !== filter[key]) return false;\n  }\n  return true;\n}\n\n/**\n * Filter Properties\n *\n * @private\n * @param {*} properties Properties\n * @param {Array<string>} keys Used to filter Properties\n * @returns {*} filtered Properties\n * @example\n * filterProperties({foo: 'bar', cluster: 0}, ['cluster'])\n * //= {cluster: 0}\n */\nfunction filterProperties(\n  properties: Record<string, any>,\n  keys: string[]\n): any {\n  if (!keys) return {};\n  if (!keys.length) return {};\n\n  var newProperties: Record<string, any> = {};\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (Object.prototype.hasOwnProperty.call(properties, key))\n      newProperties[key] = properties[key];\n  }\n  return newProperties;\n}\n\nexport {\n  getCluster,\n  clusterEach,\n  clusterReduce,\n  createBins,\n  applyFilter,\n  propertiesContainsFilter,\n  filterProperties,\n};\n// No default export!\n"]}