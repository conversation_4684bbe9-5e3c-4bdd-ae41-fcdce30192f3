﻿<!DOCTYPE html>
<html class="no-js css-menubar" lang="zh-cn">

<head>
    <title>弹窗子页面</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 移动设备 viewport -->
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui">
    <meta name="author" content="火星科技 http://cesium.marsgis.cn ">
    <!-- 360浏览器默认使用Webkit内核 -->
    <meta name="renderer" content="webkit">
    <!-- Chrome浏览器添加桌面快捷方式（安卓） -->
    <link rel="icon" type="image/png" href="../../img/favicon/favicon.png">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Safari浏览器添加到主屏幕（IOS） -->
    <link rel="icon" sizes="192x192" href="img/favicon/apple-touch-icon.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="火星科技MarsGIS">
    <!-- Win8标题栏及ICON图标 -->
    <link rel="apple-touch-icon-precomposed" href="../../img/favicon/apple-touch-icon.png">
    <meta name="msapplication-TileImage" content="../../img/favicon/<EMAIL>">
    <meta name="msapplication-TileColor" content="#62a8ea">

    <!-- 第3方lib引入 -->
    <script type="text/javascript" src="../../lib/include-lib.js?time=20200102" libpath="../../lib/"
        include="jquery,font-awesome,bootstrap,admui-frame"></script>

    <link href="../../css/widget-win.css" rel="stylesheet" />
    <style>
        .btn_none {
            background: none;
            color: #fff;
        }

        .btn-group {
            display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    padding-left: 20px;
        }

        .tool-btn {
            cursor: pointer;
            user-select: none;
            min-width: 62px;
            height: 96px;
            box-sizing: border-box;
            margin: 0 30px 10px 0;
            text-align: center;
            padding-top: 12px;
        }


        .tool-thum {
            transition: all .2s ease;
            width: 50px;
            height: 50px;
            margin: 0 auto;
            border-radius: 50%;
            margin-bottom: 10px;
            background: #fd960f;
            cursor: pointer;
        }

        .tool-thum img {
            width: 24px;
            height: 24px;
            margin: 13px;
        }
    </style>
</head>


<body style="padding:5px;text-align:center;" title="提示：单击按钮激活对应功能,在地图上单击开始，绘制中单击增加点、双击结束。">

    <div class="btn-group">
        <div id="btn_measure_length" class="tool-btn">
            <div  class="tool-thum" style="background: #dd751b;" ><img src="image/measure-length.svg" alt="空间距离"></div>
            <span class="btn_none"> 空间距离 </span>
        </div>

        <div id="btn_measure_length_td" class="tool-btn">
            <div  class="tool-thum" style="background: #c092fe;"><img src="image/measure-length2.svg" alt="贴地距离"></div>
            <span class="btn_none"> 贴地距离 </span>
        </div>
        

        <div id="btn_measure_section" class="tool-btn">
            <div  class="tool-thum" style="background: #88b8ff;"><img src="image/measure-section.svg" alt="剖面"></div>
            <span class="btn_none"> 剖面 </span>
        </div>

        <div id="btn_measure_area" class="tool-btn">
            <div  class="tool-thum" style="background: #3de3f4;"><img src="image/measure-area.svg" alt="面积"></div>
            <span class="btn_none"> 投影面积 </span>
        </div>

        <div id="btn_measure_area_td" class="tool-btn">
            <div  class="tool-thum" style="background: #c092fe;"><img src="image/measure-area.svg" alt="面积"></div>
            <span class="btn_none"> 贴地面积 </span>
        </div>

        <div id="btn_measure_angle" class="tool-btn">
            <div  class="tool-thum" style="background: #95d333;"><img src="image/measure-angle.svg" alt="角度"></div>
            <span class="btn_none"> 角度 </span>
        </div>


        <div id="btn_measure_height" class="tool-btn">
            <div  class="tool-thum" style="background: #55d5a0;"><img src="image/measure-height.svg" alt="高度差"></div>
            <span class="btn_none"> 高度差 </span>
        </div>

        <div id="btn_measure_supHeight" class="tool-btn">
            <div  class="tool-thum" style="background: #37bc41;"><img src="image/measure-height-sup.svg" alt="三角测量"></div>
            <span class="btn_none"> 三角测量 </span>
        </div>

        <div id="btn_measure_point" class="tool-btn">
            <div  class="tool-thum" style="background: #babc31;"><img src="image/measure-coor.svg" alt="坐标测量"></div>
            <span class="btn_none"> 坐标测量 </span>
        </div>

    </div>

    <div id="measure_danwei" style="margin-top:5px;">
        <span style="font-size: 14px;padding-right:10px">单位</span>
        <select id="measure_length_danwei" class="form-control" style="width:190px!important;">
            <option value="auto">自动</option>
            <option value="m">米</option>
            <option value="km">公里</option>
            <option value="mile">海里</option>
            <option value="zhang">丈</option>
        </select>
        <select id="measure_area_danwei" class="form-control" style="width:190px!important;display:none;">
            <option value="auto">自动</option>
            <option value="m">平方米</option>
            <option value="km">平方公里</option>
            <option value="mu">亩</option>
            <option value="ha">公顷</option>
        </select>
    </div>

    <div style="margin-top:5px;">
        <span id="lbl_measure_result" style="font-size: 16px;"></span>
    </div>

    <div>
        <button id="btn_measure_clear" type="button" class="btn"
            style="background: #0077ff; color:#fff;margin-top:15px; border-radius: 15px; padding-left:30px; padding-right:30px;">
            清空测量数据
        </button>
    </div>

    <!--页面js-->
    <script src="view.js?time=20200102"></script>
</body>

</html>