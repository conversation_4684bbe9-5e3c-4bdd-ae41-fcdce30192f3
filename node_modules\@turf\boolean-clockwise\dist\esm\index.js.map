{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, LineString, Position } from \"geojson\";\nimport { getCoords } from \"@turf/invariant\";\n\n/**\n * Takes a ring and return true or false whether or not the ring is clockwise or counter-clockwise.\n *\n * @function\n * @param {Feature<LineString>|LineString|Array<Array<number>>} line to be evaluated\n * @returns {boolean} true/false\n * @example\n * var clockwiseRing = turf.lineString([[0,0],[1,1],[1,0],[0,0]]);\n * var counterClockwiseRing = turf.lineString([[0,0],[1,0],[1,1],[0,0]]);\n *\n * turf.booleanClockwise(clockwiseRing)\n * //=true\n * turf.booleanClockwise(counterClockwiseRing)\n * //=false\n */\nfunction booleanClockwise(\n  line: Feature<LineString> | LineString | Position[]\n): boolean {\n  const ring = getCoords(line);\n  let sum = 0;\n  let i = 1;\n  let prev;\n  let cur;\n\n  while (i < ring.length) {\n    prev = cur || ring[0];\n    cur = ring[i];\n    sum += (cur[0] - prev[0]) * (cur[1] + prev[1]);\n    i++;\n  }\n  return sum > 0;\n}\n\nexport { booleanClockwise };\nexport default booleanClockwise;\n"], "mappings": ";AACA,SAAS,iBAAiB;AAiB1B,SAAS,iBACP,MACS;AACT,QAAM,OAAO,UAAU,IAAI;AAC3B,MAAI,MAAM;AACV,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AAEJ,SAAO,IAAI,KAAK,QAAQ;AACtB,WAAO,OAAO,KAAK,CAAC;AACpB,UAAM,KAAK,CAAC;AACZ,YAAQ,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC;AAC5C;AAAA,EACF;AACA,SAAO,MAAM;AACf;AAGA,IAAO,iCAAQ;", "names": []}