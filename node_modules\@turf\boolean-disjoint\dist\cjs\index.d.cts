import { Feature, Geometry } from 'geojson';

/**
 * Boolean-disjoint returns (TRUE) if the intersection of the two geometries is an empty set.
 *
 * @function
 * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry
 * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry
 * @param {Object} [options={}] Optional parameters
 * @param {boolean} [options.ignoreSelfIntersections=true] ignore self-intersections on input features
 * @returns {boolean} true if the intersection is an empty set, false otherwise
 * @example
 * var point = turf.point([2, 2]);
 * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);
 *
 * turf.booleanDisjoint(line, point);
 * //=true
 */
declare function booleanDisjoint(feature1: Feature<any> | Geometry, feature2: Feature<any> | Geometry, { ignoreSelfIntersections, }?: {
    ignoreSelfIntersections?: boolean;
}): boolean;

export { booleanDisjoint, booleanDisjoint as default };
