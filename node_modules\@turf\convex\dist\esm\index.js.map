{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, GeoJsonProperties, Polygon } from \"geojson\";\nimport { AllGeoJSON, polygon } from \"@turf/helpers\";\nimport { coordEach } from \"@turf/meta\";\nimport concaveman from \"concaveman\";\n\n/**\n * Takes a {@link Feature} or a {@link FeatureCollection} and returns a convex hull {@link Polygon}.\n *\n * Internally this uses\n * the [convex-hull](https://github.com/mikolal<PERSON>enko/convex-hull) module that implements a\n * [monotone chain hull](http://en.wikibooks.org/wiki/Algorithm_Implementation/Geometry/Convex_hull/Monotone_chain).\n *\n * @function\n * @param {GeoJSON} geojson input Feature or FeatureCollection\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.concavity=Infinity] 1 - thin shape. Infinity - convex hull.\n * @param {Object} [options.properties={}] Translate Properties to Feature\n * @returns {Feature<Polygon>} a convex hull\n * @example\n * var points = turf.featureCollection([\n *   turf.point([10.195312, 43.755225]),\n *   turf.point([10.404052, 43.8424511]),\n *   turf.point([10.579833, 43.659924]),\n *   turf.point([10.360107, 43.516688]),\n *   turf.point([10.14038, 43.588348]),\n *   turf.point([10.195312, 43.755225])\n * ]);\n *\n * var hull = turf.convex(points);\n *\n * //addToMap\n * var addToMap = [points, hull]\n */\nfunction convex<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: AllGeoJSON,\n  options: {\n    concavity?: number;\n    properties?: P;\n  } = {}\n): Feature<Polygon, P> | null {\n  // Default parameters\n  options.concavity = options.concavity || Infinity;\n\n  // Container\n  const points: number[][] = [];\n\n  // Convert all points to flat 2D coordinate Array\n  coordEach(geojson, (coord) => {\n    points.push([coord[0], coord[1]]);\n  });\n  if (!points.length) {\n    return null;\n  }\n\n  const convexHull = concaveman(points, options.concavity);\n\n  // Convex hull should have at least 3 different vertices in order to create a valid polygon\n  if (convexHull.length > 3) {\n    return polygon([convexHull]);\n  }\n  return null;\n}\n\nexport { convex };\nexport default convex;\n"], "mappings": ";AACA,SAAqB,eAAe;AACpC,SAAS,iBAAiB;AAC1B,OAAO,gBAAgB;AA8BvB,SAAS,OACP,SACA,UAGI,CAAC,GACuB;AAE5B,UAAQ,YAAY,QAAQ,aAAa;AAGzC,QAAM,SAAqB,CAAC;AAG5B,YAAU,SAAS,CAAC,UAAU;AAC5B,WAAO,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAAA,EAClC,CAAC;AACD,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,WAAW,QAAQ,QAAQ,SAAS;AAGvD,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,QAAQ,CAAC,UAAU,CAAC;AAAA,EAC7B;AACA,SAAO;AACT;AAGA,IAAO,sBAAQ;", "names": []}