<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <link rel="stylesheet" href="src/css/buttons.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- JavaScript -->
    <script src="node_modules/cesium/Build/Cesium/Cesium.js"></script>
    <script src="https://unpkg.com/@turf/turf@6.5.0/turf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 工具类 -->
    <script src="src/features/工具类/EventBus.js"></script>
    <script src="src/features/工具类/PanelManager.js"></script>
    <script src="src/features/工具类/PanelPositioner.js"></script>
    <script src="src/js/cesium.js"></script>
    <script src="src/features/布局/天空盒/SkyBoxManager.js"></script>
    <script src="src/features/布局/坐标导航/CesiumNavigation.umd.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
</head>
<body>
    <!-- 标题图片容器 -->
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    
    <div id="cesiumContainer"></div>
    
    <!-- 工具按钮组 - 按钮将由各UI组件动态添加 -->
    <div id="toolButtons"></div>
    
    <!-- 引入SVG图标 -->
    <div id="svg-container"></div>
    
    <script>
        // 等待页面加载完成
        window.onload = function() {
            try {
                // 初始化Cesium
                const viewer = initCesium();
                
                // 初始化坐标显示功能
                window.coordinateDisplay = new CoordinateDisplay(viewer);
                
                // 加载SVG图标
                fetch('src/images/svg/icons.svg')
                    .then(response => response.text())
                    .then(svgContent => {
                        document.getElementById('svg-container').innerHTML = svgContent;
                    })
                    .catch(error => console.error('加载SVG图标失败:', error));
                
            } catch (error) {
                console.error('初始化失败:', error);
            }
        };
    </script>
</body>
</html>
