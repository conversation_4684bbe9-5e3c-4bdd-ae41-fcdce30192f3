{"version": 3, "sources": ["../../index.js"], "sourcesContent": ["import { length } from \"@turf/length\";\nimport { lineSliceAlong } from \"@turf/line-slice-along\";\nimport { flattenEach } from \"@turf/meta\";\nimport { featureCollection, isObject } from \"@turf/helpers\";\n\n/**\n * Divides a {@link LineString} into chunks of a specified length.\n * If the line is shorter than the segment length then the original line is returned.\n *\n * @function\n * @param {FeatureCollection|Geometry|Feature<LineString|MultiLineString>} geojson the lines to split\n * @param {number} segmentLength how long to make each segment\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] units can be degrees, radians, miles, or kilometers\n * @param {boolean} [options.reverse=false] reverses coordinates to start the first chunked segment at the end\n * @returns {FeatureCollection<LineString>} collection of line segments\n * @example\n * var line = turf.lineString([[-95, 40], [-93, 45], [-85, 50]]);\n *\n * var chunk = turf.lineChunk(line, 15, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [chunk];\n */\nfunction lineChunk(geojson, segmentLength, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var units = options.units;\n  var reverse = options.reverse;\n\n  // Validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (segmentLength <= 0)\n    throw new Error(\"segmentLength must be greater than 0\");\n\n  // Container\n  var results = [];\n\n  // Flatten each feature to simple LineString\n  flattenEach(geojson, function (feature) {\n    // reverses coordinates to start the first chunked segment at the end\n    if (reverse)\n      feature.geometry.coordinates = feature.geometry.coordinates.reverse();\n\n    sliceLineSegments(feature, segmentLength, units, function (segment) {\n      results.push(segment);\n    });\n  });\n  return featureCollection(results);\n}\n\n/**\n * Slice Line Segments\n *\n * @private\n * @param {Feature<LineString>} line GeoJSON LineString\n * @param {number} segmentLength how long to make each segment\n * @param {string}[units='kilometers'] units can be degrees, radians, miles, or kilometers\n * @param {Function} callback iterate over sliced line segments\n * @returns {void}\n */\nfunction sliceLineSegments(line, segmentLength, units, callback) {\n  var lineLength = length(line, { units: units });\n\n  // If the line is shorter than the segment length then the orginal line is returned.\n  if (lineLength <= segmentLength) return callback(line);\n\n  var numberOfSegments = lineLength / segmentLength;\n\n  // If numberOfSegments is integer, no need to plus 1\n  if (!Number.isInteger(numberOfSegments)) {\n    numberOfSegments = Math.floor(numberOfSegments) + 1;\n  }\n\n  for (var i = 0; i < numberOfSegments; i++) {\n    var outline = lineSliceAlong(\n      line,\n      segmentLength * i,\n      segmentLength * (i + 1),\n      { units: units }\n    );\n    callback(outline, i);\n  }\n}\n\nexport { lineChunk };\nexport default lineChunk;\n"], "mappings": ";AAAA,SAAS,cAAc;AACvB,SAAS,sBAAsB;AAC/B,SAAS,mBAAmB;AAC5B,SAAS,mBAAmB,gBAAgB;AAqB5C,SAAS,UAAU,SAAS,eAAe,SAAS;AAElD,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,MAAI,QAAQ,QAAQ;AACpB,MAAI,UAAU,QAAQ;AAGtB,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AACnD,MAAI,iBAAiB;AACnB,UAAM,IAAI,MAAM,sCAAsC;AAGxD,MAAI,UAAU,CAAC;AAGf,cAAY,SAAS,SAAU,SAAS;AAEtC,QAAI;AACF,cAAQ,SAAS,cAAc,QAAQ,SAAS,YAAY,QAAQ;AAEtE,sBAAkB,SAAS,eAAe,OAAO,SAAU,SAAS;AAClE,cAAQ,KAAK,OAAO;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACD,SAAO,kBAAkB,OAAO;AAClC;AAYA,SAAS,kBAAkB,MAAM,eAAe,OAAO,UAAU;AAC/D,MAAI,aAAa,OAAO,MAAM,EAAE,MAAa,CAAC;AAG9C,MAAI,cAAc,cAAe,QAAO,SAAS,IAAI;AAErD,MAAI,mBAAmB,aAAa;AAGpC,MAAI,CAAC,OAAO,UAAU,gBAAgB,GAAG;AACvC,uBAAmB,KAAK,MAAM,gBAAgB,IAAI;AAAA,EACpD;AAEA,WAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,QAAI,UAAU;AAAA,MACZ;AAAA,MACA,gBAAgB;AAAA,MAChB,iBAAiB,IAAI;AAAA,MACrB,EAAE,MAAa;AAAA,IACjB;AACA,aAAS,SAAS,CAAC;AAAA,EACrB;AACF;AAGA,IAAO,0BAAQ;", "names": []}