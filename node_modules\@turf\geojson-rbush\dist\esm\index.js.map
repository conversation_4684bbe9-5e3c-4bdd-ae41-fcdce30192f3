{"version": 3, "sources": ["../../index.js"], "sourcesContent": ["import rbush from \"rbush\";\nimport { featureCollection } from \"@turf/helpers\";\nimport { featureEach } from \"@turf/meta\";\nimport { bbox as turfBBox } from \"@turf/bbox\";\n\n/**\n * @module rbush\n */\n\n/**\n * GeoJSON implementation of [RBush](https://github.com/mourner/rbush#rbush) spatial index.\n *\n * @function rbush\n * @param {number} [maxEntries=9] defines the maximum number of entries in a tree node. 9 (used by default) is a\n * reasonable choice for most applications. Higher value means faster insertion and slower search, and vice versa.\n * @returns {RBush} GeoJSON RBush\n * @example\n * var geojsonRbush = require('geojson-rbush').default;\n * var tree = geojsonRbush();\n */\nfunction geojsonRbush(maxEntries) {\n  var tree = new rbush(maxEntries);\n\n  /**\n   * [insert](https://github.com/mourner/rbush#data-format)\n   *\n   * @memberof rbush\n   * @param {Feature} feature insert single GeoJSON Feature\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   * tree.insert(poly)\n   */\n  tree.insert = function (feature) {\n    if (feature.type !== \"Feature\") throw new Error(\"invalid feature\");\n    feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n    return rbush.prototype.insert.call(this, feature);\n  };\n\n  /**\n   * [load](https://github.com/mourner/rbush#bulk-inserting-data)\n   *\n   * @memberof rbush\n   * @param {FeatureCollection|Array<Feature>} features load entire GeoJSON FeatureCollection\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var polys = turf.polygons([\n   *     [[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]],\n   *     [[[-93, 32], [-83, 32], [-83, 39], [-93, 39], [-93, 32]]]\n   * ]);\n   * tree.load(polys);\n   */\n  tree.load = function (features) {\n    var load = [];\n    // Load an Array of Features\n    if (Array.isArray(features)) {\n      features.forEach(function (feature) {\n        if (feature.type !== \"Feature\") throw new Error(\"invalid features\");\n        feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n        load.push(feature);\n      });\n    } else {\n      // Load a FeatureCollection\n      featureEach(features, function (feature) {\n        if (feature.type !== \"Feature\") throw new Error(\"invalid features\");\n        feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n        load.push(feature);\n      });\n    }\n    return rbush.prototype.load.call(this, load);\n  };\n\n  /**\n   * [remove](https://github.com/mourner/rbush#removing-data)\n   *\n   * @memberof rbush\n   * @param {Feature} feature remove single GeoJSON Feature\n   * @param {Function} equals Pass a custom equals function to compare by value for removal.\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   *\n   * tree.remove(poly);\n   */\n  tree.remove = function (feature, equals) {\n    if (feature.type !== \"Feature\") throw new Error(\"invalid feature\");\n    feature.bbox = feature.bbox ? feature.bbox : turfBBox(feature);\n    return rbush.prototype.remove.call(this, feature, equals);\n  };\n\n  /**\n   * [clear](https://github.com/mourner/rbush#removing-data)\n   *\n   * @memberof rbush\n   * @returns {RBush} GeoJSON Rbush\n   * @example\n   * tree.clear()\n   */\n  tree.clear = function () {\n    return rbush.prototype.clear.call(this);\n  };\n\n  /**\n   * [search](https://github.com/mourner/rbush#search)\n   *\n   * @memberof rbush\n   * @param {BBox|FeatureCollection|Feature} geojson search with GeoJSON\n   * @returns {FeatureCollection} all features that intersects with the given GeoJSON.\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   *\n   * tree.search(poly);\n   */\n  tree.search = function (geojson) {\n    var features = rbush.prototype.search.call(this, this.toBBox(geojson));\n    return featureCollection(features);\n  };\n\n  /**\n   * [collides](https://github.com/mourner/rbush#collisions)\n   *\n   * @memberof rbush\n   * @param {BBox|FeatureCollection|Feature} geojson collides with GeoJSON\n   * @returns {boolean} true if there are any items intersecting the given GeoJSON, otherwise false.\n   * @example\n   * var poly = turf.polygon([[[-78, 41], [-67, 41], [-67, 48], [-78, 48], [-78, 41]]]);\n   *\n   * tree.collides(poly);\n   */\n  tree.collides = function (geojson) {\n    return rbush.prototype.collides.call(this, this.toBBox(geojson));\n  };\n\n  /**\n   * [all](https://github.com/mourner/rbush#search)\n   *\n   * @memberof rbush\n   * @returns {FeatureCollection} all the features in RBush\n   * @example\n   * tree.all()\n   */\n  tree.all = function () {\n    var features = rbush.prototype.all.call(this);\n    return featureCollection(features);\n  };\n\n  /**\n   * [toJSON](https://github.com/mourner/rbush#export-and-import)\n   *\n   * @memberof rbush\n   * @returns {any} export data as JSON object\n   * @example\n   * var exported = tree.toJSON()\n   */\n  tree.toJSON = function () {\n    return rbush.prototype.toJSON.call(this);\n  };\n\n  /**\n   * [fromJSON](https://github.com/mourner/rbush#export-and-import)\n   *\n   * @memberof rbush\n   * @param {any} json import previously exported data\n   * @returns {RBush} GeoJSON RBush\n   * @example\n   * var exported = {\n   *   \"children\": [\n   *     {\n   *       \"type\": \"Feature\",\n   *       \"geometry\": {\n   *         \"type\": \"Point\",\n   *         \"coordinates\": [110, 50]\n   *       },\n   *       \"properties\": {},\n   *       \"bbox\": [110, 50, 110, 50]\n   *     }\n   *   ],\n   *   \"height\": 1,\n   *   \"leaf\": true,\n   *   \"minX\": 110,\n   *   \"minY\": 50,\n   *   \"maxX\": 110,\n   *   \"maxY\": 50\n   * }\n   * tree.fromJSON(exported)\n   */\n  tree.fromJSON = function (json) {\n    return rbush.prototype.fromJSON.call(this, json);\n  };\n\n  /**\n   * Converts GeoJSON to {minX, minY, maxX, maxY} schema\n   *\n   * @memberof rbush\n   * @private\n   * @param {BBox|FeatureCollection|Feature} geojson feature(s) to retrieve BBox from\n   * @returns {Object} converted to {minX, minY, maxX, maxY}\n   */\n  tree.toBBox = function (geojson) {\n    var bbox;\n    if (geojson.bbox) bbox = geojson.bbox;\n    else if (Array.isArray(geojson) && geojson.length === 4) bbox = geojson;\n    else if (Array.isArray(geojson) && geojson.length === 6)\n      bbox = [geojson[0], geojson[1], geojson[3], geojson[4]];\n    else if (geojson.type === \"Feature\") bbox = turfBBox(geojson);\n    else if (geojson.type === \"FeatureCollection\") bbox = turfBBox(geojson);\n    else throw new Error(\"invalid geojson\");\n\n    return {\n      minX: bbox[0],\n      minY: bbox[1],\n      maxX: bbox[2],\n      maxY: bbox[3],\n    };\n  };\n  return tree;\n}\n\nexport { geojsonRbush };\nexport default geojsonRbush;\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAS,yBAAyB;AAClC,SAAS,mBAAmB;AAC5B,SAAS,QAAQ,gBAAgB;AAiBjC,SAAS,aAAa,YAAY;AAChC,MAAI,OAAO,IAAI,MAAM,UAAU;AAY/B,OAAK,SAAS,SAAU,SAAS;AAC/B,QAAI,QAAQ,SAAS,UAAW,OAAM,IAAI,MAAM,iBAAiB;AACjE,YAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO;AAC7D,WAAO,MAAM,UAAU,OAAO,KAAK,MAAM,OAAO;AAAA,EAClD;AAeA,OAAK,OAAO,SAAU,UAAU;AAC9B,QAAI,OAAO,CAAC;AAEZ,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,eAAS,QAAQ,SAAU,SAAS;AAClC,YAAI,QAAQ,SAAS,UAAW,OAAM,IAAI,MAAM,kBAAkB;AAClE,gBAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO;AAC7D,aAAK,KAAK,OAAO;AAAA,MACnB,CAAC;AAAA,IACH,OAAO;AAEL,kBAAY,UAAU,SAAU,SAAS;AACvC,YAAI,QAAQ,SAAS,UAAW,OAAM,IAAI,MAAM,kBAAkB;AAClE,gBAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO;AAC7D,aAAK,KAAK,OAAO;AAAA,MACnB,CAAC;AAAA,IACH;AACA,WAAO,MAAM,UAAU,KAAK,KAAK,MAAM,IAAI;AAAA,EAC7C;AAcA,OAAK,SAAS,SAAU,SAAS,QAAQ;AACvC,QAAI,QAAQ,SAAS,UAAW,OAAM,IAAI,MAAM,iBAAiB;AACjE,YAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO;AAC7D,WAAO,MAAM,UAAU,OAAO,KAAK,MAAM,SAAS,MAAM;AAAA,EAC1D;AAUA,OAAK,QAAQ,WAAY;AACvB,WAAO,MAAM,UAAU,MAAM,KAAK,IAAI;AAAA,EACxC;AAaA,OAAK,SAAS,SAAU,SAAS;AAC/B,QAAI,WAAW,MAAM,UAAU,OAAO,KAAK,MAAM,KAAK,OAAO,OAAO,CAAC;AACrE,WAAO,kBAAkB,QAAQ;AAAA,EACnC;AAaA,OAAK,WAAW,SAAU,SAAS;AACjC,WAAO,MAAM,UAAU,SAAS,KAAK,MAAM,KAAK,OAAO,OAAO,CAAC;AAAA,EACjE;AAUA,OAAK,MAAM,WAAY;AACrB,QAAI,WAAW,MAAM,UAAU,IAAI,KAAK,IAAI;AAC5C,WAAO,kBAAkB,QAAQ;AAAA,EACnC;AAUA,OAAK,SAAS,WAAY;AACxB,WAAO,MAAM,UAAU,OAAO,KAAK,IAAI;AAAA,EACzC;AA8BA,OAAK,WAAW,SAAU,MAAM;AAC9B,WAAO,MAAM,UAAU,SAAS,KAAK,MAAM,IAAI;AAAA,EACjD;AAUA,OAAK,SAAS,SAAU,SAAS;AAC/B,QAAI;AACJ,QAAI,QAAQ,KAAM,QAAO,QAAQ;AAAA,aACxB,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW,EAAG,QAAO;AAAA,aACvD,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW;AACpD,aAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAAA,aAC/C,QAAQ,SAAS,UAAW,QAAO,SAAS,OAAO;AAAA,aACnD,QAAQ,SAAS,oBAAqB,QAAO,SAAS,OAAO;AAAA,QACjE,OAAM,IAAI,MAAM,iBAAiB;AAEtC,WAAO;AAAA,MACL,MAAM,KAAK,CAAC;AAAA,MACZ,MAAM,KAAK,CAAC;AAAA,MACZ,MAAM,KAAK,CAAC;AAAA,MACZ,MAAM,KAAK,CAAC;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAO,6BAAQ;", "names": []}