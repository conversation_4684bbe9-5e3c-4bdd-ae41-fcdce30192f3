{"version": 3, "sources": ["../../index.ts", "../../lib/rbush-export.ts"], "sourcesContent": ["import { FeatureCollection, Polygon, Point } from \"geo<PERSON><PERSON>\";\nimport { bbox as turfbbox } from \"@turf/bbox\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { rbush } from \"./lib/rbush-export.js\";\n\ninterface Entry {\n  minX: number;\n  minY: number;\n  maxX: number;\n  maxY: number;\n  property: any;\n}\n\n/**\n * Merges a specified property from a FeatureCollection of points into a\n * FeatureCollection of polygons. Given an `inProperty` on points and an `outProperty`\n * for polygons, this finds every point that lies within each polygon, collects the\n * `inProperty` values from those points, and adds them as an array to `outProperty`\n * on the polygon.\n *\n * @function\n * @param {FeatureCollection<Polygon>} polygons polygons with values on which to aggregate\n * @param {FeatureCollection<Point>} points points to be aggregated\n * @param {string} inProperty property to be nested from\n * @param {string} outProperty property to be nested into\n * @returns {FeatureCollection<Polygon>} polygons with properties listed based on `outField`\n * @example\n * var poly1 = turf.polygon([[[0,0],[10,0],[10,10],[0,10],[0,0]]]);\n * var poly2 = turf.polygon([[[10,0],[20,10],[20,20],[20,0],[10,0]]]);\n * var polyFC = turf.featureCollection([poly1, poly2]);\n * var pt1 = turf.point([5,5], {population: 200});\n * var pt2 = turf.point([1,3], {population: 600});\n * var pt3 = turf.point([14,2], {population: 100});\n * var pt4 = turf.point([13,1], {population: 200});\n * var pt5 = turf.point([19,7], {population: 300});\n * var pointFC = turf.featureCollection([pt1, pt2, pt3, pt4, pt5]);\n * var collected = turf.collect(polyFC, pointFC, 'population', 'values');\n * var values = collected.features[0].properties.values\n * //=values => [200, 600]\n *\n * //addToMap\n * var addToMap = [pointFC, collected]\n */\nfunction collect(\n  polygons: FeatureCollection<Polygon>,\n  points: FeatureCollection<Point>,\n  inProperty: string,\n  outProperty: string\n): FeatureCollection<Polygon> {\n  var rtree = new rbush<Entry>(6);\n\n  var treeItems = points.features.map(function (item) {\n    return {\n      minX: item.geometry.coordinates[0],\n      minY: item.geometry.coordinates[1],\n      maxX: item.geometry.coordinates[0],\n      maxY: item.geometry.coordinates[1],\n      property: item.properties?.[inProperty],\n    };\n  });\n\n  rtree.load(treeItems);\n  polygons.features.forEach(function (poly) {\n    if (!poly.properties) {\n      poly.properties = {};\n    }\n    var bbox = turfbbox(poly);\n    var potentialPoints = rtree.search({\n      minX: bbox[0],\n      minY: bbox[1],\n      maxX: bbox[2],\n      maxY: bbox[3],\n    });\n    var values: any[] = [];\n    potentialPoints.forEach(function (pt) {\n      if (booleanPointInPolygon([pt.minX, pt.minY], poly)) {\n        values.push(pt.property);\n      }\n    });\n\n    poly.properties[outProperty] = values;\n  });\n\n  return polygons;\n}\n\nexport { collect };\nexport default collect;\n", "// Get around problems with moduleResolution node16 and some older libraries.\n// Manifests as \"This expression is not callable ... has no call signatures\"\n// https://stackoverflow.com/a/74709714\n\nimport lib from \"rbush\";\n\nexport const rbush = lib as unknown as typeof lib.default;\n"], "mappings": ";AACA,SAAS,QAAQ,gBAAgB;AACjC,SAAS,6BAA6B;;;ACEtC,OAAO,SAAS;AAET,IAAM,QAAQ;;;ADqCrB,SAAS,QACP,UACA,QACA,YACA,aAC4B;AAC5B,MAAI,QAAQ,IAAI,MAAa,CAAC;AAE9B,MAAI,YAAY,OAAO,SAAS,IAAI,SAAU,MAAM;AAnDtD;AAoDI,WAAO;AAAA,MACL,MAAM,KAAK,SAAS,YAAY,CAAC;AAAA,MACjC,MAAM,KAAK,SAAS,YAAY,CAAC;AAAA,MACjC,MAAM,KAAK,SAAS,YAAY,CAAC;AAAA,MACjC,MAAM,KAAK,SAAS,YAAY,CAAC;AAAA,MACjC,WAAU,UAAK,eAAL,mBAAkB;AAAA,IAC9B;AAAA,EACF,CAAC;AAED,QAAM,KAAK,SAAS;AACpB,WAAS,SAAS,QAAQ,SAAU,MAAM;AACxC,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,CAAC;AAAA,IACrB;AACA,QAAI,OAAO,SAAS,IAAI;AACxB,QAAI,kBAAkB,MAAM,OAAO;AAAA,MACjC,MAAM,KAAK,CAAC;AAAA,MACZ,MAAM,KAAK,CAAC;AAAA,MACZ,MAAM,KAAK,CAAC;AAAA,MACZ,MAAM,KAAK,CAAC;AAAA,IACd,CAAC;AACD,QAAI,SAAgB,CAAC;AACrB,oBAAgB,QAAQ,SAAU,IAAI;AACpC,UAAI,sBAAsB,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG;AACnD,eAAO,KAAK,GAAG,QAAQ;AAAA,MACzB;AAAA,IACF,CAAC;AAED,SAAK,WAAW,WAAW,IAAI;AAAA,EACjC,CAAC;AAED,SAAO;AACT;AAGA,IAAO,uBAAQ;", "names": []}