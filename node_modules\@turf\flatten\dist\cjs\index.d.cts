import { AllGeoJSON } from '@turf/helpers';
import { Point, MultiPoint, Feature, FeatureCollection, LineString, MultiLineString, Polygon, MultiPolygon } from 'geojson';

/**
 * Flattens any {@link GeoJSON} to a {@link FeatureCollection} inspired by [geojson-flatten](https://github.com/tmcw/geojson-flatten).
 *
 * @function
 * @param {GeoJSON} geojson any valid GeoJSON Object
 * @returns {FeatureCollection<any>} all Multi-Geometries are flattened into single Features
 * @example
 * var multiGeometry = turf.multiPolygon([
 *   [[[102.0, 2.0], [103.0, 2.0], [103.0, 3.0], [102.0, 3.0], [102.0, 2.0]]],
 *   [[[100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0]],
 *   [[100.2, 0.2], [100.8, 0.2], [100.8, 0.8], [100.2, 0.8], [100.2, 0.2]]]
 * ]);
 *
 * var flatten = turf.flatten(multiGeometry);
 *
 * //addToMap
 * var addToMap = [flatten]
 */
declare function flatten<T extends Point | MultiPoint>(geojson: Feature<T> | FeatureCollection<T> | T): FeatureCollection<Point>;
declare function flatten<T extends LineString | MultiLineString>(geojson: Feature<T> | FeatureCollection<T> | T): FeatureCollection<LineString>;
declare function flatten<T extends Polygon | MultiPolygon>(geojson: Feature<T> | FeatureCollection<T> | T): FeatureCollection<Polygon>;
declare function flatten(geojson: AllGeoJSON): FeatureCollection<any>;

export { flatten as default, flatten };
