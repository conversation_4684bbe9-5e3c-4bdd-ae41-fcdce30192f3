# @turf/isolines

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## isolines

Takes a grid [FeatureCollection][1] of [Point][2] features with z-values and an array of
value breaks and generates [isolines][3].

### Parameters

*   `pointGrid` **[FeatureCollection][1]<[Point][2]>** input points
*   `breaks` **[Array][4]<[number][5]>** values of `zProperty` where to draw isolines
*   `options` **[Object][6]** Optional parameters (optional, default `{}`)

    *   `options.zProperty` **[string][7]** the property name in `points` from which z-values will be pulled (optional, default `'elevation'`)
    *   `options.commonProperties` **[Object][6]** GeoJSON properties passed to ALL isolines (optional, default `{}`)
    *   `options.breaksProperties` **[Array][4]<[Object][6]>** GeoJSON properties passed, in order, to the correspondent isoline;
        the breaks array will define the order in which the isolines are created (optional, default `[]`)

### Examples

```javascript
// create a grid of points with random z-values in their properties
var extent = [0, 30, 20, 50];
var cellWidth = 100;
var pointGrid = turf.pointGrid(extent, cellWidth, {units: 'miles'});

for (var i = 0; i < pointGrid.features.length; i++) {
    pointGrid.features[i].properties.temperature = Math.random() * 10;
}
var breaks = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

var lines = turf.isolines(pointGrid, breaks, {zProperty: 'temperature'});

//addToMap
var addToMap = [lines];
```

Returns **[FeatureCollection][1]<[MultiLineString][8]>** a FeatureCollection of [MultiLineString][8] features representing isolines

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://en.wikipedia.org/wiki/Contour_line

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.5

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/isolines
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
