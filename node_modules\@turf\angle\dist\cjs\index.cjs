"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _bearing = require('@turf/bearing');
var _helpers = require('@turf/helpers');
var _rhumbbearing = require('@turf/rhumb-bearing');
function angle(startPoint, midPoint, endPoint, options = {}) {
  if (!_helpers.isObject.call(void 0, options)) {
    throw new Error("options is invalid");
  }
  if (!startPoint) {
    throw new Error("startPoint is required");
  }
  if (!midPoint) {
    throw new Error("midPoint is required");
  }
  if (!endPoint) {
    throw new Error("endPoint is required");
  }
  const A = startPoint;
  const O = midPoint;
  const B = endPoint;
  const azimuthOA = _helpers.bearingToAzimuth.call(void 0, 
    options.mercator !== true ? _bearing.bearing.call(void 0, O, A) : _rhumbbearing.rhumbBearing.call(void 0, O, A)
  );
  let azimuthOB = _helpers.bearingToAzimuth.call(void 0, 
    options.mercator !== true ? _bearing.bearing.call(void 0, O, B) : _rhumbbearing.rhumbBearing.call(void 0, O, B)
  );
  if (azimuthOB < azimuthOA) {
    azimuthOB = azimuthOB + 360;
  }
  const angleAOB = azimuthOB - azimuthOA;
  if (options.explementary === true) {
    return 360 - angleAOB;
  }
  return angleAOB;
}
var turf_angle_default = angle;



exports.angle = angle; exports.default = turf_angle_default;
//# sourceMappingURL=index.cjs.map