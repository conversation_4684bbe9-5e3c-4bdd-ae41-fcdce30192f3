{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-overlap/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,kCAA4B;AAC5B,4CAAwB;AACxB,iDAA4B;AAC5B,qDAA8B;AAC9B,wDAAgC;AAuBhC,SAAS,cAAA,CACP,QAAA,EACA,QAAA,EACS;AACT,EAAA,MAAM,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC9B,EAAA,MAAM,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC9B,EAAA,MAAM,MAAA,EAAQ,KAAA,CAAM,IAAA;AACpB,EAAA,MAAM,MAAA,EAAQ,KAAA,CAAM,IAAA;AAEpB,EAAA,GAAA,CACG,MAAA,IAAU,aAAA,GAAgB,MAAA,IAAU,aAAA,GAAA,CACnC,MAAA,IAAU,aAAA,GAAgB,MAAA,IAAU,iBAAA,EAAA,GACpC,MAAA,IAAU,aAAA,GACV,MAAA,IAAU,kBAAA,GAAA,CACV,MAAA,IAAU,UAAA,GAAa,MAAA,IAAU,cAAA,EAAA,GACjC,MAAA,IAAU,UAAA,GACV,MAAA,IAAU,cAAA,EACZ;AACA,IAAA,MAAM,IAAI,KAAA,CAAM,mCAAmC,CAAA;AAAA,EACrD;AACA,EAAA,GAAA,CAAI,MAAA,IAAU,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,8BAA8B,CAAA;AAGrE,EAAA,GAAA,CAAI,gDAAA,QAAgB,EAAiB,QAAA,EAAiB,EAAE,SAAA,EAAW,EAAE,CAAC,CAAA;AACpE,IAAA,OAAO,KAAA;AAET,EAAA,IAAI,QAAA,EAAU,CAAA;AAEd,EAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,IACb,KAAK,YAAA;AACH,MAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAK,KAAA,CAAqB,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjE,QAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAK,KAAA,CAAqB,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACjE,UAAA,IAAI,OAAA,EAAS,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA;AAChC,UAAA,IAAI,OAAA,EAAS,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA;AAChC,UAAA,GAAA,CAAI,MAAA,CAAO,CAAC,EAAA,IAAM,MAAA,CAAO,CAAC,EAAA,GAAK,MAAA,CAAO,CAAC,EAAA,IAAM,MAAA,CAAO,CAAC,CAAA,EAAG;AACtD,YAAA,OAAO,IAAA;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,MAAA,OAAO,KAAA;AAAA,IAET,KAAK,YAAA;AAAA,IACL,KAAK,iBAAA;AACH,MAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,QAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,UAAA,GAAA,CAAI,sCAAA,QAAY,EAAW,QAAS,CAAA,CAAE,QAAA,CAAS,MAAA,EAAQ,OAAA,EAAA;AAAA,QACzD,CAAC,CAAA;AAAA,MACH,CAAC,CAAA;AACD,MAAA,KAAA;AAAA,IAEF,KAAK,SAAA;AAAA,IACL,KAAK,cAAA;AACH,MAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,QAAA,+BAAA,QAAY,EAAU,CAAC,QAAA,EAAA,GAAa;AAClC,UAAA,GAAA,CAAI,0CAAA,QAAc,EAAW,QAAS,CAAA,CAAE,QAAA,CAAS,MAAA,EAAQ,OAAA,EAAA;AAAA,QAC3D,CAAC,CAAA;AAAA,MACH,CAAC,CAAA;AACD,MAAA,KAAA;AAAA,EACJ;AAEA,EAAA,OAAO,QAAA,EAAU,CAAA;AACnB;AAGA,IAAO,6BAAA,EAAQ,cAAA;AD1Cf;AACE;AACA;AACF,wFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-overlap/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Geometry, MultiPoint } from \"geojson\";\nimport { segmentEach } from \"@turf/meta\";\nimport { getGeom } from \"@turf/invariant\";\nimport { lineOverlap } from \"@turf/line-overlap\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { geojsonEquality } from \"geojson-equality-ts\";\n\n/**\n * Compares two geometries of the same dimension and returns true if their intersection set results in a geometry\n * different from both but of the same dimension. It applies to Polygon/Polygon, LineString/LineString,\n * Multipoint/Multipoint, MultiLineString/MultiLineString and MultiPolygon/MultiPolygon.\n *\n * In other words, it returns true if the two geometries overlap, provided that neither completely contains the other.\n *\n * @function\n * @param  {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} feature1 input\n * @param  {Geometry|Feature<LineString|MultiLineString|Polygon|MultiPolygon>} feature2 input\n * @returns {boolean} true/false\n * @example\n * var poly1 = turf.polygon([[[0,0],[0,5],[5,5],[5,0],[0,0]]]);\n * var poly2 = turf.polygon([[[1,1],[1,6],[6,6],[6,1],[1,1]]]);\n * var poly3 = turf.polygon([[[10,10],[10,15],[15,15],[15,10],[10,10]]]);\n *\n * turf.booleanOverlap(poly1, poly2)\n * //=true\n * turf.booleanOverlap(poly2, poly3)\n * //=false\n */\nfunction booleanOverlap(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  const geom1 = getGeom(feature1);\n  const geom2 = getGeom(feature2);\n  const type1 = geom1.type;\n  const type2 = geom2.type;\n\n  if (\n    (type1 === \"MultiPoint\" && type2 !== \"MultiPoint\") ||\n    ((type1 === \"LineString\" || type1 === \"MultiLineString\") &&\n      type2 !== \"LineString\" &&\n      type2 !== \"MultiLineString\") ||\n    ((type1 === \"Polygon\" || type1 === \"MultiPolygon\") &&\n      type2 !== \"Polygon\" &&\n      type2 !== \"MultiPolygon\")\n  ) {\n    throw new Error(\"features must be of the same type\");\n  }\n  if (type1 === \"Point\") throw new Error(\"Point geometry not supported\");\n\n  // features must be not equal\n  if (geojsonEquality(feature1 as any, feature2 as any, { precision: 6 }))\n    return false;\n\n  let overlap = 0;\n\n  switch (type1) {\n    case \"MultiPoint\":\n      for (var i = 0; i < (geom1 as MultiPoint).coordinates.length; i++) {\n        for (var j = 0; j < (geom2 as MultiPoint).coordinates.length; j++) {\n          var coord1 = geom1.coordinates[i];\n          var coord2 = geom2.coordinates[j];\n          if (coord1[0] === coord2[0] && coord1[1] === coord2[1]) {\n            return true;\n          }\n        }\n      }\n      return false;\n\n    case \"LineString\":\n    case \"MultiLineString\":\n      segmentEach(feature1, (segment1) => {\n        segmentEach(feature2, (segment2) => {\n          if (lineOverlap(segment1!, segment2!).features.length) overlap++;\n        });\n      });\n      break;\n\n    case \"Polygon\":\n    case \"MultiPolygon\":\n      segmentEach(feature1, (segment1) => {\n        segmentEach(feature2, (segment2) => {\n          if (lineIntersect(segment1!, segment2!).features.length) overlap++;\n        });\n      });\n      break;\n  }\n\n  return overlap > 0;\n}\n\nexport { booleanOverlap };\nexport default booleanOverlap;\n"]}