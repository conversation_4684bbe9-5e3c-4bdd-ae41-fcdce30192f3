{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-point-on-line/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACEA,4CAAoC;AAmBpC,SAAS,kBAAA,CACP,EAAA,EACA,IAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACI;AAET,EAAA,MAAM,SAAA,EAAW,iCAAA,EAAW,CAAA;AAC5B,EAAA,MAAM,WAAA,EAAa,kCAAA,IAAc,CAAA;AAGjC,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AAC9C,IAAA,IAAI,eAAA,EAAmC,KAAA;AACvC,IAAA,GAAA,CAAI,OAAA,CAAQ,iBAAA,EAAmB;AAC7B,MAAA,GAAA,CAAI,EAAA,IAAM,CAAA,EAAG;AACX,QAAA,eAAA,EAAiB,OAAA;AAAA,MACnB;AACA,MAAA,GAAA,CAAI,EAAA,IAAM,UAAA,CAAW,OAAA,EAAS,CAAA,EAAG;AAC/B,QAAA,eAAA,EAAiB,KAAA;AAAA,MACnB;AACA,MAAA,GAAA,CAAI,EAAA,IAAM,EAAA,GAAK,EAAA,EAAI,EAAA,IAAM,UAAA,CAAW,OAAA,EAAS,CAAA,EAAG;AAC9C,QAAA,eAAA,EAAiB,MAAA;AAAA,MACnB;AAAA,IACF;AACA,IAAA,GAAA,CACE,oBAAA;AAAA,MACE,UAAA,CAAW,CAAC,CAAA;AAAA,MACZ,UAAA,CAAW,EAAA,EAAI,CAAC,CAAA;AAAA,MAChB,QAAA;AAAA,MACA,cAAA;AAAA,MACA,OAAO,OAAA,CAAQ,QAAA,IAAY,YAAA,EAAc,KAAA,EAAO,OAAA,CAAQ;AAAA,IAC1D,CAAA,EACA;AACA,MAAA,OAAO,IAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,KAAA;AACT;AAcA,SAAS,oBAAA,CACP,gBAAA,EACA,cAAA,EACA,EAAA,EACA,eAAA,EACA,OAAA,EACS;AACT,EAAA,MAAM,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA;AACd,EAAA,MAAM,EAAA,EAAI,EAAA,CAAG,CAAC,CAAA;AACd,EAAA,MAAM,GAAA,EAAK,gBAAA,CAAiB,CAAC,CAAA;AAC7B,EAAA,MAAM,GAAA,EAAK,gBAAA,CAAiB,CAAC,CAAA;AAC7B,EAAA,MAAM,GAAA,EAAK,cAAA,CAAe,CAAC,CAAA;AAC3B,EAAA,MAAM,GAAA,EAAK,cAAA,CAAe,CAAC,CAAA;AAC3B,EAAA,MAAM,IAAA,EAAM,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA;AACpB,EAAA,MAAM,IAAA,EAAM,EAAA,CAAG,CAAC,EAAA,EAAI,EAAA;AACpB,EAAA,MAAM,IAAA,EAAM,GAAA,EAAK,EAAA;AACjB,EAAA,MAAM,IAAA,EAAM,GAAA,EAAK,EAAA;AACjB,EAAA,MAAM,MAAA,EAAQ,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,GAAA;AAChC,EAAA,GAAA,CAAI,QAAA,IAAY,IAAA,EAAM;AACpB,IAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,KAAK,EAAA,EAAI,OAAA,EAAS;AAC7B,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF,EAAA,KAAA,GAAA,CAAW,MAAA,IAAU,CAAA,EAAG;AACtB,IAAA,OAAO,KAAA;AAAA,EACT;AAIA,EAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,IAAM,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,IAAM,CAAA,EAAG;AAE1D,IAAA,GAAA,CAAI,eAAA,EAAiB;AAGnB,MAAA,OAAO,KAAA;AAAA,IACT;AACA,IAAA,GAAA,CAAI,EAAA,CAAG,CAAC,EAAA,IAAM,gBAAA,CAAiB,CAAC,EAAA,GAAK,EAAA,CAAG,CAAC,EAAA,IAAM,gBAAA,CAAiB,CAAC,CAAA,EAAG;AAElE,MAAA,OAAO,IAAA;AAAA,IACT,EAAA,KAAO;AAEL,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AAEA,EAAA,GAAA,CAAI,CAAC,eAAA,EAAiB;AACpB,IAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA,EAAG;AAClC,MAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,GAAM,EAAA,GAAK,EAAA,GAAK,GAAA,EAAK,GAAA,GAAM,EAAA,GAAK,EAAA,GAAK,EAAA;AAAA,IACxD;AACA,IAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,GAAM,EAAA,GAAK,EAAA,GAAK,GAAA,EAAK,GAAA,GAAM,EAAA,GAAK,EAAA,GAAK,EAAA;AAAA,EACxD,EAAA,KAAA,GAAA,CAAW,gBAAA,IAAoB,OAAA,EAAS;AACtC,IAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA,EAAG;AAClC,MAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,EAAK,EAAA,GAAK,EAAA,GAAK,GAAA,EAAK,GAAA,GAAM,EAAA,GAAK,EAAA,EAAI,EAAA;AAAA,IACtD;AACA,IAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,EAAK,EAAA,GAAK,EAAA,GAAK,GAAA,EAAK,GAAA,GAAM,EAAA,GAAK,EAAA,EAAI,EAAA;AAAA,EACtD,EAAA,KAAA,GAAA,CAAW,gBAAA,IAAoB,KAAA,EAAO;AACpC,IAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA,EAAG;AAClC,MAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,GAAM,EAAA,GAAK,EAAA,EAAI,GAAA,EAAK,GAAA,EAAK,EAAA,GAAK,EAAA,GAAK,EAAA;AAAA,IACtD;AACA,IAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,GAAM,EAAA,GAAK,EAAA,EAAI,GAAA,EAAK,GAAA,EAAK,EAAA,GAAK,EAAA,GAAK,EAAA;AAAA,EACtD,EAAA,KAAA,GAAA,CAAW,gBAAA,IAAoB,MAAA,EAAQ;AACrC,IAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAA,GAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAA,EAAG;AAClC,MAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,EAAK,EAAA,GAAK,EAAA,EAAI,GAAA,EAAK,GAAA,EAAK,EAAA,GAAK,EAAA,EAAI,EAAA;AAAA,IACpD;AACA,IAAA,OAAO,IAAA,EAAM,EAAA,EAAI,GAAA,EAAK,EAAA,GAAK,EAAA,EAAI,GAAA,EAAK,GAAA,EAAK,EAAA,GAAK,EAAA,EAAI,EAAA;AAAA,EACpD;AACA,EAAA,OAAO,KAAA;AACT;AAGA,IAAO,mCAAA,EAAQ,kBAAA;AD5Df;AACE;AACA;AACF,sGAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-point-on-line/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, LineString } from \"geojson\";\nimport { Coord } from \"@turf/helpers\";\nimport { getCoord, getCoords } from \"@turf/invariant\";\n\n/**\n * Returns true if a point is on a line. Accepts a optional parameter to ignore the\n * start and end vertices of the linestring.\n *\n * @function\n * @param {Coord} pt GeoJSON Point\n * @param {Feature<LineString>} line GeoJSON LineString\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.ignoreEndVertices=false] whether to ignore the start and end vertices.\n * @param {number} [options.epsilon] Fractional number to compare with the cross product result. Useful for dealing with floating points such as lng/lat points\n * @returns {boolean} true/false\n * @example\n * var pt = turf.point([0, 0]);\n * var line = turf.lineString([[-1, -1],[1, 1],[1.5, 2.2]]);\n * var isPointOnLine = turf.booleanPointOnLine(pt, line);\n * //=true\n */\nfunction booleanPointOnLine(\n  pt: Coord,\n  line: Feature<LineString> | LineString,\n  options: {\n    ignoreEndVertices?: boolean;\n    epsilon?: number;\n  } = {}\n): boolean {\n  // Normalize inputs\n  const ptCoords = getCoord(pt);\n  const lineCoords = getCoords(line);\n\n  // Main\n  for (let i = 0; i < lineCoords.length - 1; i++) {\n    let ignoreBoundary: boolean | string = false;\n    if (options.ignoreEndVertices) {\n      if (i === 0) {\n        ignoreBoundary = \"start\";\n      }\n      if (i === lineCoords.length - 2) {\n        ignoreBoundary = \"end\";\n      }\n      if (i === 0 && i + 1 === lineCoords.length - 1) {\n        ignoreBoundary = \"both\";\n      }\n    }\n    if (\n      isPointOnLineSegment(\n        lineCoords[i],\n        lineCoords[i + 1],\n        ptCoords,\n        ignoreBoundary,\n        typeof options.epsilon === \"undefined\" ? null : options.epsilon\n      )\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// See http://stackoverflow.com/a/4833823/1979085\n// See https://stackoverflow.com/a/328122/1048847\n/**\n * @private\n * @param {Position} lineSegmentStart coord pair of start of line\n * @param {Position} lineSegmentEnd coord pair of end of line\n * @param {Position} pt coord pair of point to check\n * @param {boolean|string} excludeBoundary whether the point is allowed to fall on the line ends.\n * @param {number} epsilon Fractional number to compare with the cross product result. Useful for dealing with floating points such as lng/lat points\n * If true which end to ignore.\n * @returns {boolean} true/false\n */\nfunction isPointOnLineSegment(\n  lineSegmentStart: number[],\n  lineSegmentEnd: number[],\n  pt: number[],\n  excludeBoundary: string | boolean,\n  epsilon: number | null\n): boolean {\n  const x = pt[0];\n  const y = pt[1];\n  const x1 = lineSegmentStart[0];\n  const y1 = lineSegmentStart[1];\n  const x2 = lineSegmentEnd[0];\n  const y2 = lineSegmentEnd[1];\n  const dxc = pt[0] - x1;\n  const dyc = pt[1] - y1;\n  const dxl = x2 - x1;\n  const dyl = y2 - y1;\n  const cross = dxc * dyl - dyc * dxl;\n  if (epsilon !== null) {\n    if (Math.abs(cross) > epsilon) {\n      return false;\n    }\n  } else if (cross !== 0) {\n    return false;\n  }\n\n  // Special cases for zero length lines\n  // https://github.com/Turfjs/turf/issues/2750\n  if (Math.abs(dxl) === Math.abs(dyl) && Math.abs(dxl) === 0) {\n    // Zero length line.\n    if (excludeBoundary) {\n      // To be on a zero length line pt has to be on the start (and end), BUT we\n      // are excluding start and end from possible matches.\n      return false;\n    }\n    if (pt[0] === lineSegmentStart[0] && pt[1] === lineSegmentStart[1]) {\n      // If point is same as start (and end) it's on the line segment\n      return true;\n    } else {\n      // Otherwise point is somewhere else\n      return false;\n    }\n  }\n\n  if (!excludeBoundary) {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 <= x && x <= x2 : x2 <= x && x <= x1;\n    }\n    return dyl > 0 ? y1 <= y && y <= y2 : y2 <= y && y <= y1;\n  } else if (excludeBoundary === \"start\") {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 < x && x <= x2 : x2 <= x && x < x1;\n    }\n    return dyl > 0 ? y1 < y && y <= y2 : y2 <= y && y < y1;\n  } else if (excludeBoundary === \"end\") {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 <= x && x < x2 : x2 < x && x <= x1;\n    }\n    return dyl > 0 ? y1 <= y && y < y2 : y2 < y && y <= y1;\n  } else if (excludeBoundary === \"both\") {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 < x && x < x2 : x2 < x && x < x1;\n    }\n    return dyl > 0 ? y1 < y && y < y2 : y2 < y && y < y1;\n  }\n  return false;\n}\n\nexport { booleanPointOnLine };\nexport default booleanPointOnLine;\n"]}