{"version": 3, "sources": ["../../index.js", "../../lib/arc.js"], "sourcesContent": ["import { lineString } from \"@turf/helpers\";\nimport { getCoord } from \"@turf/invariant\";\nimport { GreatCircle } from \"./lib/arc.js\";\n\n/**\n * Calculate great circles routes as {@link LineString} or {@link MultiLineString}.\n * If the `start` and `end` points span the antimeridian, the resulting feature will\n * be split into a `MultiLineString`. If the `start` and `end` positions are the same\n * then a `LineString` will be returned with duplicate coordinates the length of the `npoints` option.\n *\n * @function\n * @param {Coord} start source point feature\n * @param {Coord} end destination point feature\n * @param {Object} [options={}] Optional parameters\n * @param {Object} [options.properties={}] line feature properties\n * @param {number} [options.npoints=100] number of points\n * @param {number} [options.offset=10] offset controls the likelyhood that lines will\n * be split which cross the dateline. The higher the number the more likely.\n * @returns {Feature<LineString | MultiLineString>} great circle line feature\n * @example\n * var start = turf.point([-122, 48]);\n * var end = turf.point([-77, 39]);\n *\n * var greatCircle = turf.greatCircle(start, end, {properties: {name: 'Seattle to DC'}});\n *\n * //addToMap\n * var addToMap = [start, end, greatCircle]\n */\nfunction greatCircle(start, end, options) {\n  // Optional parameters\n  options = options || {};\n  if (typeof options !== \"object\") throw new Error(\"options is invalid\");\n  var properties = options.properties;\n  var npoints = options.npoints;\n  var offset = options.offset;\n\n  start = getCoord(start);\n  end = getCoord(end);\n\n  properties = properties || {};\n  npoints = npoints || 100;\n\n  if (start[0] === end[0] && start[1] === end[1]) {\n    const arr = Array(npoints);\n    arr.fill([start[0], start[1]]);\n    return lineString(arr, properties);\n  }\n\n  offset = offset || 10;\n\n  var generator = new GreatCircle(\n    { x: start[0], y: start[1] },\n    { x: end[0], y: end[1] },\n    properties\n  );\n\n  var line = generator.Arc(npoints, { offset: offset });\n\n  return line.json();\n}\n\nexport { greatCircle };\nexport default greatCircle;\n", "/*!\n * Copyright (c) 2019, <PERSON>\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *     * Redistributions of source code must retain the above copyright\n *       notice, this list of conditions and the following disclaimer.\n *     * Redistributions in binary form must reproduce the above copyright\n *       notice, this list of conditions and the following disclaimer in\n *       the documentation and/or other materials provided with the\n *       distribution.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS\n * IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,\n * THE IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON>ABILITY AND FITNESS FOR A PARTICULAR\n * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR\n * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\n * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\n * PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\n * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\nvar D2R = Math.PI / 180;\nvar R2D = 180 / Math.PI;\n\nvar Coord = function (lon, lat) {\n  this.lon = lon;\n  this.lat = lat;\n  this.x = D2R * lon;\n  this.y = D2R * lat;\n};\n\nCoord.prototype.view = function () {\n  return String(this.lon).slice(0, 4) + \",\" + String(this.lat).slice(0, 4);\n};\n\nCoord.prototype.antipode = function () {\n  var anti_lat = -1 * this.lat;\n  var anti_lon = this.lon < 0 ? 180 + this.lon : (180 - this.lon) * -1;\n  return new Coord(anti_lon, anti_lat);\n};\n\nvar LineString = function () {\n  this.coords = [];\n  this.length = 0;\n};\n\nLineString.prototype.move_to = function (coord) {\n  this.length++;\n  this.coords.push(coord);\n};\n\nvar Arc = function (properties) {\n  this.properties = properties || {};\n  this.geometries = [];\n};\n\nArc.prototype.json = function () {\n  if (this.geometries.length <= 0) {\n    return {\n      geometry: { type: \"LineString\", coordinates: null },\n      type: \"Feature\",\n      properties: this.properties,\n    };\n  } else if (this.geometries.length === 1) {\n    return {\n      geometry: { type: \"LineString\", coordinates: this.geometries[0].coords },\n      type: \"Feature\",\n      properties: this.properties,\n    };\n  } else {\n    var multiline = [];\n    for (var i = 0; i < this.geometries.length; i++) {\n      multiline.push(this.geometries[i].coords);\n    }\n    return {\n      geometry: { type: \"MultiLineString\", coordinates: multiline },\n      type: \"Feature\",\n      properties: this.properties,\n    };\n  }\n};\n\n// TODO - output proper multilinestring\nArc.prototype.wkt = function () {\n  var wkt_string = \"\";\n  var wkt = \"LINESTRING(\";\n  var collect = function (c) {\n    wkt += c[0] + \" \" + c[1] + \",\";\n  };\n  for (var i = 0; i < this.geometries.length; i++) {\n    if (this.geometries[i].coords.length === 0) {\n      return \"LINESTRING(empty)\";\n    } else {\n      var coords = this.geometries[i].coords;\n      coords.forEach(collect);\n      wkt_string += wkt.substring(0, wkt.length - 1) + \")\";\n    }\n  }\n  return wkt_string;\n};\n\n/*\n * http://en.wikipedia.org/wiki/Great-circle_distance\n *\n */\nvar GreatCircle = function (start, end, properties) {\n  if (!start || start.x === undefined || start.y === undefined) {\n    throw new Error(\n      \"GreatCircle constructor expects two args: start and end objects with x and y properties\"\n    );\n  }\n  if (!end || end.x === undefined || end.y === undefined) {\n    throw new Error(\n      \"GreatCircle constructor expects two args: start and end objects with x and y properties\"\n    );\n  }\n  this.start = new Coord(start.x, start.y);\n  this.end = new Coord(end.x, end.y);\n  this.properties = properties || {};\n\n  var w = this.start.x - this.end.x;\n  var h = this.start.y - this.end.y;\n  var z =\n    Math.pow(Math.sin(h / 2.0), 2) +\n    Math.cos(this.start.y) *\n      Math.cos(this.end.y) *\n      Math.pow(Math.sin(w / 2.0), 2);\n  this.g = 2.0 * Math.asin(Math.sqrt(z));\n\n  if (this.g === Math.PI) {\n    throw new Error(\n      \"it appears \" +\n        start.view() +\n        \" and \" +\n        end.view() +\n        \" are 'antipodal', e.g diametrically opposite, thus there is no single route but rather infinite\"\n    );\n  } else if (isNaN(this.g)) {\n    throw new Error(\n      \"could not calculate great circle between \" + start + \" and \" + end\n    );\n  }\n};\n\n/*\n * http://williams.best.vwh.net/avform.htm#Intermediate\n */\nGreatCircle.prototype.interpolate = function (f) {\n  var A = Math.sin((1 - f) * this.g) / Math.sin(this.g);\n  var B = Math.sin(f * this.g) / Math.sin(this.g);\n  var x =\n    A * Math.cos(this.start.y) * Math.cos(this.start.x) +\n    B * Math.cos(this.end.y) * Math.cos(this.end.x);\n  var y =\n    A * Math.cos(this.start.y) * Math.sin(this.start.x) +\n    B * Math.cos(this.end.y) * Math.sin(this.end.x);\n  var z = A * Math.sin(this.start.y) + B * Math.sin(this.end.y);\n  var lat = R2D * Math.atan2(z, Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2)));\n  var lon = R2D * Math.atan2(y, x);\n  return [lon, lat];\n};\n\n/*\n * Generate points along the great circle\n */\nGreatCircle.prototype.Arc = function (npoints, options) {\n  var first_pass = [];\n  if (!npoints || npoints <= 2) {\n    first_pass.push([this.start.lon, this.start.lat]);\n    first_pass.push([this.end.lon, this.end.lat]);\n  } else {\n    var delta = 1.0 / (npoints - 1);\n    for (var i = 0; i < npoints; ++i) {\n      var step = delta * i;\n      var pair = this.interpolate(step);\n      first_pass.push(pair);\n    }\n  }\n  /* partial port of dateline handling from:\n      gdal/ogr/ogrgeometryfactory.cpp\n\n      TODO - does not handle all wrapping scenarios yet\n    */\n  var bHasBigDiff = false;\n  var dfMaxSmallDiffLong = 0;\n  // from http://www.gdal.org/ogr2ogr.html\n  // -datelineoffset:\n  // (starting with GDAL 1.10) offset from dateline in degrees (default long. = +/- 10deg, geometries within 170deg to -170deg will be splited)\n  var dfDateLineOffset = options && options.offset ? options.offset : 10;\n  var dfLeftBorderX = 180 - dfDateLineOffset;\n  var dfRightBorderX = -180 + dfDateLineOffset;\n  var dfDiffSpace = 360 - dfDateLineOffset;\n\n  // https://github.com/OSGeo/gdal/blob/7bfb9c452a59aac958bff0c8386b891edf8154ca/gdal/ogr/ogrgeometryfactory.cpp#L2342\n  for (var j = 1; j < first_pass.length; ++j) {\n    var dfPrevX = first_pass[j - 1][0];\n    var dfX = first_pass[j][0];\n    var dfDiffLong = Math.abs(dfX - dfPrevX);\n    if (\n      dfDiffLong > dfDiffSpace &&\n      ((dfX > dfLeftBorderX && dfPrevX < dfRightBorderX) ||\n        (dfPrevX > dfLeftBorderX && dfX < dfRightBorderX))\n    ) {\n      bHasBigDiff = true;\n    } else if (dfDiffLong > dfMaxSmallDiffLong) {\n      dfMaxSmallDiffLong = dfDiffLong;\n    }\n  }\n\n  var poMulti = [];\n  if (bHasBigDiff && dfMaxSmallDiffLong < dfDateLineOffset) {\n    var poNewLS = [];\n    poMulti.push(poNewLS);\n    for (var k = 0; k < first_pass.length; ++k) {\n      var dfX0 = parseFloat(first_pass[k][0]);\n      if (k > 0 && Math.abs(dfX0 - first_pass[k - 1][0]) > dfDiffSpace) {\n        var dfX1 = parseFloat(first_pass[k - 1][0]);\n        var dfY1 = parseFloat(first_pass[k - 1][1]);\n        var dfX2 = parseFloat(first_pass[k][0]);\n        var dfY2 = parseFloat(first_pass[k][1]);\n        if (\n          dfX1 > -180 &&\n          dfX1 < dfRightBorderX &&\n          dfX2 === 180 &&\n          k + 1 < first_pass.length &&\n          first_pass[k - 1][0] > -180 &&\n          first_pass[k - 1][0] < dfRightBorderX\n        ) {\n          poNewLS.push([-180, first_pass[k][1]]);\n          k++;\n          poNewLS.push([first_pass[k][0], first_pass[k][1]]);\n          continue;\n        } else if (\n          dfX1 > dfLeftBorderX &&\n          dfX1 < 180 &&\n          dfX2 === -180 &&\n          k + 1 < first_pass.length &&\n          first_pass[k - 1][0] > dfLeftBorderX &&\n          first_pass[k - 1][0] < 180\n        ) {\n          poNewLS.push([180, first_pass[k][1]]);\n          k++;\n          poNewLS.push([first_pass[k][0], first_pass[k][1]]);\n          continue;\n        }\n\n        if (dfX1 < dfRightBorderX && dfX2 > dfLeftBorderX) {\n          // swap dfX1, dfX2\n          var tmpX = dfX1;\n          dfX1 = dfX2;\n          dfX2 = tmpX;\n          // swap dfY1, dfY2\n          var tmpY = dfY1;\n          dfY1 = dfY2;\n          dfY2 = tmpY;\n        }\n        if (dfX1 > dfLeftBorderX && dfX2 < dfRightBorderX) {\n          dfX2 += 360;\n        }\n\n        if (dfX1 <= 180 && dfX2 >= 180 && dfX1 < dfX2) {\n          var dfRatio = (180 - dfX1) / (dfX2 - dfX1);\n          var dfY = dfRatio * dfY2 + (1 - dfRatio) * dfY1;\n          poNewLS.push([\n            first_pass[k - 1][0] > dfLeftBorderX ? 180 : -180,\n            dfY,\n          ]);\n          poNewLS = [];\n          poNewLS.push([\n            first_pass[k - 1][0] > dfLeftBorderX ? -180 : 180,\n            dfY,\n          ]);\n          poMulti.push(poNewLS);\n        } else {\n          poNewLS = [];\n          poMulti.push(poNewLS);\n        }\n        poNewLS.push([dfX0, first_pass[k][1]]);\n      } else {\n        poNewLS.push([first_pass[k][0], first_pass[k][1]]);\n      }\n    }\n  } else {\n    // add normally\n    var poNewLS0 = [];\n    poMulti.push(poNewLS0);\n    for (var l = 0; l < first_pass.length; ++l) {\n      poNewLS0.push([first_pass[l][0], first_pass[l][1]]);\n    }\n  }\n\n  var arc = new Arc(this.properties);\n  for (var m = 0; m < poMulti.length; ++m) {\n    var line = new LineString();\n    arc.geometries.push(line);\n    var points = poMulti[m];\n    for (var j0 = 0; j0 < points.length; ++j0) {\n      line.move_to(points[j0]);\n    }\n  }\n  return arc;\n};\n\nexport { Coord, Arc, GreatCircle };\n\nexport default {\n  Coord,\n  Arc,\n  GreatCircle,\n};\n"], "mappings": ";AAAA,SAAS,kBAAkB;AAC3B,SAAS,gBAAgB;;;ACyBzB,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,MAAM,MAAM,KAAK;AAErB,IAAI,QAAQ,SAAU,KAAK,KAAK;AAC9B,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,IAAI,MAAM;AACf,OAAK,IAAI,MAAM;AACjB;AAEA,MAAM,UAAU,OAAO,WAAY;AACjC,SAAO,OAAO,KAAK,GAAG,EAAE,MAAM,GAAG,CAAC,IAAI,MAAM,OAAO,KAAK,GAAG,EAAE,MAAM,GAAG,CAAC;AACzE;AAEA,MAAM,UAAU,WAAW,WAAY;AACrC,MAAI,WAAW,KAAK,KAAK;AACzB,MAAI,WAAW,KAAK,MAAM,IAAI,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO;AAClE,SAAO,IAAI,MAAM,UAAU,QAAQ;AACrC;AAEA,IAAI,aAAa,WAAY;AAC3B,OAAK,SAAS,CAAC;AACf,OAAK,SAAS;AAChB;AAEA,WAAW,UAAU,UAAU,SAAU,OAAO;AAC9C,OAAK;AACL,OAAK,OAAO,KAAK,KAAK;AACxB;AAEA,IAAI,MAAM,SAAU,YAAY;AAC9B,OAAK,aAAa,cAAc,CAAC;AACjC,OAAK,aAAa,CAAC;AACrB;AAEA,IAAI,UAAU,OAAO,WAAY;AAC/B,MAAI,KAAK,WAAW,UAAU,GAAG;AAC/B,WAAO;AAAA,MACL,UAAU,EAAE,MAAM,cAAc,aAAa,KAAK;AAAA,MAClD,MAAM;AAAA,MACN,YAAY,KAAK;AAAA,IACnB;AAAA,EACF,WAAW,KAAK,WAAW,WAAW,GAAG;AACvC,WAAO;AAAA,MACL,UAAU,EAAE,MAAM,cAAc,aAAa,KAAK,WAAW,CAAC,EAAE,OAAO;AAAA,MACvE,MAAM;AAAA,MACN,YAAY,KAAK;AAAA,IACnB;AAAA,EACF,OAAO;AACL,QAAI,YAAY,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,gBAAU,KAAK,KAAK,WAAW,CAAC,EAAE,MAAM;AAAA,IAC1C;AACA,WAAO;AAAA,MACL,UAAU,EAAE,MAAM,mBAAmB,aAAa,UAAU;AAAA,MAC5D,MAAM;AAAA,MACN,YAAY,KAAK;AAAA,IACnB;AAAA,EACF;AACF;AAGA,IAAI,UAAU,MAAM,WAAY;AAC9B,MAAI,aAAa;AACjB,MAAI,MAAM;AACV,MAAI,UAAU,SAAU,GAAG;AACzB,WAAO,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,EAC7B;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,QAAI,KAAK,WAAW,CAAC,EAAE,OAAO,WAAW,GAAG;AAC1C,aAAO;AAAA,IACT,OAAO;AACL,UAAI,SAAS,KAAK,WAAW,CAAC,EAAE;AAChC,aAAO,QAAQ,OAAO;AACtB,oBAAc,IAAI,UAAU,GAAG,IAAI,SAAS,CAAC,IAAI;AAAA,IACnD;AAAA,EACF;AACA,SAAO;AACT;AAMA,IAAI,cAAc,SAAU,OAAO,KAAK,YAAY;AAClD,MAAI,CAAC,SAAS,MAAM,MAAM,UAAa,MAAM,MAAM,QAAW;AAC5D,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,OAAO,IAAI,MAAM,UAAa,IAAI,MAAM,QAAW;AACtD,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,OAAK,QAAQ,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC;AACvC,OAAK,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC;AACjC,OAAK,aAAa,cAAc,CAAC;AAEjC,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AAChC,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AAChC,MAAI,IACF,KAAK,IAAI,KAAK,IAAI,IAAI,CAAG,GAAG,CAAC,IAC7B,KAAK,IAAI,KAAK,MAAM,CAAC,IACnB,KAAK,IAAI,KAAK,IAAI,CAAC,IACnB,KAAK,IAAI,KAAK,IAAI,IAAI,CAAG,GAAG,CAAC;AACjC,OAAK,IAAI,IAAM,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC;AAErC,MAAI,KAAK,MAAM,KAAK,IAAI;AACtB,UAAM,IAAI;AAAA,MACR,gBACE,MAAM,KAAK,IACX,UACA,IAAI,KAAK,IACT;AAAA,IACJ;AAAA,EACF,WAAW,MAAM,KAAK,CAAC,GAAG;AACxB,UAAM,IAAI;AAAA,MACR,8CAA8C,QAAQ,UAAU;AAAA,IAClE;AAAA,EACF;AACF;AAKA,YAAY,UAAU,cAAc,SAAU,GAAG;AAC/C,MAAI,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,MAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC;AAC9C,MAAI,IACF,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,IAClD,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC;AAChD,MAAI,IACF,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,IAClD,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC;AAChD,MAAI,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC;AAC5D,MAAI,MAAM,MAAM,KAAK,MAAM,GAAG,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;AACxE,MAAI,MAAM,MAAM,KAAK,MAAM,GAAG,CAAC;AAC/B,SAAO,CAAC,KAAK,GAAG;AAClB;AAKA,YAAY,UAAU,MAAM,SAAU,SAAS,SAAS;AACtD,MAAI,aAAa,CAAC;AAClB,MAAI,CAAC,WAAW,WAAW,GAAG;AAC5B,eAAW,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,GAAG,CAAC;AAChD,eAAW,KAAK,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AAAA,EAC9C,OAAO;AACL,QAAI,QAAQ,KAAO,UAAU;AAC7B,aAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,UAAI,OAAO,QAAQ;AACnB,UAAI,OAAO,KAAK,YAAY,IAAI;AAChC,iBAAW,KAAK,IAAI;AAAA,IACtB;AAAA,EACF;AAMA,MAAI,cAAc;AAClB,MAAI,qBAAqB;AAIzB,MAAI,mBAAmB,WAAW,QAAQ,SAAS,QAAQ,SAAS;AACpE,MAAI,gBAAgB,MAAM;AAC1B,MAAI,iBAAiB,OAAO;AAC5B,MAAI,cAAc,MAAM;AAGxB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,QAAI,UAAU,WAAW,IAAI,CAAC,EAAE,CAAC;AACjC,QAAI,MAAM,WAAW,CAAC,EAAE,CAAC;AACzB,QAAI,aAAa,KAAK,IAAI,MAAM,OAAO;AACvC,QACE,aAAa,gBACX,MAAM,iBAAiB,UAAU,kBAChC,UAAU,iBAAiB,MAAM,iBACpC;AACA,oBAAc;AAAA,IAChB,WAAW,aAAa,oBAAoB;AAC1C,2BAAqB;AAAA,IACvB;AAAA,EACF;AAEA,MAAI,UAAU,CAAC;AACf,MAAI,eAAe,qBAAqB,kBAAkB;AACxD,QAAI,UAAU,CAAC;AACf,YAAQ,KAAK,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,UAAI,OAAO,WAAW,WAAW,CAAC,EAAE,CAAC,CAAC;AACtC,UAAI,IAAI,KAAK,KAAK,IAAI,OAAO,WAAW,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,aAAa;AAChE,YAAI,OAAO,WAAW,WAAW,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1C,YAAI,OAAO,WAAW,WAAW,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1C,YAAI,OAAO,WAAW,WAAW,CAAC,EAAE,CAAC,CAAC;AACtC,YAAI,OAAO,WAAW,WAAW,CAAC,EAAE,CAAC,CAAC;AACtC,YACE,OAAO,QACP,OAAO,kBACP,SAAS,OACT,IAAI,IAAI,WAAW,UACnB,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,QACvB,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,gBACvB;AACA,kBAAQ,KAAK,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC;AACA,kBAAQ,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD;AAAA,QACF,WACE,OAAO,iBACP,OAAO,OACP,SAAS,QACT,IAAI,IAAI,WAAW,UACnB,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,iBACvB,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,KACvB;AACA,kBAAQ,KAAK,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC;AACA,kBAAQ,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD;AAAA,QACF;AAEA,YAAI,OAAO,kBAAkB,OAAO,eAAe;AAEjD,cAAI,OAAO;AACX,iBAAO;AACP,iBAAO;AAEP,cAAI,OAAO;AACX,iBAAO;AACP,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,iBAAiB,OAAO,gBAAgB;AACjD,kBAAQ;AAAA,QACV;AAEA,YAAI,QAAQ,OAAO,QAAQ,OAAO,OAAO,MAAM;AAC7C,cAAI,WAAW,MAAM,SAAS,OAAO;AACrC,cAAI,MAAM,UAAU,QAAQ,IAAI,WAAW;AAC3C,kBAAQ,KAAK;AAAA,YACX,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,gBAAgB,MAAM;AAAA,YAC7C;AAAA,UACF,CAAC;AACD,oBAAU,CAAC;AACX,kBAAQ,KAAK;AAAA,YACX,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,gBAAgB,OAAO;AAAA,YAC9C;AAAA,UACF,CAAC;AACD,kBAAQ,KAAK,OAAO;AAAA,QACtB,OAAO;AACL,oBAAU,CAAC;AACX,kBAAQ,KAAK,OAAO;AAAA,QACtB;AACA,gBAAQ,KAAK,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,MACvC,OAAO;AACL,gBAAQ,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,EACF,OAAO;AAEL,QAAI,WAAW,CAAC;AAChB,YAAQ,KAAK,QAAQ;AACrB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,eAAS,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IACpD;AAAA,EACF;AAEA,MAAI,MAAM,IAAI,IAAI,KAAK,UAAU;AACjC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,QAAI,OAAO,IAAI,WAAW;AAC1B,QAAI,WAAW,KAAK,IAAI;AACxB,QAAI,SAAS,QAAQ,CAAC;AACtB,aAAS,KAAK,GAAG,KAAK,OAAO,QAAQ,EAAE,IAAI;AACzC,WAAK,QAAQ,OAAO,EAAE,CAAC;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;;;ADtRA,SAAS,YAAY,OAAO,KAAK,SAAS;AAExC,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,YAAY,SAAU,OAAM,IAAI,MAAM,oBAAoB;AACrE,MAAI,aAAa,QAAQ;AACzB,MAAI,UAAU,QAAQ;AACtB,MAAI,SAAS,QAAQ;AAErB,UAAQ,SAAS,KAAK;AACtB,QAAM,SAAS,GAAG;AAElB,eAAa,cAAc,CAAC;AAC5B,YAAU,WAAW;AAErB,MAAI,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG;AAC9C,UAAM,MAAM,MAAM,OAAO;AACzB,QAAI,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAC7B,WAAO,WAAW,KAAK,UAAU;AAAA,EACnC;AAEA,WAAS,UAAU;AAEnB,MAAI,YAAY,IAAI;AAAA,IAClB,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,IAC3B,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;AAAA,IACvB;AAAA,EACF;AAEA,MAAI,OAAO,UAAU,IAAI,SAAS,EAAE,OAAe,CAAC;AAEpD,SAAO,KAAK,KAAK;AACnB;AAGA,IAAO,4BAAQ;", "names": []}