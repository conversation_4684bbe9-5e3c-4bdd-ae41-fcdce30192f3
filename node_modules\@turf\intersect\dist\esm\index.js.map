{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  Feature,\n  GeoJsonProperties,\n  MultiPolygon,\n  Polygon,\n  FeatureCollection,\n} from \"geojson\";\nimport { multiPolygon, polygon } from \"@turf/helpers\";\nimport { geomEach } from \"@turf/meta\";\nimport * as polyclip from \"polyclip-ts\";\n\n/**\n * Takes {@link Polygon|polygon} or {@link MultiPolygon|multi-polygon} geometries and\n * finds their polygonal intersection. If they don't intersect, returns null.\n *\n * @function\n * @param {FeatureCollection<Polygon | MultiPolygon>} features the features to intersect\n * @param {Object} [options={}] Optional Parameters\n * @param {Object} [options.properties={}] Translate GeoJSON Properties to Feature\n * @returns {Feature|null} returns a feature representing the area they share (either a {@link Polygon} or\n * {@link MultiPolygon}). If they do not share any area, returns `null`.\n * @example\n * var poly1 = turf.polygon([[\n *   [-122.801742, 45.48565],\n *   [-122.801742, 45.60491],\n *   [-122.584762, 45.60491],\n *   [-122.584762, 45.48565],\n *   [-122.801742, 45.48565]\n * ]]);\n *\n * var poly2 = turf.polygon([[\n *   [-122.520217, 45.535693],\n *   [-122.64038, 45.553967],\n *   [-122.720031, 45.526554],\n *   [-122.669906, 45.507309],\n *   [-122.723464, 45.446643],\n *   [-122.532577, 45.408574],\n *   [-122.487258, 45.477466],\n *   [-122.520217, 45.535693]\n * ]]);\n *\n * var intersection = turf.intersect(turf.featureCollection([poly1, poly2]));\n *\n * //addToMap\n * var addToMap = [poly1, poly2, intersection];\n */\nfunction intersect<P extends GeoJsonProperties = GeoJsonProperties>(\n  features: FeatureCollection<Polygon | MultiPolygon>,\n  options: {\n    properties?: P;\n  } = {}\n): Feature<Polygon | MultiPolygon, P> | null {\n  const geoms: polyclip.Geom[] = [];\n\n  geomEach(features, (geom) => {\n    geoms.push(geom.coordinates as polyclip.Geom);\n  });\n\n  if (geoms.length < 2) {\n    throw new Error(\"Must specify at least 2 geometries\");\n  }\n  const intersection = polyclip.intersection(geoms[0], ...geoms.slice(1));\n  if (intersection.length === 0) return null;\n  if (intersection.length === 1)\n    return polygon(intersection[0], options.properties);\n  return multiPolygon(intersection, options.properties);\n}\n\nexport { intersect };\nexport default intersect;\n"], "mappings": ";AAOA,SAAS,cAAc,eAAe;AACtC,SAAS,gBAAgB;AACzB,YAAY,cAAc;AAqC1B,SAAS,UACP,UACA,UAEI,CAAC,GACsC;AAC3C,QAAM,QAAyB,CAAC;AAEhC,WAAS,UAAU,CAAC,SAAS;AAC3B,UAAM,KAAK,KAAK,WAA4B;AAAA,EAC9C,CAAC;AAED,MAAI,MAAM,SAAS,GAAG;AACpB,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACtD;AACA,QAAMA,gBAAwB,sBAAa,MAAM,CAAC,GAAG,GAAG,MAAM,MAAM,CAAC,CAAC;AACtE,MAAIA,cAAa,WAAW,EAAG,QAAO;AACtC,MAAIA,cAAa,WAAW;AAC1B,WAAO,QAAQA,cAAa,CAAC,GAAG,QAAQ,UAAU;AACpD,SAAO,aAAaA,eAAc,QAAQ,UAAU;AACtD;AAGA,IAAO,yBAAQ;", "names": ["intersection"]}