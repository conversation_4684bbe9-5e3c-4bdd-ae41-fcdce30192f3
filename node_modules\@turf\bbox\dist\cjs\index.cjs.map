{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-bbox/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACEA,kCAA0B;AAmB1B,SAAS,IAAA,CACP,OAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACC;AACN,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,GAAQ,KAAA,GAAQ,KAAA,IAAS,OAAA,CAAQ,SAAA,EAAW;AACtD,IAAA,OAAO,OAAA,CAAQ,IAAA;AAAA,EACjB;AACA,EAAA,MAAM,OAAA,EAAe,CAAC,QAAA,EAAU,QAAA,EAAU,CAAA,QAAA,EAAW,CAAA,QAAS,CAAA;AAC9D,EAAA,6BAAA,OAAU,EAAS,CAAC,KAAA,EAAA,GAAU;AAC5B,IAAA,GAAA,CAAI,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACxB,MAAA,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA;AAAA,IACrB;AACA,IAAA,GAAA,CAAI,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACxB,MAAA,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA;AAAA,IACrB;AACA,IAAA,GAAA,CAAI,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACxB,MAAA,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA;AAAA,IACrB;AACA,IAAA,GAAA,CAAI,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACxB,MAAA,MAAA,CAAO,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA;AAAA,IACrB;AAAA,EACF,CAAC,CAAA;AACD,EAAA,OAAO,MAAA;AACT;AAGA,IAAO,kBAAA,EAAQ,IAAA;ADzBf;AACE;AACA;AACF,yDAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-bbox/dist/cjs/index.cjs", "sourcesContent": [null, "import { BB<PERSON> } from \"geojson\";\nimport { AllGeoJSON } from \"@turf/helpers\";\nimport { coordEach } from \"@turf/meta\";\n\n/**\n * Calculates the bounding box for any GeoJSON object, including FeatureCollection.\n * Uses geojson.bbox if available and options.recompute is not set.\n *\n * @function\n * @param {GeoJSON} geojson any GeoJSON object\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.recompute] Whether to ignore an existing bbox property on geojson\n * @returns {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @example\n * var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]]);\n * var bbox = turf.bbox(line);\n * var bboxPolygon = turf.bboxPolygon(bbox);\n *\n * //addToMap\n * var addToMap = [line, bboxPolygon]\n */\nfunction bbox(\n  geojson: AllGeoJSON,\n  options: {\n    recompute?: boolean;\n  } = {}\n): BBox {\n  if (geojson.bbox != null && true !== options.recompute) {\n    return geojson.bbox;\n  }\n  const result: BBox = [Infinity, Infinity, -Infinity, -Infinity];\n  coordEach(geojson, (coord) => {\n    if (result[0] > coord[0]) {\n      result[0] = coord[0];\n    }\n    if (result[1] > coord[1]) {\n      result[1] = coord[1];\n    }\n    if (result[2] < coord[0]) {\n      result[2] = coord[0];\n    }\n    if (result[3] < coord[1]) {\n      result[3] = coord[1];\n    }\n  });\n  return result;\n}\n\nexport { bbox };\nexport default bbox;\n"]}