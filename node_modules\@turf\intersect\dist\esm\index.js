// index.ts
import { multiPolygon, polygon } from "@turf/helpers";
import { geomEach } from "@turf/meta";
import * as polyclip from "polyclip-ts";
function intersect(features, options = {}) {
  const geoms = [];
  geomEach(features, (geom) => {
    geoms.push(geom.coordinates);
  });
  if (geoms.length < 2) {
    throw new Error("Must specify at least 2 geometries");
  }
  const intersection2 = polyclip.intersection(geoms[0], ...geoms.slice(1));
  if (intersection2.length === 0) return null;
  if (intersection2.length === 1)
    return polygon(intersection2[0], options.properties);
  return multiPolygon(intersection2, options.properties);
}
var turf_intersect_default = intersect;
export {
  turf_intersect_default as default,
  intersect
};
//# sourceMappingURL=index.js.map