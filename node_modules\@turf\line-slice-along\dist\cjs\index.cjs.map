{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-slice-along/dist/cjs/index.cjs", "../../index.js"], "names": [], "mappings": "AAAA;ACAA,wCAAwB;AACxB,0CAAyB;AACzB,gDAA4B;AAC5B,wCAAqC;AAyBrC,SAAS,cAAA,CAAe,IAAA,EAAM,SAAA,EAAW,QAAA,EAAU,OAAA,EAAS;AAE1D,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAE5D,EAAA,IAAI,MAAA;AACJ,EAAA,IAAI,MAAA,EAAQ,CAAC,CAAA;AAGb,EAAA,GAAA,CAAI,IAAA,CAAK,KAAA,IAAS,SAAA,EAAW,OAAA,EAAS,IAAA,CAAK,QAAA,CAAS,WAAA;AAAA,EAAA,KAAA,GAAA,CAC3C,IAAA,CAAK,KAAA,IAAS,YAAA,EAAc,OAAA,EAAS,IAAA,CAAK,WAAA;AAAA,EAAA,KAC9C,MAAM,IAAI,KAAA,CAAM,gDAAgD,CAAA;AACrE,EAAA,IAAI,iBAAA,EAAmB,MAAA,CAAO,MAAA;AAC9B,EAAA,IAAI,UAAA,EAAY,CAAA;AAChB,EAAA,IAAI,QAAA,EAAU,SAAA,EAAW,YAAA;AACzB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,IAAA,GAAA,CAAI,UAAA,GAAa,UAAA,GAAa,EAAA,IAAM,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG,KAAA;AAAA,IAAA,KAAA,GAAA,CAC9C,UAAA,EAAY,UAAA,GAAa,KAAA,CAAM,OAAA,IAAW,CAAA,EAAG;AACpD,MAAA,SAAA,EAAW,UAAA,EAAY,SAAA;AACvB,MAAA,GAAA,CAAI,CAAC,QAAA,EAAU;AACb,QAAA,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AACpB,QAAA,OAAO,iCAAA,KAAgB,CAAA;AAAA,MACzB;AACA,MAAA,UAAA,EAAY,8BAAA,MAAQ,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,EAAA,EAAI,CAAC,CAAC,EAAA,EAAI,GAAA;AAChD,MAAA,aAAA,EAAe,sCAAA,MAAY,CAAO,CAAC,CAAA,EAAG,QAAA,EAAU,SAAA,EAAW,OAAO,CAAA;AAClE,MAAA,KAAA,CAAM,IAAA,CAAK,YAAA,CAAa,QAAA,CAAS,WAAW,CAAA;AAAA,IAC9C;AAEA,IAAA,GAAA,CAAI,UAAA,GAAa,QAAA,EAAU;AACzB,MAAA,SAAA,EAAW,SAAA,EAAW,SAAA;AACtB,MAAA,GAAA,CAAI,CAAC,QAAA,EAAU;AACb,QAAA,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AACpB,QAAA,OAAO,iCAAA,KAAgB,CAAA;AAAA,MACzB;AACA,MAAA,UAAA,EAAY,8BAAA,MAAQ,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,EAAA,EAAI,CAAC,CAAC,EAAA,EAAI,GAAA;AAChD,MAAA,aAAA,EAAe,sCAAA,MAAY,CAAO,CAAC,CAAA,EAAG,QAAA,EAAU,SAAA,EAAW,OAAO,CAAA;AAClE,MAAA,KAAA,CAAM,IAAA,CAAK,YAAA,CAAa,QAAA,CAAS,WAAW,CAAA;AAC5C,MAAA,OAAO,iCAAA,KAAgB,CAAA;AAAA,IACzB;AAEA,IAAA,GAAA,CAAI,UAAA,GAAa,SAAA,EAAW;AAC1B,MAAA,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,CAAA;AAAA,IACtB;AAEA,IAAA,GAAA,CAAI,EAAA,IAAM,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG;AAC3B,MAAA,OAAO,iCAAA,KAAgB,CAAA;AAAA,IACzB;AAEA,IAAA,UAAA,GAAa,gCAAA,MAAS,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,EAAA,EAAI,CAAC,CAAA,EAAG,OAAO,CAAA;AAAA,EACzD;AAEA,EAAA,GAAA,CAAI,UAAA,EAAY,UAAA,GAAa,MAAA,CAAO,OAAA,IAAW,gBAAA;AAC7C,IAAA,MAAM,IAAI,KAAA,CAAM,+BAA+B,CAAA;AAEjD,EAAA,IAAI,KAAA,EAAO,MAAA,CAAO,MAAA,CAAO,OAAA,EAAS,CAAC,CAAA;AACnC,EAAA,OAAO,iCAAA,CAAY,IAAA,EAAM,IAAI,CAAC,CAAA;AAChC;AAGA,IAAO,8BAAA,EAAQ,cAAA;ADlCf;AACE;AACA;AACF,yFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-slice-along/dist/cjs/index.cjs", "sourcesContent": [null, "import { bearing } from \"@turf/bearing\";\nimport { distance } from \"@turf/distance\";\nimport { destination } from \"@turf/destination\";\nimport { lineString, isObject } from \"@turf/helpers\";\n\n/**\n * Takes a {@link LineString|line}, a specified distance along the line to a start {@link Point},\n * and a specified  distance along the line to a stop point\n * and returns a subsection of the line in-between those points.\n *\n * This can be useful for extracting only the part of a route between two distances.\n *\n * @function\n * @param {Feature<LineString>|LineString} line input line\n * @param {number} startDist distance along the line to starting point\n * @param {number} stopDist distance along the line to ending point\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] can be degrees, radians, miles, or kilometers\n * @returns {Feature<LineString>} sliced line\n * @example\n * var line = turf.lineString([[7, 45], [9, 45], [14, 40], [14, 41]]);\n * var start = 12.5;\n * var stop = 25;\n * var sliced = turf.lineSliceAlong(line, start, stop, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [line, start, stop, sliced]\n */\nfunction lineSliceAlong(line, startDist, stopDist, options) {\n  // Optional parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n\n  var coords;\n  var slice = [];\n\n  // Validation\n  if (line.type === \"Feature\") coords = line.geometry.coordinates;\n  else if (line.type === \"LineString\") coords = line.coordinates;\n  else throw new Error(\"input must be a LineString Feature or Geometry\");\n  var origCoordsLength = coords.length;\n  var travelled = 0;\n  var overshot, direction, interpolated;\n  for (var i = 0; i < coords.length; i++) {\n    if (startDist >= travelled && i === coords.length - 1) break;\n    else if (travelled > startDist && slice.length === 0) {\n      overshot = startDist - travelled;\n      if (!overshot) {\n        slice.push(coords[i]);\n        return lineString(slice);\n      }\n      direction = bearing(coords[i], coords[i - 1]) - 180;\n      interpolated = destination(coords[i], overshot, direction, options);\n      slice.push(interpolated.geometry.coordinates);\n    }\n\n    if (travelled >= stopDist) {\n      overshot = stopDist - travelled;\n      if (!overshot) {\n        slice.push(coords[i]);\n        return lineString(slice);\n      }\n      direction = bearing(coords[i], coords[i - 1]) - 180;\n      interpolated = destination(coords[i], overshot, direction, options);\n      slice.push(interpolated.geometry.coordinates);\n      return lineString(slice);\n    }\n\n    if (travelled >= startDist) {\n      slice.push(coords[i]);\n    }\n\n    if (i === coords.length - 1) {\n      return lineString(slice);\n    }\n\n    travelled += distance(coords[i], coords[i + 1], options);\n  }\n\n  if (travelled < startDist && coords.length === origCoordsLength)\n    throw new Error(\"Start position is beyond line\");\n\n  var last = coords[coords.length - 1];\n  return lineString([last, last]);\n}\n\nexport { lineSliceAlong };\nexport default lineSliceAlong;\n"]}