# @turf/line-intersect

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineIntersect

Takes any LineString or Polygon GeoJSON and returns the intersecting point(s).

### Parameters

*   `line1` **[GeoJSON][1]** any LineString or Polygon
*   `line2` **[GeoJSON][1]** any LineString or Polygon
*   `options` **[Object][2]** Optional parameters (optional, default `{}`)

    *   `options.removeDuplicates` **[boolean][3]** remove duplicate intersections (optional, default `true`)
    *   `options.ignoreSelfIntersections` **[boolean][3]** ignores self-intersections on input features (optional, default `true`)

### Examples

```javascript
var line1 = turf.lineString([[126, -11], [129, -21]]);
var line2 = turf.lineString([[123, -18], [131, -14]]);
var intersects = turf.lineIntersect(line1, line2);

//addToMap
var addToMap = [line1, line2, intersects]
```

Returns **[FeatureCollection][4]<[Point][5]>** point(s) that intersect both

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[4]: https://tools.ietf.org/html/rfc7946#section-3.3

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/line-intersect
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
