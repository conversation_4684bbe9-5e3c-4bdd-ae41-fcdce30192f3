"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.js
var _meta = require('@turf/meta');
var _invariant = require('@turf/invariant');





var _helpers = require('@turf/helpers');

// lib/intersection.js
function ab(segment) {
  var start = segment[0];
  var end = segment[1];
  return [end[0] - start[0], end[1] - start[1]];
}
function crossProduct(v1, v2) {
  return v1[0] * v2[1] - v2[0] * v1[1];
}
function add(v1, v2) {
  return [v1[0] + v2[0], v1[1] + v2[1]];
}
function sub(v1, v2) {
  return [v1[0] - v2[0], v1[1] - v2[1]];
}
function scalarMult(s, v) {
  return [s * v[0], s * v[1]];
}
function intersectSegments(a, b) {
  var p = a[0];
  var r = ab(a);
  var q = b[0];
  var s = ab(b);
  var cross = crossProduct(r, s);
  var qmp = sub(q, p);
  var numerator = crossProduct(qmp, s);
  var t = numerator / cross;
  var intersection2 = add(p, scalarMult(t, r));
  return intersection2;
}
function isParallel(a, b) {
  var r = ab(a);
  var s = ab(b);
  return crossProduct(r, s) === 0;
}
function intersection(a, b) {
  if (isParallel(a, b)) return false;
  return intersectSegments(a, b);
}

// index.js
function lineOffset(geojson, distance, options) {
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  var units = options.units;
  if (!geojson) throw new Error("geojson is required");
  if (distance === void 0 || distance === null || isNaN(distance))
    throw new Error("distance is required");
  var type = _invariant.getType.call(void 0, geojson);
  var properties = geojson.properties;
  switch (type) {
    case "LineString":
      return lineOffsetFeature(geojson, distance, units);
    case "MultiLineString":
      var coords = [];
      _meta.flattenEach.call(void 0, geojson, function(feature) {
        coords.push(
          lineOffsetFeature(feature, distance, units).geometry.coordinates
        );
      });
      return _helpers.multiLineString.call(void 0, coords, properties);
    default:
      throw new Error("geometry " + type + " is not supported");
  }
}
function lineOffsetFeature(line, distance, units) {
  var segments = [];
  var offsetDegrees = _helpers.lengthToDegrees.call(void 0, distance, units);
  var coords = _invariant.getCoords.call(void 0, line);
  var finalCoords = [];
  coords.forEach(function(currentCoords, index) {
    if (index !== coords.length - 1) {
      var segment = processSegment(
        currentCoords,
        coords[index + 1],
        offsetDegrees
      );
      segments.push(segment);
      if (index > 0) {
        var seg2Coords = segments[index - 1];
        var intersects = intersection(segment, seg2Coords);
        if (intersects !== false) {
          seg2Coords[1] = intersects;
          segment[0] = intersects;
        }
        finalCoords.push(seg2Coords[0]);
        if (index === coords.length - 2) {
          finalCoords.push(segment[0]);
          finalCoords.push(segment[1]);
        }
      }
      if (coords.length === 2) {
        finalCoords.push(segment[0]);
        finalCoords.push(segment[1]);
      }
    }
  });
  return _helpers.lineString.call(void 0, finalCoords, line.properties);
}
function processSegment(point1, point2, offset) {
  var L = Math.sqrt(
    (point1[0] - point2[0]) * (point1[0] - point2[0]) + (point1[1] - point2[1]) * (point1[1] - point2[1])
  );
  var out1x = point1[0] + offset * (point2[1] - point1[1]) / L;
  var out2x = point2[0] + offset * (point2[1] - point1[1]) / L;
  var out1y = point1[1] + offset * (point1[0] - point2[0]) / L;
  var out2y = point2[1] + offset * (point1[0] - point2[0]) / L;
  return [
    [out1x, out1y],
    [out2x, out2y]
  ];
}
var turf_line_offset_default = lineOffset;



exports.default = turf_line_offset_default; exports.lineOffset = lineOffset;
//# sourceMappingURL=index.cjs.map