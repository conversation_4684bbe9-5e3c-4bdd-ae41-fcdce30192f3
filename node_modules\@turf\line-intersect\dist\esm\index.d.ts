import { LineString, MultiLineString, Polygon, MultiPolygon, FeatureCollection, Feature, Point } from 'geojson';

/**
 * Takes any LineString or Polygon GeoJSON and returns the intersecting point(s).
 *
 * @function
 * @param {GeoJSON} line1 any LineString or Polygon
 * @param {GeoJSON} line2 any LineString or Polygon
 * @param {Object} [options={}] Optional parameters
 * @param {boolean} [options.removeDuplicates=true] remove duplicate intersections
 * @param {boolean} [options.ignoreSelfIntersections=true] ignores self-intersections on input features
 * @returns {FeatureCollection<Point>} point(s) that intersect both
 * @example
 * var line1 = turf.lineString([[126, -11], [129, -21]]);
 * var line2 = turf.lineString([[123, -18], [131, -14]]);
 * var intersects = turf.lineIntersect(line1, line2);
 *
 * //addToMap
 * var addToMap = [line1, line2, intersects]
 */
declare function lineIntersect<G1 extends LineString | MultiLineString | Polygon | MultiPolygon, G2 extends LineString | MultiLineString | Polygon | MultiPolygon>(line1: FeatureCollection<G1> | Feature<G1> | G1, line2: FeatureCollection<G2> | Feature<G2> | G2, options?: {
    removeDuplicates?: boolean;
    ignoreSelfIntersections?: boolean;
}): FeatureCollection<Point>;

export { lineIntersect as default, lineIntersect };
