import { AllGeoJSON } from '@turf/helpers';
import { FeatureCollection, Point } from 'geojson';

/**
 * Takes a feature or set of features and returns all positions as {@link Point|points}.
 *
 * @function
 * @param {GeoJSON} geojson input features
 * @returns {FeatureCollection<point>} points representing the exploded input features
 * @throws {Error} if it encounters an unknown geometry type
 * @example
 * var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);
 *
 * var explode = turf.explode(polygon);
 *
 * //addToMap
 * var addToMap = [polygon, explode]
 */
declare function explode(geojson: AllGeoJSON): FeatureCollection<Point>;

export { explode as default, explode };
