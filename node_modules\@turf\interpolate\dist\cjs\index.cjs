"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.js
var _bbox = require('@turf/bbox');
var _hexgrid = require('@turf/hex-grid');
var _pointgrid = require('@turf/point-grid');
var _distance = require('@turf/distance');
var _centroid = require('@turf/centroid');
var _squaregrid = require('@turf/square-grid');
var _trianglegrid = require('@turf/triangle-grid');
var _clone = require('@turf/clone');
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
var _invariant = require('@turf/invariant');
function interpolate(points, cellSize, options) {
  options = options || {};
  if (typeof options !== "object") throw new Error("options is invalid");
  var gridType = options.gridType;
  var property = options.property;
  var weight = options.weight;
  var box = options.bbox;
  if (!points) throw new Error("points is required");
  _invariant.collectionOf.call(void 0, points, "Point", "input must contain Points");
  if (!cellSize) throw new Error("cellSize is required");
  if (weight !== void 0 && typeof weight !== "number")
    throw new Error("weight must be a number");
  property = property || "elevation";
  gridType = gridType || "square";
  weight = weight || 1;
  box = box != null ? box : _bbox.bbox.call(void 0, points);
  _helpers.validateBBox.call(void 0, box);
  var grid;
  switch (gridType) {
    case "point":
    case "points":
      grid = _pointgrid.pointGrid.call(void 0, box, cellSize, options);
      break;
    case "square":
    case "squares":
      grid = _squaregrid.squareGrid.call(void 0, box, cellSize, options);
      break;
    case "hex":
    case "hexes":
      grid = _hexgrid.hexGrid.call(void 0, box, cellSize, options);
      break;
    case "triangle":
    case "triangles":
      grid = _trianglegrid.triangleGrid.call(void 0, box, cellSize, options);
      break;
    default:
      throw new Error("invalid gridType");
  }
  var results = [];
  _meta.featureEach.call(void 0, grid, function(gridFeature) {
    var zw = 0;
    var sw = 0;
    _meta.featureEach.call(void 0, points, function(point) {
      var gridPoint = gridType === "point" ? gridFeature : _centroid.centroid.call(void 0, gridFeature);
      var d = _distance.distance.call(void 0, gridPoint, point, options);
      var zValue;
      if (property !== void 0) zValue = point.properties[property];
      if (zValue === void 0) zValue = point.geometry.coordinates[2];
      if (zValue === void 0) throw new Error("zValue is missing");
      if (d === 0) zw = zValue;
      var w = 1 / Math.pow(d, weight);
      sw += w;
      zw += w * zValue;
    });
    var newFeature = _clone.clone.call(void 0, gridFeature);
    newFeature.properties[property] = zw / sw;
    results.push(newFeature);
  });
  return _helpers.featureCollection.call(void 0, results);
}
var turf_interpolate_default = interpolate;



exports.default = turf_interpolate_default; exports.interpolate = interpolate;
//# sourceMappingURL=index.cjs.map