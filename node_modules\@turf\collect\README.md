# @turf/collect

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## collect

Merges a specified property from a FeatureCollection of points into a
FeatureCollection of polygons. Given an `inProperty` on points and an `outProperty`
for polygons, this finds every point that lies within each polygon, collects the
`inProperty` values from those points, and adds them as an array to `outProperty`
on the polygon.

### Parameters

*   `polygons` **[FeatureCollection][1]<[Polygon][2]>** polygons with values on which to aggregate
*   `points` **[FeatureCollection][1]<[Point][3]>** points to be aggregated
*   `inProperty` **[string][4]** property to be nested from
*   `outProperty` **[string][4]** property to be nested into

### Examples

```javascript
var poly1 = turf.polygon([[[0,0],[10,0],[10,10],[0,10],[0,0]]]);
var poly2 = turf.polygon([[[10,0],[20,10],[20,20],[20,0],[10,0]]]);
var polyFC = turf.featureCollection([poly1, poly2]);
var pt1 = turf.point([5,5], {population: 200});
var pt2 = turf.point([1,3], {population: 600});
var pt3 = turf.point([14,2], {population: 100});
var pt4 = turf.point([13,1], {population: 200});
var pt5 = turf.point([19,7], {population: 300});
var pointFC = turf.featureCollection([pt1, pt2, pt3, pt4, pt5]);
var collected = turf.collect(polyFC, pointFC, 'population', 'values');
var values = collected.features[0].properties.values
//=values => [200, 600]

//addToMap
var addToMap = [pointFC, collected]
```

Returns **[FeatureCollection][1]<[Polygon][2]>** polygons with properties listed based on `outField`

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/collect
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
