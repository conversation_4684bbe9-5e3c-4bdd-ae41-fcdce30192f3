# @turf/flatten

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## flatten

Flattens any [GeoJSON][1] to a [FeatureCollection][2] inspired by [geojson-flatten][3].

### Parameters

*   `geojson` **[GeoJSON][1]** any valid GeoJSON Object

### Examples

```javascript
var multiGeometry = turf.multiPolygon([
  [[[102.0, 2.0], [103.0, 2.0], [103.0, 3.0], [102.0, 3.0], [102.0, 2.0]]],
  [[[100.0, 0.0], [101.0, 0.0], [101.0, 1.0], [100.0, 1.0], [100.0, 0.0]],
  [[100.2, 0.2], [100.8, 0.2], [100.8, 0.8], [100.2, 0.8], [100.2, 0.2]]]
]);

var flatten = turf.flatten(multiGeometry);

//addToMap
var addToMap = [flatten]
```

Returns **[FeatureCollection][2]\<any>** all Multi-Geometries are flattened into single Features

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://github.com/tmcw/geojson-flatten

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/flatten
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
